{"metadata": {"version": "2.0", "last_updated": "2024-01-15T10:30:00Z", "description": "Enhanced PLC configuration for power system simulation"}, "connection": {"plc_ip": "*************", "timeout": 5.0, "retry_attempts": 3, "update_interval": 0.1, "enabled": true, "simulation_mode": false}, "data_management": {"buffer_size": 10000, "auto_export": true, "export_interval": 300, "export_format": "csv", "data_retention_days": 30}, "categories": {"generator": {"enabled": true, "update_frequency": 1, "priority": "high", "description": "Generator monitoring and control variables"}, "bus": {"enabled": true, "update_frequency": 1, "priority": "high", "description": "Bus system variables"}, "system": {"enabled": true, "update_frequency": 2, "priority": "medium", "description": "Overall system variables"}, "environment": {"enabled": true, "update_frequency": 5, "priority": "low", "description": "Environmental monitoring"}, "alarm": {"enabled": true, "update_frequency": 1, "priority": "critical", "description": "Safety and alarm systems"}, "control": {"enabled": true, "update_frequency": 1, "priority": "high", "description": "Control commands to PLC"}, "maintenance": {"enabled": true, "update_frequency": 10, "priority": "low", "description": "Maintenance and lifecycle data"}, "performance": {"enabled": true, "update_frequency": 2, "priority": "medium", "description": "Performance metrics and analysis"}}, "graphing": {"default_time_window": 300, "max_points_per_graph": 1000, "auto_scale": true, "grid_enabled": true, "legend_position": "below", "colors": {"generator": "#1f77b4", "bus": "#ff7f0e", "system": "#2ca02c", "environment": "#d62728", "alarm": "#ff0000", "control": "#9467bd", "maintenance": "#8c564b", "performance": "#e377c2"}}, "alarms": {"enabled": true, "audio_alerts": false, "email_notifications": false, "log_all_alarms": true, "critical_variables": ["ALM.I45FP1A", "ALM.I45FP1B", "ALM.I71GP1A", "ALM.I71GP1B", "ALM.ISYSOVLD", "ALM.ISYSUNBAL"]}, "simulation": {"noise_level": 0.02, "trend_enabled": true, "realistic_delays": true, "fault_injection": false, "custom_scenarios": []}, "dashboard": {"max_variables_displayed": 20, "refresh_rate": 1000, "auto_arrange": true, "show_statistics": true, "compact_mode": false}, "export": {"include_metadata": true, "timestamp_format": "ISO8601", "decimal_places": 3, "include_quality": true, "compression": false}, "security": {"read_only_mode": false, "require_confirmation": true, "log_all_writes": true, "backup_before_write": false}}