# Changelog - Sistema de Simulação Elétrica

Todas as mudanças notáveis neste projeto serão documentadas neste arquivo.

## [2.0.0] - 2024-01-15

### 🆕 Novidades Principais

#### Sistema de Configuração Unificado
- **NOVO**: Sistema de configuração modular com arquivos especializados
- **NOVO**: Migração automática de configurações antigas (`config.json`)
- **NOVO**: Validação robusta com relatórios de erro detalhados
- **NOVO**: Backup automático de configurações antigas

#### Gerenciamento de Dados Avançado
- **NOVO**: Organização automática por data em `simulation_data/`
- **NOVO**: Formato JSON + CSV + TXT para máxima compatibilidade
- **NOVO**: Salvamento completo para reprodutibilidade total
- **NOVO**: Sistema de limpeza automática (desabilitado por padrão)
- **NOVO**: Índice global de simulações

#### Preferências de Usuário
- **NOVO**: Sistema de preferências personalizáveis
- **NOVO**: Configurações de interface (tamanho janela, gráfico padrão)
- **NOVO**: Configurações de armazenamento (organização, limpeza)
- **NOVO**: Configurações de performance (frequência de atualização)

### 🔧 Melhorias na Interface

#### Simplificação de Controles
- **MELHORADO**: Seletor único de tipo de gráfico (removido seletor duplo)
- **MELHORADO**: Legenda posicionada abaixo dos gráficos
- **MELHORADO**: Interface mais limpa e intuitiva

#### Novos Recursos Visuais
- **NOVO**: Gráfico de "Balanço Potência" com fórmula [Geração - Carga]
- **MELHORADO**: Cores consistentes para cada tipo de gráfico
- **MELHORADO**: Eixos Y simétricos para balanço de potência

### 📁 Nova Estrutura de Arquivos

#### Configurações (config/)
```
config/
├── system_config.json          # Parâmetros do sistema elétrico
├── generators_config.json      # Configurações dos geradores  
├── user_preferences.json       # Preferências do usuário
├── default_scenarios.json      # Cenários pré-definidos
└── config.json.backup          # Backup da configuração antiga
```

#### Dados de Simulação (simulation_data/)
```
simulation_data/
├── index.json                  # Índice global
├── 2024-01-15/                 # Organização por data
│   ├── simulation_*_config.json
│   ├── simulation_*_data.csv
│   ├── simulation_*_summary.txt
│   └── daily_summary.txt
└── cleanup_log.txt
```

### 🔄 Migração e Compatibilidade

#### Migração Automática
- **AUTOMÁTICO**: Detecção de `config.json` antigo
- **AUTOMÁTICO**: Conversão para novo formato
- **AUTOMÁTICO**: Backup do arquivo original
- **AUTOMÁTICO**: Validação da migração

#### Retrocompatibilidade
- **MANTIDO**: Suporte a execução com `--legacy`
- **MANTIDO**: Funcionalidade existente preservada
- **MANTIDO**: Parâmetros técnicos inalterados

### 🛠️ Melhorias Técnicas

#### Arquitetura
- **NOVO**: Classe `ConfigurationManager` para gerenciamento unificado
- **NOVO**: Classe `DateBasedSimulationManager` para dados
- **MELHORADO**: Separação clara de responsabilidades
- **MELHORADO**: Código mais modular e manutenível

#### Validação e Segurança
- **NOVO**: Validação automática de configurações
- **NOVO**: Relatórios de erro detalhados
- **NOVO**: Verificação de integridade de dados
- **MELHORADO**: Tratamento de erros mais robusto

#### Performance
- **MELHORADO**: Carregamento mais rápido de configurações
- **MELHORADO**: Gerenciamento eficiente de memória
- **NOVO**: Configuração de frequência de atualização

### 📚 Documentação

#### Novos Documentos
- **NOVO**: [GUIA_RAPIDO.md](GUIA_RAPIDO.md) - Início rápido em 5 minutos
- **NOVO**: [CHANGELOG.md](CHANGELOG.md) - Este arquivo
- **NOVO**: [LICENSE](LICENSE) - Licença MIT com análise de compatibilidade
- **ATUALIZADO**: [README.md](README.md) - Documentação completa
- **ATUALIZADO**: [config_instructions.md](config_instructions.md) - Guia de configuração

#### Scripts de Teste
- **NOVO**: `test_config.py` - Teste do sistema de configuração
- **NOVO**: Opções de linha de comando para teste e debug

#### Licenciamento
- **NOVO**: Análise completa de compatibilidade de licenças das dependências
- **NOVO**: Licença MIT escolhida por compatibilidade total com todas as bibliotecas
- **NOVO**: Documentação detalhada sobre licenças de terceiros
- **NOVO**: Arquivo LICENSE com texto completo e informações de dependências

### 🐛 Correções

#### Interface
- **CORRIGIDO**: Problema com seletores duplos confusos
- **CORRIGIDO**: Posicionamento inconsistente da legenda
- **CORRIGIDO**: Problemas de layout em diferentes resoluções

#### Configuração
- **CORRIGIDO**: Perda de configurações entre sessões
- **CORRIGIDO**: Validação inconsistente de parâmetros
- **CORRIGIDO**: Problemas com caracteres especiais em caminhos

#### Dados
- **CORRIGIDO**: Problemas de salvamento em sistemas Windows
- **CORRIGIDO**: Inconsistências no formato de dados
- **CORRIGIDO**: Perda de metadados importantes

### ⚠️ Mudanças Importantes

#### Configuração
- **MUDANÇA**: `config.json` substituído por múltiplos arquivos especializados
- **MUDANÇA**: Estrutura de dados reorganizada para melhor organização
- **MIGRAÇÃO**: Automática e transparente para usuários existentes

#### Interface
- **MUDANÇA**: Seletor único de gráfico (removido seletor "Tipo gráfico")
- **MUDANÇA**: Legenda sempre posicionada abaixo dos gráficos
- **MELHORIA**: Interface mais limpa e intuitiva

#### Armazenamento
- **MUDANÇA**: Dados organizados por data em vez de arquivo único
- **MUDANÇA**: Formato expandido com configuração completa
- **SEGURANÇA**: Limpeza automática desabilitada por padrão

### 🔮 Próximas Versões

#### Planejado para v2.1
- [ ] Interface para carregamento de dados salvos
- [ ] Comparação entre múltiplas simulações
- [ ] Exportação de relatórios em PDF

#### Planejado para v2.2
- [ ] Interface gráfica para edição de configurações
- [ ] Cenários pré-definidos para ensino
- [ ] Análise estatística de resultados

---

## [1.0.0] - 2023-12-01

### Versão Inicial
- Sistema básico de simulação elétrica
- Interface gráfica com wxPython
- Configuração via `config.json`
- 4 geradores e 5 barramentos
- Controle de disjuntores
- Gráficos de potência, frequência, tensão e corrente
- Integração com PLC (opcional)

---

**Formato baseado em [Keep a Changelog](https://keepachangelog.com/)**
