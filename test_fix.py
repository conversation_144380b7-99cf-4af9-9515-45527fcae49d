import numpy as np
from core.constants import SystemConfig

# Test the SystemConfig class
config = SystemConfig()
print(f"SystemConfig created with t type: {type(config.t)}")
if isinstance(config.t, np.ndarray):
    print(f"t is an array with shape: {config.t.shape}, length: {len(config.t)}")
else:
    print(f"t is a scalar with value: {config.t}")

# Test accessing time values
try:
    if isinstance(config.t, np.ndarray):
        print(f"First time value: {config.t[0]}")
        print(f"Last time value: {config.t[-1]}")
    else:
        print(f"Using scalar time value: {config.t}")
    print("Time access successful!")
except Exception as e:
    print(f"Error accessing time: {e}")

print("\nTest completed successfully!")
