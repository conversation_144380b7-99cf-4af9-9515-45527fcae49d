# plc_integration.py
from plc_communication import PLCCommunication

def setup_plc_communication(simulation, plc_ip="*************", update_interval=0.1):
    """
    Configura e inicia a comunicação com o PLC.
    
    Args:
        simulation: Instância da classe Simulation
        plc_ip: Endereço IP do PLC
        update_interval: Intervalo de atualização em segundos (padrão: 100ms)
        
    Returns:
        PLCCommunication: Instância da classe de comunicação com o PLC
    """
    # Cria a instância de comunicação com o PLC
    plc_comm = PLCCommunication(
        simulation=simulation,
        plc_ip=plc_ip,
        tags_file="tags_plc_map.csv",
        update_interval=update_interval
    )
    
    # Inicia a comunicação
    plc_comm.start()
    
    return plc_comm
