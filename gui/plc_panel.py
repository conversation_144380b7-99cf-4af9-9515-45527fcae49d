#!/usr/bin/env python3
"""
PLC Panel for displaying real-time PLC data and graphs.
"""

import wx
import matplotlib
matplotlib.use('WXAgg')
from matplotlib.backends.backend_wxagg import FigureCanvasWxAgg
from matplotlib.figure import Figure
from matplotlib.dates import DateFormatter
import matplotlib.dates as mdates
from datetime import datetime, timedelta
from typing import Dict, List, Optional

# Simple logging function
def log_message(message: str, level: str = "info") -> None:
    """Log a message with the specified level."""
    print(f"[{level.upper()}] {message}")


class PLCVariablePanel(wx.Panel):
    """Panel for displaying a single PLC variable with real-time graph."""

    def __init__(self, parent, variable, plc_manager, show_controls=False):
        super().__init__(parent)
        self.variable = variable
        self.plc_manager = plc_manager
        self.show_controls = show_controls  # Show controls for write variables

        self._create_ui()
        if not show_controls:  # Only show plots for read variables
            self._setup_plot()
    
    def _create_ui(self):
        """Create the user interface."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Header with variable info
        header_panel = wx.Panel(self)
        header_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # Variable name and description
        name_label = wx.StaticText(header_panel, label=f"{self.variable.tag}")
        name_label.SetFont(wx.Font(10, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        
        desc_label = wx.StaticText(header_panel, label=f"{self.variable.description}")
        desc_label.SetFont(wx.Font(8, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_ITALIC, wx.FONTWEIGHT_NORMAL))
        
        # Current value
        self.value_label = wx.StaticText(header_panel, label="-- --")
        self.value_label.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        self.value_label.SetForegroundColour(wx.Colour(0, 100, 0))
        
        # Unit
        unit_label = wx.StaticText(header_panel, label=self.variable.unit)
        unit_label.SetFont(wx.Font(10, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
        
        header_sizer.Add(name_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)
        header_sizer.Add(desc_label, 1, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)
        header_sizer.AddStretchSpacer()
        header_sizer.Add(self.value_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)
        header_sizer.Add(unit_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)
        
        header_panel.SetSizer(header_sizer)
        
        # Content area - either plot or controls
        if self.show_controls:
            # Create controls for write variables
            self._create_controls(main_sizer)
        else:
            # Create plot for read variables
            self.figure = Figure(figsize=(6, 2), dpi=80)
            self.canvas = FigureCanvasWxAgg(self, -1, self.figure)
            main_sizer.Add(self.canvas, 1, wx.EXPAND | wx.ALL, 5)

        main_sizer.Add(header_panel, 0, wx.EXPAND | wx.ALL, 5)
        self.SetSizer(main_sizer)

    def _create_controls(self, main_sizer):
        """Create control widgets for write variables."""
        controls_panel = wx.Panel(self)
        controls_sizer = wx.BoxSizer(wx.HORIZONTAL)

        if self.variable.data_type == 'BOOL':
            # Boolean control - checkbox or toggle button
            self.control_widget = wx.CheckBox(controls_panel, label="Enable")
            self.control_widget.Bind(wx.EVT_CHECKBOX, self._on_bool_changed)

        elif self.variable.data_type == 'REAL':
            # Real number control - spin control with decimal places
            min_val = self.variable.min_value if self.variable.min_value is not None else -1000.0
            max_val = self.variable.max_value if self.variable.max_value is not None else 1000.0

            self.control_widget = wx.SpinCtrlDouble(
                controls_panel,
                value=str((min_val + max_val) / 2),  # Default to middle value
                min=min_val,
                max=max_val,
                inc=0.1
            )
            self.control_widget.SetDigits(2)
            self.control_widget.Bind(wx.EVT_SPINCTRLDOUBLE, self._on_real_changed)

        else:
            # Text control for other types
            self.control_widget = wx.TextCtrl(controls_panel, value="0")
            self.control_widget.Bind(wx.EVT_TEXT, self._on_text_changed)

        # Send button
        send_btn = wx.Button(controls_panel, label="Send to PLC")
        send_btn.Bind(wx.EVT_BUTTON, self._on_send_to_plc)

        # Status indicator
        self.status_indicator = wx.StaticText(controls_panel, label="Ready")
        self.status_indicator.SetForegroundColour(wx.Colour(100, 100, 100))

        controls_sizer.Add(wx.StaticText(controls_panel, label="Value:"), 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)
        controls_sizer.Add(self.control_widget, 1, wx.EXPAND | wx.ALL, 5)
        controls_sizer.Add(send_btn, 0, wx.ALL, 5)
        controls_sizer.Add(self.status_indicator, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)

        controls_panel.SetSizer(controls_sizer)
        main_sizer.Add(controls_panel, 0, wx.EXPAND | wx.ALL, 5)

    def _on_bool_changed(self, event):
        """Handle boolean control change."""
        self.status_indicator.SetLabel("Modified")
        self.status_indicator.SetForegroundColour(wx.Colour(200, 100, 0))

    def _on_real_changed(self, event):
        """Handle real number control change."""
        self.status_indicator.SetLabel("Modified")
        self.status_indicator.SetForegroundColour(wx.Colour(200, 100, 0))

    def _on_text_changed(self, event):
        """Handle text control change."""
        self.status_indicator.SetLabel("Modified")
        self.status_indicator.SetForegroundColour(wx.Colour(200, 100, 0))

    def _on_send_to_plc(self, event):
        """Handle send to PLC button."""
        try:
            # Get value from control
            if self.variable.data_type == 'BOOL':
                value = self.control_widget.GetValue()
            elif self.variable.data_type == 'REAL':
                value = self.control_widget.GetValue()
            else:
                value = self.control_widget.GetValue()

            # TODO: Implement actual PLC write
            log_message(f"Sending {self.variable.tag} = {value} to PLC", level="info")

            self.status_indicator.SetLabel("Sent")
            self.status_indicator.SetForegroundColour(wx.Colour(0, 150, 0))

            # Reset status after 2 seconds
            wx.CallLater(2000, self._reset_status)

        except Exception as e:
            log_message(f"Error sending {self.variable.tag} to PLC: {e}", level="error")
            self.status_indicator.SetLabel("Error")
            self.status_indicator.SetForegroundColour(wx.Colour(200, 0, 0))

    def _reset_status(self):
        """Reset status indicator."""
        self.status_indicator.SetLabel("Ready")
        self.status_indicator.SetForegroundColour(wx.Colour(100, 100, 100))

    def _setup_plot(self):
        """Setup the matplotlib plot."""
        self.ax = self.figure.add_subplot(111)
        self.line, = self.ax.plot([], [], 'b-', linewidth=1.5)
        
        self.ax.set_title(f"{self.variable.tag}", fontsize=10)
        self.ax.set_ylabel(f"{self.variable.unit}", fontsize=8)
        self.ax.grid(True, alpha=0.3)
        
        # Format x-axis for time - optimized for real-time data
        self.ax.xaxis.set_major_formatter(DateFormatter('%H:%M:%S'))
        self.ax.xaxis.set_minor_formatter(DateFormatter('%M:%S'))

        # Use conservative locators initially to avoid tick overflow
        self.ax.xaxis.set_major_locator(mdates.SecondLocator(interval=30))  # Conservative start
        self.ax.xaxis.set_minor_locator(mdates.SecondLocator(interval=10))

        # Rotate labels for better readability
        self.figure.autofmt_xdate(rotation=45)

        # Configure Y-axis locators to prevent tick overflow
        from matplotlib.ticker import MaxNLocator, NullLocator

        # Use very conservative tick settings
        self.ax.yaxis.set_major_locator(MaxNLocator(nbins=5, prune='both'))  # Very conservative
        self.ax.yaxis.set_minor_locator(NullLocator())  # Disable minor ticks completely

        # Also disable automatic tick locators
        self.ax.xaxis.set_minor_locator(NullLocator())  # Disable X minor ticks too

        # Set initial limits
        now = datetime.now()
        start_time = now - timedelta(minutes=2)  # Shorter window for real-time
        self.ax.set_xlim(float(mdates.date2num(start_time)), float(mdates.date2num(now)))

        if self.variable.data_type == 'REAL':
            if 'MW' in self.variable.tag:
                self.ax.set_ylim(0, 30)
            elif 'Hz' in self.variable.tag:
                self.ax.set_ylim(59, 61)
            elif 'kV' in self.variable.tag:
                self.ax.set_ylim(13, 14.5)
            else:
                self.ax.set_ylim(-10, 10)
        else:
            self.ax.set_ylim(-0.1, 1.1)

        self.figure.tight_layout()
    
    def update(self):
        """Update the panel with latest data."""
        try:
            # Get recent data - limit points to avoid tick overflow
            max_points = 200  # Limit to 200 points to avoid matplotlib warnings
            timestamps, values = self.plc_manager.data_buffer.get_data_for_plotting(
                self.variable.tag, count=max_points
            )
            
            if timestamps and values:
                # Update current value display
                current_value = values[-1]
                if self.variable.data_type == 'BOOL':
                    self.value_label.SetLabel("ON" if current_value else "OFF")
                    self.value_label.SetForegroundColour(wx.Colour(0, 150, 0) if current_value else wx.Colour(150, 0, 0))
                else:
                    self.value_label.SetLabel(f"{current_value:.2f}")
                    self.value_label.SetForegroundColour(wx.Colour(0, 100, 0))
                
                # Convert timestamps to matplotlib date numbers for plotting
                date_nums = [mdates.date2num(ts) for ts in timestamps]

                # Update plot
                self.line.set_data(date_nums, values)

                # Update x-axis limits to show recent data
                if len(date_nums) > 1:
                    time_span = timestamps[-1] - timestamps[0]
                    if time_span.total_seconds() > 0:
                        self.ax.set_xlim(float(date_nums[0]), float(date_nums[-1]))

                        # Adjust locator based on time span to avoid too many ticks
                        duration_seconds = time_span.total_seconds()

                        # Simplified X-axis configuration to avoid tick overflow
                        from matplotlib.ticker import NullLocator

                        if duration_seconds < 30:  # Less than 30 seconds
                            major_interval = max(5, int(duration_seconds / 6))  # ~6 major ticks max
                            self.ax.xaxis.set_major_locator(mdates.SecondLocator(interval=major_interval))

                        elif duration_seconds < 120:  # Less than 2 minutes
                            major_interval = max(10, int(duration_seconds / 8))  # ~8 major ticks max
                            self.ax.xaxis.set_major_locator(mdates.SecondLocator(interval=major_interval))

                        elif duration_seconds < 600:  # Less than 10 minutes
                            major_interval = max(30, int(duration_seconds / 10))  # ~10 major ticks max
                            self.ax.xaxis.set_major_locator(mdates.SecondLocator(interval=major_interval))

                        else:  # 10+ minutes - use minute locators
                            major_interval = max(1, int(duration_seconds / 600))  # ~10 major ticks in minutes
                            self.ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=major_interval))

                        # Disable ALL minor ticks to prevent overflow
                        self.ax.xaxis.set_minor_locator(NullLocator())
                
                # Auto-scale y-axis if needed with tick control
                if values and self.variable.data_type == 'REAL':
                    y_min, y_max = min(values), max(values)
                    y_range = y_max - y_min

                    # Ensure minimum range to avoid too many ticks
                    min_range = 0.1  # Minimum range for REAL values
                    if y_range < min_range:
                        center = (y_min + y_max) / 2
                        y_min = center - min_range / 2
                        y_max = center + min_range / 2
                        y_range = min_range

                    if y_range > 0:
                        margin = max(y_range * 0.1, 0.05)  # Minimum margin
                        new_y_min = y_min - margin
                        new_y_max = y_max + margin

                        # Set limits
                        self.ax.set_ylim(new_y_min, new_y_max)

                        # Control Y-axis ticks to avoid overflow - very conservative
                        from matplotlib.ticker import MaxNLocator, NullLocator
                        self.ax.yaxis.set_major_locator(MaxNLocator(nbins=5, prune='both'))  # Very conservative
                        self.ax.yaxis.set_minor_locator(NullLocator())  # Disable minor ticks
                
                self.canvas.draw()
            else:
                # No data available
                self.value_label.SetLabel("No Data")
                self.value_label.SetForegroundColour(wx.Colour(150, 150, 150))
                
        except Exception as e:
            log_message(f"Error updating PLC panel for {self.variable.tag}: {e}", level="error")


class PLCReadDataPanel(wx.Panel):
    """Panel for displaying PLC READ data (data coming FROM PLC)."""

    def __init__(self, parent, plc_manager):
        super().__init__(parent)
        self.plc_manager = plc_manager
        self.variable_panels: Dict[str, PLCVariablePanel] = {}
        self.update_timer = None

        self._create_ui()
        self._setup_timer()
    
    def _create_ui(self):
        """Create the user interface."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Title
        title = wx.StaticText(self, label="PLC Read Data (FROM PLC)")
        title.SetFont(wx.Font(14, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        title.SetForegroundColour(wx.Colour(0, 100, 0))  # Green for read data

        # Category selection
        category_panel = wx.Panel(self)
        category_sizer = wx.BoxSizer(wx.HORIZONTAL)

        category_label = wx.StaticText(category_panel, label="Category:")
        self.category_choice = wx.Choice(category_panel, choices=[
            "All", "Generator", "Bus", "System", "Environment", "Alarm", "Maintenance", "Performance"
        ])
        self.category_choice.SetSelection(0)
        self.category_choice.Bind(wx.EVT_CHOICE, self._on_category_changed)
        
        # Statistics
        self.stats_label = wx.StaticText(category_panel, label="")
        self.stats_label.SetFont(wx.Font(8, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_ITALIC, wx.FONTWEIGHT_NORMAL))
        
        category_sizer.Add(category_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)
        category_sizer.Add(self.category_choice, 0, wx.ALL, 5)
        category_sizer.AddStretchSpacer()
        category_sizer.Add(self.stats_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)
        
        category_panel.SetSizer(category_sizer)
        
        # Scrolled panel for variable displays
        self.scrolled_panel = wx.ScrolledWindow(self)
        self.scrolled_panel.SetScrollRate(5, 5)
        
        self.variables_sizer = wx.BoxSizer(wx.VERTICAL)
        self.scrolled_panel.SetSizer(self.variables_sizer)
        
        main_sizer.Add(title, 0, wx.ALIGN_CENTER | wx.ALL, 10)
        main_sizer.Add(category_panel, 0, wx.EXPAND | wx.ALL, 5)
        main_sizer.Add(self.scrolled_panel, 1, wx.EXPAND | wx.ALL, 5)
        
        self.SetSizer(main_sizer)
        
        # Initial population
        self._populate_variables()
    
    def _populate_variables(self):
        """Populate the panel with PLC variables."""
        # Clear existing panels
        for panel in self.variable_panels.values():
            panel.Destroy()
        self.variable_panels.clear()
        self.variables_sizer.Clear()
        
        # Get selected category
        category = self.category_choice.GetStringSelection().lower()
        
        # Get variables to display - ONLY READ variables (FROM PLC)
        if category == "all":
            variables = self.plc_manager.get_plottable_variables()
            variables = [v for v in variables if v.direction == 'LEITURA']  # Only read variables
        else:
            variables = self.plc_manager.get_variables_by_category(category)
            variables = [v for v in variables if v.data_type == 'REAL' and v.direction == 'LEITURA']  # Only plottable read variables
        
        # Create panels for each variable
        for variable in variables[:10]:  # Limit to 10 for performance
            panel = PLCVariablePanel(self.scrolled_panel, variable, self.plc_manager)
            self.variable_panels[variable.tag] = panel
            self.variables_sizer.Add(panel, 0, wx.EXPAND | wx.ALL, 5)
        
        self.scrolled_panel.Layout()
        self.scrolled_panel.FitInside()
        
        log_message(f"Created {len(self.variable_panels)} PLC variable panels", level="info")
    
    def _on_category_changed(self, event):
        """Handle category selection change."""
        self._populate_variables()
    
    def _setup_timer(self):
        """Setup update timer."""
        self.update_timer = wx.Timer(self)
        self.Bind(wx.EVT_TIMER, self._on_timer_update, self.update_timer)
        self.update_timer.Start(1000)  # Update every second
    
    def _on_timer_update(self, event):
        """Handle timer update."""
        try:
            # Update all variable panels
            for panel in self.variable_panels.values():
                panel.update()
            
            # Update statistics
            stats = self.plc_manager.get_statistics()
            if stats['last_update']:
                time_str = stats['last_update'].strftime('%H:%M:%S')
                success_rate = 0
                if stats['total_reads'] > 0:
                    success_rate = (stats['successful_reads'] / stats['total_reads']) * 100
                
                stats_text = f"Last Update: {time_str} | Success Rate: {success_rate:.1f}% | Errors: {stats['connection_errors']}"
                self.stats_label.SetLabel(stats_text)
            
        except Exception as e:
            log_message(f"Error in PLC dashboard timer update: {e}", level="error")
    
    def cleanup(self):
        """Cleanup resources."""
        if self.update_timer:
            self.update_timer.Stop()
            self.update_timer = None


class PLCWriteDataPanel(wx.Panel):
    """Panel for displaying PLC WRITE data (data going TO PLC)."""

    def __init__(self, parent, plc_manager):
        super().__init__(parent)
        self.plc_manager = plc_manager
        self.variable_panels: Dict[str, PLCVariablePanel] = {}
        self.update_timer = None

        self._create_ui()
        self._setup_timer()

    def _create_ui(self):
        """Create the user interface."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)

        # Title
        title = wx.StaticText(self, label="PLC Write Data (TO PLC)")
        title.SetFont(wx.Font(14, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        title.SetForegroundColour(wx.Colour(150, 0, 0))  # Red for write data

        # Category selection
        category_panel = wx.Panel(self)
        category_sizer = wx.BoxSizer(wx.HORIZONTAL)

        category_label = wx.StaticText(category_panel, label="Category:")
        self.category_choice = wx.Choice(category_panel, choices=[
            "All", "Control", "Generator", "Bus"
        ])
        self.category_choice.SetSelection(0)
        self.category_choice.Bind(wx.EVT_CHOICE, self._on_category_changed)

        # Statistics
        self.stats_label = wx.StaticText(category_panel, label="")
        self.stats_label.SetFont(wx.Font(8, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_ITALIC, wx.FONTWEIGHT_NORMAL))

        category_sizer.Add(category_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)
        category_sizer.Add(self.category_choice, 0, wx.ALL, 5)
        category_sizer.AddStretchSpacer()
        category_sizer.Add(self.stats_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)

        category_panel.SetSizer(category_sizer)

        # Scrolled panel for variable displays
        self.scrolled_panel = wx.ScrolledWindow(self)
        self.scrolled_panel.SetScrollRate(5, 5)

        self.variables_sizer = wx.BoxSizer(wx.VERTICAL)
        self.scrolled_panel.SetSizer(self.variables_sizer)

        main_sizer.Add(title, 0, wx.ALIGN_CENTER | wx.ALL, 10)
        main_sizer.Add(category_panel, 0, wx.EXPAND | wx.ALL, 5)
        main_sizer.Add(self.scrolled_panel, 1, wx.EXPAND | wx.ALL, 5)

        self.SetSizer(main_sizer)

        # Initial population
        self._populate_variables()

    def _populate_variables(self):
        """Populate the panel with PLC variables."""
        # Clear existing panels
        for panel in self.variable_panels.values():
            panel.Destroy()
        self.variable_panels.clear()
        self.variables_sizer.Clear()

        # Get selected category
        category = self.category_choice.GetStringSelection().lower()

        # Get variables to display - ONLY WRITE variables (TO PLC)
        if category == "all":
            variables = [v for v in self.plc_manager.variables.values()
                        if v.direction == 'ESCRITA' and v.enabled]
        else:
            variables = self.plc_manager.get_variables_by_category(category)
            variables = [v for v in variables if v.direction == 'ESCRITA' and v.enabled]

        # Create panels for each variable with controls
        for variable in variables[:10]:  # Limit to 10 for performance
            panel = PLCVariablePanel(self.scrolled_panel, variable, self.plc_manager, show_controls=True)
            self.variable_panels[variable.tag] = panel
            self.variables_sizer.Add(panel, 0, wx.EXPAND | wx.ALL, 5)

        self.scrolled_panel.Layout()
        self.scrolled_panel.FitInside()

        log_message(f"Created {len(self.variable_panels)} PLC write variable panels", level="info")

    def _on_category_changed(self, event):
        """Handle category selection change."""
        self._populate_variables()

    def _setup_timer(self):
        """Setup update timer."""
        self.update_timer = wx.Timer(self)
        self.Bind(wx.EVT_TIMER, self._on_timer_update, self.update_timer)
        self.update_timer.Start(1000)  # Update every second

    def _on_timer_update(self, event):
        """Handle timer update."""
        try:
            # Update all variable panels
            for panel in self.variable_panels.values():
                panel.update()

            # Update statistics
            stats = self.plc_manager.get_statistics()
            if stats['last_update']:
                time_str = stats['last_update'].strftime('%H:%M:%S')
                write_rate = 0
                if stats['total_writes'] > 0:
                    write_rate = (stats['successful_writes'] / stats['total_writes']) * 100

                stats_text = f"Last Update: {time_str} | Write Success Rate: {write_rate:.1f}% | Total Writes: {stats['total_writes']}"
                self.stats_label.SetLabel(stats_text)

        except Exception as e:
            log_message(f"Error in PLC write dashboard timer update: {e}", level="error")

    def cleanup(self):
        """Cleanup resources."""
        if self.update_timer:
            self.update_timer.Stop()
            self.update_timer = None


class PLCConfigPanel(wx.Panel):
    """Panel for PLC configuration."""
    
    def __init__(self, parent, plc_manager):
        super().__init__(parent)
        self.plc_manager = plc_manager
        
        self._create_ui()
    
    def _create_ui(self):
        """Create the user interface."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Title
        title = wx.StaticText(self, label="PLC Configuration")
        title.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        
        # Connection settings
        conn_box = wx.StaticBox(self, label="Connection Settings")
        conn_sizer = wx.StaticBoxSizer(conn_box, wx.VERTICAL)
        
        # IP Address
        ip_panel = wx.Panel(self)
        ip_sizer = wx.BoxSizer(wx.HORIZONTAL)
        ip_label = wx.StaticText(ip_panel, label="PLC IP Address:")
        self.ip_text = wx.TextCtrl(ip_panel, value=self.plc_manager.plc_ip)
        ip_sizer.Add(ip_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)
        ip_sizer.Add(self.ip_text, 1, wx.ALL, 5)
        ip_panel.SetSizer(ip_sizer)
        
        # Update interval
        interval_panel = wx.Panel(self)
        interval_sizer = wx.BoxSizer(wx.HORIZONTAL)
        interval_label = wx.StaticText(interval_panel, label="Update Interval (ms):")
        self.interval_spin = wx.SpinCtrl(interval_panel, value=str(int(self.plc_manager.update_interval * 1000)),
                                        min=50, max=5000)
        interval_sizer.Add(interval_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)
        interval_sizer.Add(self.interval_spin, 0, wx.ALL, 5)
        interval_panel.SetSizer(interval_sizer)
        
        # Buttons
        button_panel = wx.Panel(self)
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        self.connect_btn = wx.Button(button_panel, label="Connect")
        self.disconnect_btn = wx.Button(button_panel, label="Disconnect")
        self.test_btn = wx.Button(button_panel, label="Test Connection")
        
        self.connect_btn.Bind(wx.EVT_BUTTON, self._on_connect)
        self.disconnect_btn.Bind(wx.EVT_BUTTON, self._on_disconnect)
        self.test_btn.Bind(wx.EVT_BUTTON, self._on_test)
        
        button_sizer.Add(self.connect_btn, 0, wx.ALL, 5)
        button_sizer.Add(self.disconnect_btn, 0, wx.ALL, 5)
        button_sizer.Add(self.test_btn, 0, wx.ALL, 5)
        button_panel.SetSizer(button_sizer)
        
        # Status
        self.status_label = wx.StaticText(self, label="Status: Disconnected")
        self.status_label.SetForegroundColour(wx.Colour(150, 0, 0))
        
        conn_sizer.Add(ip_panel, 0, wx.EXPAND | wx.ALL, 5)
        conn_sizer.Add(interval_panel, 0, wx.EXPAND | wx.ALL, 5)
        conn_sizer.Add(button_panel, 0, wx.EXPAND | wx.ALL, 5)
        
        main_sizer.Add(title, 0, wx.ALIGN_CENTER | wx.ALL, 10)
        main_sizer.Add(conn_sizer, 0, wx.EXPAND | wx.ALL, 10)
        main_sizer.Add(self.status_label, 0, wx.ALL, 10)
        
        self.SetSizer(main_sizer)
        
        # Update initial status
        self._update_status()
    
    def _on_connect(self, event):
        """Handle connect button."""
        self.plc_manager.plc_ip = self.ip_text.GetValue()
        self.plc_manager.update_interval = self.interval_spin.GetValue() / 1000.0
        self.plc_manager.start()
        self._update_status()
    
    def _on_disconnect(self, event):
        """Handle disconnect button."""
        self.plc_manager.stop()
        self._update_status()
    
    def _on_test(self, event):
        """Handle test connection button."""
        # Implementation for testing connection
        wx.MessageBox("Test connection functionality to be implemented", "Test Connection", wx.OK | wx.ICON_INFORMATION)
    
    def _update_status(self):
        """Update connection status display."""
        if self.plc_manager.running:
            self.status_label.SetLabel("Status: Connected")
            self.status_label.SetForegroundColour(wx.Colour(0, 150, 0))
            self.connect_btn.Enable(False)
            self.disconnect_btn.Enable(True)
        else:
            self.status_label.SetLabel("Status: Disconnected")
            self.status_label.SetForegroundColour(wx.Colour(150, 0, 0))
            self.connect_btn.Enable(True)
            self.disconnect_btn.Enable(False)
