"""
Módulo de diálogos da interface gráfica.

Este módulo contém todas as classes de diálogo usadas na interface,
incluindo configuração de turbina, geradores, disjuntores e valores atuais.
"""

import wx
import json
import os
import logging
from typing import Dict, Any, List

def log_message(message: str, level: str = "info") -> None:
    """Registra uma mensagem no log e imprime no console."""
    print(message)
    if level.lower() == "info":
        logging.info(message)
    elif level.lower() == "warning":
        logging.warning(message)
    elif level.lower() == "error":
        logging.error(message)

class ConfigDialog(wx.Dialog):
    """Diálogo para configurar parâmetros da turbina."""

    def __init__(self, parent, generators, bus):
        super(ConfigDialog, self).__init__(parent, title="Configuração Governador e Turbina", size=(550, 500))
        self.generators = generators
        self.bus = bus
        log_message("ConfigDialog inicializado", level="info")

        # Create main panel with scroll
        panel = wx.ScrolledWindow(self)
        panel.SetScrollbars(20, 20, 50, 50)
        panel.SetBackgroundColour(wx.BLACK)

        main_vbox = wx.BoxSizer(wx.VERTICAL)

        # Add title
        title = wx.StaticText(panel, label="CONFIGURAÇÃO GOVERNADOR E TURBINA")
        title.SetFont(wx.Font(16, wx.FONTFAMILY_SWISS, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        title.SetForegroundColour(wx.WHITE)
        main_vbox.Add(title, 0, wx.ALL | wx.ALIGN_CENTER, 10)

        # Add generator selector and parameters
        gen_box = self._create_generator_section(panel)
        main_vbox.Add(gen_box, 0, wx.EXPAND | wx.ALL, 10)

        # Add save button
        save_btn = wx.Button(panel, label="SALVAR", size=(200, 40))
        save_btn.SetBackgroundColour(wx.Colour(0, 80, 0))
        save_btn.SetForegroundColour(wx.Colour(0, 255, 0))
        save_btn.Bind(wx.EVT_BUTTON, self.on_save)
        main_vbox.Add(save_btn, 0, wx.ALL | wx.ALIGN_CENTER, 10)

        panel.SetSizer(main_vbox)

        sizer = wx.BoxSizer(wx.VERTICAL)
        sizer.Add(panel, 1, wx.EXPAND)
        self.SetSizer(sizer)

        # Inicializar campos com valores do primeiro gerador
        self.gen_choice.Bind(wx.EVT_CHOICE, self.on_generator_select)
        self.update_fields()

    def _create_generator_section(self, parent):
        """Creates the generator parameters section."""
        box = wx.StaticBox(parent, label="PARÂMETROS GOVERNADOR E TURBINA")
        box.SetForegroundColour(wx.Colour(0, 255, 0))
        box.SetBackgroundColour(wx.Colour(20, 20, 20))
        sizer = wx.StaticBoxSizer(box, wx.VERTICAL)

        # Generator selector
        sel_sizer = wx.BoxSizer(wx.HORIZONTAL)
        sel_label = wx.StaticText(parent, label="Gerador:")
        sel_label.SetForegroundColour(wx.WHITE)
        sel_sizer.Add(sel_label, 0, wx.ALL | wx.ALIGN_CENTER_VERTICAL, 5)

        gen_names = [gen.name for gen in self.generators] + ["All"]
        self.gen_choice = wx.Choice(parent, choices=gen_names)
        self.gen_choice.SetSelection(0)
        self.gen_choice.SetBackgroundColour(wx.Colour(40, 40, 40))
        self.gen_choice.SetForegroundColour(wx.Colour(0, 255, 0))
        sel_sizer.Add(self.gen_choice, 1, wx.ALL, 5)
        sizer.Add(sel_sizer, 0, wx.EXPAND | wx.ALL, 5)

        # Parameter fields - apenas parâmetros específicos da turbina
        self.gen_inputs = {}
        params = [
            ("K_pgov", "Ganho Proporcional (P)"),
            ("K_igov", "Ganho Integral (I)"),
            ("K_dgov", "Ganho Derivativo (D)"),
            ("K_turb", "Ganho da Turbina"),
            ("T_turb", "Constante de Tempo Turbina (s)"),
            ("W_fnl", "Vazão Combustível em Vazio"),
            ("disturb_start", "Início Perturbação (s)"),
            ("disturb_end", "Fim Perturbação (s)"),
            ("disturb_value", "Valor Perturbação (pu)")
        ]

        for param, label in params:
            h_sizer = wx.BoxSizer(wx.HORIZONTAL)

            text = wx.StaticText(parent, label=label)
            text.SetForegroundColour(wx.WHITE)
            h_sizer.Add(text, 0, wx.ALL | wx.ALIGN_CENTER_VERTICAL, 5)

            ctrl = wx.TextCtrl(parent, style=wx.TE_RIGHT)
            ctrl.SetBackgroundColour(wx.Colour(40, 40, 40))
            ctrl.SetForegroundColour(wx.Colour(0, 255, 0))
            h_sizer.Add(ctrl, 1, wx.ALL | wx.EXPAND, 5)

            self.gen_inputs[param] = ctrl
            sizer.Add(h_sizer, 0, wx.EXPAND | wx.ALL, 2)

        return sizer

    def update_fields(self):
        """Atualiza os campos com os valores do gerador selecionado."""
        selected_gen = self.gen_choice.GetString(self.gen_choice.GetSelection())

        # Se "All" estiver selecionado, use o primeiro gerador
        if selected_gen == "All" and self.generators:
            gen = self.generators[0]
        else:
            # Encontre o gerador pelo nome
            gen = next((g for g in self.generators if g.name == selected_gen), None)

        if gen:
            # Preencha os campos com os valores do gerador
            for param, ctrl in self.gen_inputs.items():
                value = getattr(gen, param, 0.0)
                ctrl.SetValue(str(value))

    def on_generator_select(self, event):
        """Manipula a seleção de gerador."""
        self.update_fields()

    def on_save(self, event):
        """Handles save button clicks."""
        try:
            selected_gen = self.gen_choice.GetString(self.gen_choice.GetSelection())

            # Handle save for all generators or single generator
            gens = self.generators if selected_gen == "All" else [
                g for g in self.generators if g.name == selected_gen
            ]

            # Update parameters
            for gen in gens:
                for param, ctrl in self.gen_inputs.items():
                    value = float(ctrl.GetValue())
                    setattr(gen, param, value)

            # Save to config file
            self._save_config_to_file()

            self.EndModal(wx.ID_OK)

        except ValueError as e:
            wx.MessageBox(f"Erro: Insira valores numéricos válidos!\n{e}",
                        "Erro", wx.OK | wx.ICON_ERROR)

    def _save_config_to_file(self):
        """Saves configuration to file."""
        config = {
            "generators": [{
                "name": gen.name,
                "mode": gen.mode,
                "P_setpoint": gen.P_setpoint,
                "H": gen.H,
                "damping_factor": gen.damping_factor,
                "K_pgov": gen.K_pgov,
                "K_igov": gen.K_igov,
                "K_dgov": gen.K_dgov,
                "K_turb": gen.K_turb,
                "T_turb": gen.T_turb,
                "W_fnl": gen.W_fnl,
                "disturb_start": gen.disturb_start,
                "disturb_end": gen.disturb_end,
                "disturb_value": gen.disturb_value
            } for gen in self.generators]
        }

        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)),
                                "config.json")
        with open(config_path, "w") as f:
            json.dump(config, f, indent=4)

class BreakerConfigDialog(wx.Dialog):
    """Diálogo para configurar disjuntores."""

    def __init__(self, parent, breaker_status):
        super().__init__(parent, title="Configurar Disjuntores", size=(400, 400))
        self.breaker_status = breaker_status.copy()

        panel = wx.Panel(self)
        panel.SetBackgroundColour(wx.BLACK)

        sizer = wx.BoxSizer(wx.VERTICAL)

        # Title
        title = wx.StaticText(panel, label="ESTADO DISJUNTORES")
        title.SetFont(wx.Font(14, wx.FONTFAMILY_SWISS, wx.FONTSTYLE_NORMAL,
                            wx.FONTWEIGHT_BOLD))
        title.SetForegroundColour(wx.Colour(0, 255, 0))
        sizer.Add(title, 0, wx.ALL | wx.ALIGN_CENTER, 10)

        # Breaker toggles
        self.breaker_toggles = {}
        self.breaker_indicators = {}

        grid = wx.GridBagSizer(5, 5)

        for i, label in enumerate(['CB1', 'CB2', 'CB3', 'CB4',
                                'CB5', 'CB6', 'CB7', 'CB8']):
            # Add toggle button
            toggle = wx.ToggleButton(panel, label=label)
            toggle.SetValue(self.breaker_status[i])
            toggle.Bind(wx.EVT_TOGGLEBUTTON, self.on_toggle)
            grid.Add(toggle, pos=(i, 0), flag=wx.ALL, border=5)

            # Add indicator
            indicator = wx.StaticText(panel,
                                    label="FECHADO" if self.breaker_status[i] else "ABERTO")
            indicator.SetForegroundColour(wx.RED if self.breaker_status[i] else wx.GREEN)
            grid.Add(indicator, pos=(i, 1), flag=wx.ALL | wx.ALIGN_CENTER_VERTICAL,
                    border=5)

            self.breaker_toggles[label] = toggle
            self.breaker_indicators[label] = indicator

        sizer.Add(grid, 1, wx.ALL | wx.EXPAND, 10)

        # Add save button
        save_btn = wx.Button(panel, label="Salvar")
        save_btn.Bind(wx.EVT_BUTTON, self.on_save)
        sizer.Add(save_btn, 0, wx.ALL | wx.ALIGN_CENTER, 10)

        panel.SetSizer(sizer)

        # Set main sizer
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        main_sizer.Add(panel, 1, wx.EXPAND)
        self.SetSizer(main_sizer)

    def on_toggle(self, event):
        """Handle breaker toggle button events."""
        toggle = event.GetEventObject()
        label = toggle.GetLabel()
        value = toggle.GetValue()

        # Update indicator
        self.breaker_indicators[label].SetLabel("FECHADO" if value else "ABERTO")
        self.breaker_indicators[label].SetForegroundColour(wx.RED if value else wx.GREEN)

        # Update breaker status
        idx = int(label[2]) - 1  # Get breaker number from label
        self.breaker_status[idx] = value

    def on_save(self, event):
        """Save breaker configuration."""
        # Update breaker status from toggle states
        for label, toggle in self.breaker_toggles.items():
            idx = int(label[2]) - 1
            self.breaker_status[idx] = toggle.GetValue()

        # Save to config file
        self._save_config_to_file()
        self.EndModal(wx.ID_OK)

    def _save_config_to_file(self):
        """Save breaker status to config file."""
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)),
                                "config.json")
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            config = {}

        config['breaker_status'] = self.breaker_status

        with open(config_path, 'w') as f:
            json.dump(config, f, indent=4)

class GeneratorConfigDialog(wx.Dialog):
    """Diálogo para configurar parâmetros dos geradores."""

    def __init__(self, parent, generators):
        super().__init__(parent, title="Configuração dos Parâmetros do Gerador", size=(500, 600))
        self.generators = generators
        self.modified_params = {}

        panel = wx.Panel(self)
        panel.SetBackgroundColour(wx.BLACK)

        sizer = wx.BoxSizer(wx.VERTICAL)

        # Add generator selector
        sel_sizer = wx.BoxSizer(wx.HORIZONTAL)
        sel_label = wx.StaticText(panel, label="Gerador:")
        sel_label.SetForegroundColour(wx.WHITE)
        sel_sizer.Add(sel_label, 0, wx.ALL | wx.ALIGN_CENTER_VERTICAL, 5)

        gen_names = [gen.name for gen in generators]
        self.gen_choice = wx.Choice(panel, choices=gen_names)
        self.gen_choice.SetSelection(0)
        self.gen_choice.Bind(wx.EVT_CHOICE, self.on_generator_select)
        sel_sizer.Add(self.gen_choice, 1, wx.ALL, 5)

        sizer.Add(sel_sizer, 0, wx.EXPAND | wx.ALL, 5)

        # Adicionar equação de oscilação para explicar o papel do fator de amortecimento
        equation_box = wx.StaticBox(panel, label="Equação de Oscilação")
        equation_box.SetForegroundColour(wx.WHITE)
        equation_box.SetBackgroundColour(wx.Colour(40, 40, 40))
        equation_sizer = wx.StaticBoxSizer(equation_box, wx.VERTICAL)

        # Equação principal
        equation_text = wx.StaticText(panel, label="dω/dt = (P_mech - P_elec) / (2H)")
        equation_text.SetForegroundColour(wx.Colour(255, 255, 0))  # Amarelo para destaque
        equation_text.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        equation_sizer.Add(equation_text, 0, wx.ALL | wx.ALIGN_CENTER, 5)

        # Equação com amortecimento
        damping_eq = wx.StaticText(panel, label="Com amortecimento: dω/dt = (P_mech - P_elec - D × Δω) / (2H)")
        damping_eq.SetForegroundColour(wx.Colour(255, 255, 0))  # Amarelo para destaque
        equation_sizer.Add(damping_eq, 0, wx.ALL | wx.ALIGN_CENTER, 5)

        # Adicionar legenda da equação
        legend_text = (
            "Onde:\n"
            "dω/dt = taxa de variação da velocidade angular\n"
            "P_mech = potência mecânica da turbina\n"
            "P_elec = potência elétrica do gerador\n"
            "D = fator de amortecimento (damping factor)\n"
            "Δω = desvio de velocidade (ω - ω_nominal)\n"
            "H = constante de inércia do conjunto turbina-gerador"
        )
        legend = wx.StaticText(panel, label=legend_text)
        legend.SetForegroundColour(wx.WHITE)
        equation_sizer.Add(legend, 0, wx.ALL, 10)

        sizer.Add(equation_sizer, 0, wx.EXPAND | wx.ALL, 10)

        # Add parameter inputs - parâmetros específicos do gerador e AVR
        self.param_ctrls = {}
        params = [
            # Parâmetros básicos
            ("mode", "Modo Operação"),
            ("P_setpoint", "Referência Potência (pu)"),

            # Parâmetros do conjunto turbina-gerador
            ("H", "Constante de Inércia (s)"),
            ("damping_factor", "Fator de Amortecimento"),

            # Parâmetros do AVR (controle de tensão)
            ("K_pavr", "AVR - Ganho Proporcional (P)"),
            ("K_iavr", "AVR - Ganho Integral (I)"),
            ("K_davr", "AVR - Ganho Derivativo (D)"),
            ("T_avr", "AVR - Constante de Tempo (s)")
        ]

        param_grid = wx.FlexGridSizer(rows=len(params), cols=2, vgap=5, hgap=5)
        param_grid.AddGrowableCol(1, proportion=1)

        for param, label in params:
            text = wx.StaticText(panel, label=label)
            text.SetForegroundColour(wx.WHITE)
            param_grid.Add(text, 0, wx.ALIGN_RIGHT | wx.ALIGN_CENTER_VERTICAL)

            if param == "mode":
                # Modo é uma escolha entre "synchronous" e "droop"
                ctrl = wx.Choice(panel, choices=["synchronous", "droop"])
                ctrl.SetBackgroundColour(wx.Colour(40, 40, 40))
                ctrl.SetForegroundColour(wx.Colour(0, 255, 0))
            else:
                # Outros parâmetros são campos de texto
                ctrl = wx.TextCtrl(panel)
                ctrl.SetBackgroundColour(wx.Colour(40, 40, 40))
                ctrl.SetForegroundColour(wx.Colour(0, 255, 0))

            param_grid.Add(ctrl, 0, wx.EXPAND)
            self.param_ctrls[param] = ctrl

        sizer.Add(param_grid, 1, wx.EXPAND | wx.ALL, 10)

        # Add save button
        save_btn = wx.Button(panel, label="Salvar")
        save_btn.Bind(wx.EVT_BUTTON, self.on_save)
        sizer.Add(save_btn, 0, wx.ALL | wx.ALIGN_CENTER, 10)

        panel.SetSizer(sizer)

        # Set main sizer
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        main_sizer.Add(panel, 1, wx.EXPAND)
        self.SetSizer(main_sizer)

        # Initialize with first generator's values
        self.update_fields()

    def update_fields(self):
        """Update input fields with current generator values."""
        gen = self.generators[self.gen_choice.GetSelection()]
        for param, ctrl in self.param_ctrls.items():
            value = getattr(gen, param, '')
            if param == "mode":
                # Para o modo, selecionar o item correto na lista
                if value == "synchronous":
                    ctrl.SetSelection(0)
                else:
                    ctrl.SetSelection(1)
            else:
                # Para outros parâmetros, definir o valor como texto
                ctrl.SetValue(str(value))

    def on_generator_select(self, event):
        """Handle generator selection changes."""
        self.update_fields()

    def on_save(self, event):
        """Save generator configuration."""
        try:
            gen_idx = self.gen_choice.GetSelection()
            gen = self.generators[gen_idx]

            # Collect modified parameters
            modified = {}
            for param, ctrl in self.param_ctrls.items():
                if param == 'mode':
                    # Para o modo, obter a seleção atual
                    new_val = ctrl.GetString(ctrl.GetSelection())
                    old_val = getattr(gen, param, '')

                    if new_val != old_val:
                        modified[param] = new_val
                else:
                    # Para outros parâmetros, obter o valor como texto
                    new_val = ctrl.GetValue()
                    old_val = str(getattr(gen, param, ''))

                    if new_val != old_val:
                        modified[param] = float(new_val)

            if modified:
                self.modified_params[gen.name] = modified

            self.EndModal(wx.ID_OK)

        except ValueError as e:
            wx.MessageBox(f"Erro: Valores inválidos!\n{e}",
                        "Erro", wx.OK | wx.ICON_ERROR)

    def get_modified_parameters(self):
        """Return modified parameters."""
        return self.modified_params

class CurrentValuesDialog(wx.Dialog):
    """Diálogo para exibir valores atuais."""

    def __init__(self, parent, generator):
        super().__init__(parent, title="Valores Atuais", size=(400, 500))
        self.generator = generator

        panel = wx.Panel(self)
        panel.SetBackgroundColour(wx.BLACK)

        sizer = wx.BoxSizer(wx.VERTICAL)

        # Title
        title = wx.StaticText(panel, label=f"Valores Atuais - Gerador {generator.name}")
        title.SetFont(wx.Font(14, wx.FONTFAMILY_SWISS, wx.FONTSTYLE_NORMAL,
                            wx.FONTWEIGHT_BOLD))
        title.SetForegroundColour(wx.Colour(0, 255, 0))
        sizer.Add(title, 0, wx.ALL | wx.ALIGN_CENTER, 10)

        # Values grid
        grid = wx.FlexGridSizer(rows=0, cols=2, vgap=5, hgap=10)

        # Add current values
        values = [
            ("Potência", f"{generator.P_elec[-1]:.4f} pu"),
            ("Frequência", f"{generator.omega[-1]:.4f} pu"),
            ("Tensão", f"{generator.V_t[-1]:.4f} pu"),
            # ...add other values...
        ]

        for label, value in values:
            lbl = wx.StaticText(panel, label=label)
            lbl.SetForegroundColour(wx.WHITE)
            grid.Add(lbl, 0, wx.ALIGN_RIGHT | wx.ALIGN_CENTER_VERTICAL)

            val = wx.StaticText(panel, label=value)
            val.SetForegroundColour(wx.Colour(0, 255, 0))
            grid.Add(val, 0, wx.EXPAND)

        sizer.Add(grid, 1, wx.ALL | wx.EXPAND, 10)

        # Close button
        close_btn = wx.Button(panel, label="Fechar")
        close_btn.Bind(wx.EVT_BUTTON, lambda evt: self.Close())
        sizer.Add(close_btn, 0, wx.ALL | wx.ALIGN_CENTER, 10)

        panel.SetSizer(sizer)

        # Set main sizer
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        main_sizer.Add(panel, 1, wx.EXPAND)
        self.SetSizer(main_sizer)

class LoadControlDialog(wx.Dialog):
    """Dialog for controlling bus loads."""

    def __init__(self, parent, system, simulation):
        super(LoadControlDialog, self).__init__(
            parent, title="Controle Carga Barramento", size=(400, 300),
            style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER
        )

        self.system = system
        self.simulation = simulation

        # Set background color
        self.SetBackgroundColour(wx.Colour(45, 55, 72))

        # Create main sizer
        main_sizer = wx.BoxSizer(wx.VERTICAL)

        # Add title
        title = wx.StaticText(self, label="Controle Carga Barramento")
        title.SetFont(wx.Font(14, wx.FONTFAMILY_SWISS, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        title.SetForegroundColour(wx.Colour(255, 255, 255))  # White text
        main_sizer.Add(title, 0, wx.ALL | wx.ALIGN_CENTER, 10)

        # Add description
        description = wx.StaticText(self, label="Insira valores carga para cada barramento (MW)")
        description.SetForegroundColour(wx.Colour(200, 200, 200))  # Light gray text
        main_sizer.Add(description, 0, wx.ALL | wx.ALIGN_CENTER, 5)

        # Create grid for load inputs
        grid = wx.FlexGridSizer(rows=len(system.secondary_buses), cols=2, vgap=10, hgap=10)
        grid.AddGrowableCol(1, 1)  # Make input column expandable

        # Store references to input controls
        self.load_inputs = {}

        for i, bus in enumerate(system.secondary_buses):
            bus_id = chr(65 + i)  # A, B, C, D

            # Bus label
            label = wx.StaticText(self, label=f"Barramento {bus_id} Carga:")
            label.SetFont(wx.Font(10, wx.FONTFAMILY_SWISS, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
            label.SetForegroundColour(wx.Colour(180, 210, 255))  # Light blue

            # Get current load value in MW
            current_load_mw = bus.load_p / 1e6

            # Create text input
            load_input = wx.TextCtrl(self, value=f"{current_load_mw:.2f}")
            load_input.SetBackgroundColour(wx.Colour(60, 70, 90))  # Dark gray background
            load_input.SetForegroundColour(wx.Colour(255, 255, 255))  # White text

            grid.Add(label, 0, wx.ALIGN_RIGHT | wx.ALIGN_CENTER_VERTICAL)
            grid.Add(load_input, 0, wx.EXPAND)

            self.load_inputs[bus_id] = load_input

        main_sizer.Add(grid, 0, wx.ALL | wx.EXPAND, 20)

        # Add quick load change buttons
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)

        # Zero All button
        zero_btn = wx.Button(self, label="Zerar Todos")
        zero_btn.SetBackgroundColour(wx.Colour(60, 70, 90))
        zero_btn.SetForegroundColour(wx.WHITE)
        zero_btn.Bind(wx.EVT_BUTTON, self._on_zero_all)
        button_sizer.Add(zero_btn, 0, wx.RIGHT, 10)

        # Reset Default button
        reset_btn = wx.Button(self, label="Restaurar Padrão")
        reset_btn.SetBackgroundColour(wx.Colour(60, 70, 90))
        reset_btn.SetForegroundColour(wx.WHITE)
        reset_btn.Bind(wx.EVT_BUTTON, self._on_reset_default)
        button_sizer.Add(reset_btn, 0, wx.RIGHT, 10)

        # Match Generator Setpoints button
        match_btn = wx.Button(self, label="Igualar Referências")
        match_btn.SetBackgroundColour(wx.Colour(60, 70, 90))
        match_btn.SetForegroundColour(wx.WHITE)
        match_btn.Bind(wx.EVT_BUTTON, self._on_match_setpoints)
        button_sizer.Add(match_btn, 0)

        main_sizer.Add(button_sizer, 0, wx.ALL | wx.ALIGN_CENTER, 10)

        # Add OK/Cancel buttons
        btn_sizer = wx.StdDialogButtonSizer()
        ok_btn = wx.Button(self, wx.ID_OK, "Aplicar")
        cancel_btn = wx.Button(self, wx.ID_CANCEL, "Cancelar")
        btn_sizer.AddButton(ok_btn)
        btn_sizer.AddButton(cancel_btn)
        btn_sizer.Realize()
        main_sizer.Add(btn_sizer, 0, wx.ALL | wx.ALIGN_CENTER, 10)

        self.SetSizer(main_sizer)

    def _on_zero_all(self, event):
        """Set all loads to zero."""
        for input_ctrl in self.load_inputs.values():
            input_ctrl.SetValue("0.00")

    def _on_reset_default(self, event):
        """Reset to default load values from config."""
        if hasattr(self.system.config, 'loads'):
            for bus_id, input_ctrl in self.load_inputs.items():
                if bus_id in self.system.config.loads:
                    load_mw = self.system.config.loads[bus_id]['P'] / 1e6
                    input_ctrl.SetValue(f"{load_mw:.2f}")

    def _on_match_setpoints(self, event):
        """Match loads to generator setpoints."""
        for i, (bus_id, input_ctrl) in enumerate(self.load_inputs.items()):
            if i < len(self.system.generators):
                gen = self.system.generators[i]
                setpoint_mw = gen.P_setpoint * (self.system.config.s_base / 1e6)
                input_ctrl.SetValue(f"{setpoint_mw:.2f}")

    def apply_changes(self):
        """Apply load changes to buses."""
        for i, bus in enumerate(self.system.secondary_buses):
            bus_id = chr(65 + i)
            try:
                load_mw = float(self.load_inputs[bus_id].GetValue())
                bus.load_p = load_mw * 1e6  # Convert MW to W
            except ValueError as e:
                wx.MessageBox(
                    f"Valor de carga inválido para Barramento {bus_id}",
                    "Erro",
                    wx.OK | wx.ICON_ERROR
                )
                return False

        self.simulation.request_ui_update()
        return True
