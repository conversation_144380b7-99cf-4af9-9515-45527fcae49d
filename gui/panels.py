"""
<PERSON><PERSON><PERSON><PERSON> de painéis da interface gráfica.

Este módulo contém todas as classes de painel usadas na interface,
incluindo controle de carga, informações do gerador, parâmetros da turbina,
controle do gerador e indicadores de cargas.
"""

# Standard library imports
import wx
import wx.lib.scrolledpanel as scrolled

# Check wxPython version
WX_VERSION = tuple(map(int, wx.version().split('.')[:2]))

# Define font style based on wx version
if WX_VERSION >= (4, 1):
    FONT_STYLE_NORMAL = wx.FONTSTYLE_NORMAL
else:
    FONT_STYLE_NORMAL = wx.NORMAL

# Matplotlib imports
import matplotlib.pyplot as plt
from matplotlib.figure import Figure  # This was missing

# Local imports
from gui.dialogs import log_message

class LoadControlPanel(scrolled.ScrolledPanel):
    """Panel for controlling bus loads."""
    def __init__(self, parent, system, simulation):
        super(LoadControlPanel, self).__init__(parent, style=wx.BORDER_SUNKEN)
        self.system = system
        self.simulation = simulation

        # Setup panel
        self.SetBackgroundColour(wx.BLACK)
        self.SetupScrolling()

        # Create UI elements
        self._init_ui()

    def _init_ui(self):
        """Initialize the user interface."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)

        # Title
        title = wx.StaticText(self, label="CONTROLE DE CARGAS")
        title.SetForegroundColour(wx.WHITE)
        title.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        main_sizer.Add(title, 0, wx.ALL | wx.CENTER, 5)

        # Create controls for each bus
        for i, bus in enumerate(self.system.secondary_buses):
            bus_panel = self._create_bus_panel(bus, i)
            main_sizer.Add(bus_panel, 0, wx.EXPAND | wx.ALL, 5)

        self.SetSizer(main_sizer)
        self.Layout()

    def _create_bus_panel(self, bus, index):
        """Create a panel for controlling a single bus load."""
        panel = wx.Panel(self)
        panel.SetBackgroundColour(wx.Colour(40, 40, 40))

        sizer = wx.BoxSizer(wx.VERTICAL)

        # Bus title
        bus_label = wx.StaticText(panel, label=f"Barramento {chr(65 + index)}")
        bus_label.SetForegroundColour(wx.WHITE)
        bus_label.SetFont(wx.Font(10, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        sizer.Add(bus_label, 0, wx.ALL, 5)

        # Load controls
        grid = wx.FlexGridSizer(2, 2, 5, 5)

        # Active power control
        p_label = wx.StaticText(panel, label="Potência Ativa (MW):")
        p_label.SetForegroundColour(wx.WHITE)
        p_ctrl = wx.SpinCtrlDouble(panel, value="0.0", min=0, max=100, inc=0.1, size=(100, -1))
        p_ctrl.SetValue(bus.load_p / 1e6)  # Convert to MW

        # Reactive power control
        q_label = wx.StaticText(panel, label="Potência Reativa (MVAR):")
        q_label.SetForegroundColour(wx.WHITE)
        q_ctrl = wx.SpinCtrlDouble(panel, value="0.0", min=0, max=100, inc=0.1, size=(100, -1))
        q_ctrl.SetValue(bus.load_q / 1e6)  # Convert to MVAR

        # Add to grid
        grid.AddMany([
            (p_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 5),
            (p_ctrl, 0, wx.EXPAND),
            (q_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 5),
            (q_ctrl, 0, wx.EXPAND)
        ])

        sizer.Add(grid, 0, wx.ALL | wx.EXPAND, 5)

        # Store references to controls
        bus.p_ctrl = p_ctrl
        bus.q_ctrl = q_ctrl

        # Bind events
        p_ctrl.Bind(wx.EVT_SPINCTRLDOUBLE, lambda evt: self._on_load_change(evt, bus, 'p'))
        q_ctrl.Bind(wx.EVT_SPINCTRLDOUBLE, lambda evt: self._on_load_change(evt, bus, 'q'))

        panel.SetSizer(sizer)
        return panel

    def _on_load_change(self, event, bus, load_type):
        """Handle load change events."""
        try:
            value = event.GetValue() * 1e6  # Convert from MW/MVAR to W/VAR
            if load_type == 'p':
                bus.load_p = value
                log_message(f"Potência ativa alterada para {value/1e6:.2f} MW", level="info")
            else:
                bus.load_q = value
                log_message(f"Potência reativa alterada para {value/1e6:.2f} MVAR", level="info")

            # Update simulation if needed
            if hasattr(self.simulation, 'update_network'):
                self.simulation.update_network()

        except Exception as e:
            log_message(f"Erro ao alterar carga: {e}", level="error")
            event.Skip()

    def update_values(self):
        """Update displayed values from bus objects."""
        for bus in self.system.secondary_buses:
            if hasattr(bus, 'p_ctrl'):
                bus.p_ctrl.SetValue(bus.load_p / 1e6)
            if hasattr(bus, 'q_ctrl'):
                bus.q_ctrl.SetValue(bus.load_q / 1e6)

class GeneratorInfoPanel(wx.Panel):
    """Panel for displaying generator information."""
    def __init__(self, parent, generators, simulation):
        super(GeneratorInfoPanel, self).__init__(parent, style=wx.BORDER_SUNKEN)
        self.generators = generators
        self.simulation = simulation
        self.SetBackgroundColour(wx.BLACK)
        self._init_ui()

    def _init_ui(self):
        """Initialize UI components."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)

        # Create title
        title = wx.StaticText(self, label="INFORMAÇÕES DOS GERADORES")
        title.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        title.SetForegroundColour(wx.WHITE)
        main_sizer.Add(title, 0, wx.ALL | wx.CENTER, 5)

        # Create a scrolled panel for generator info
        self.scrolled_panel = wx.ScrolledWindow(self)
        self.scrolled_panel.SetBackgroundColour(wx.BLACK)
        self.scrolled_panel.SetScrollRate(5, 5)

        # Create a sizer for the scrolled panel
        scroll_sizer = wx.BoxSizer(wx.VERTICAL)

        # Create info panels for each generator
        for i, gen in enumerate(self.generators):
            panel = self._create_generator_info_panel(gen, i)
            scroll_sizer.Add(panel, 0, wx.EXPAND | wx.ALL, 5)

        self.scrolled_panel.SetSizer(scroll_sizer)
        main_sizer.Add(self.scrolled_panel, 1, wx.EXPAND | wx.ALL, 5)

        self.SetSizer(main_sizer)

    def _create_generator_info_panel(self, generator, idx):
        """Create an info panel for a single generator."""
        panel = wx.Panel(self.scrolled_panel)
        panel.SetBackgroundColour(wx.Colour(40, 40, 40))
        sizer = wx.BoxSizer(wx.VERTICAL)

        # Generator title
        title = wx.StaticText(panel, label=f"Gerador {generator.name}")
        title.SetFont(wx.Font(10, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        title.SetForegroundColour(wx.WHITE)
        sizer.Add(title, 0, wx.ALL, 5)

        # Create grid for generator info
        grid = wx.FlexGridSizer(rows=5, cols=2, vgap=5, hgap=10)
        grid.AddGrowableCol(1)

        # Info fields
        info_fields = [
            ("Modo", "mode", lambda v: "Síncrono" if v == "synchronous" else "Droop"),
            ("Potência (MW)", "P_elec", lambda v: f"{safe_convert(v) * self.simulation.config.s_base / 1e6:.2f}"),
            ("Tensão (kV)", "V_t", lambda v: f"{safe_convert(v) * self.simulation.config.v_base / 1000:.2f}"),
            ("Frequência (Hz)", "omega", lambda v: f"{safe_convert(v) * self.simulation.config.f_base:.2f}"),
            ("Estado", None, lambda _: "Conectado" if self.simulation.system.breaker_status[idx + 4] else "Desconectado")
        ]

        # Helper function to safely convert numpy values to float
        def safe_convert(value):
            try:
                if hasattr(value, 'shape') and value.shape:
                    # If it's a numpy array with elements, get the first element
                    return float(value[0])
                else:
                    # Otherwise, convert directly to float
                    return float(value)
            except Exception as e:
                log_message(f"Erro ao converter valor: {e}", level="error")
                return 0.0

        # Create labels and value fields
        for label, attr, formatter in info_fields:
            # Label
            lbl = wx.StaticText(panel, label=label + ":")
            lbl.SetForegroundColour(wx.WHITE)

            # Value field
            if attr is None:
                # Special case for status which doesn't come from an attribute
                value = formatter(None)
            else:
                value = formatter(getattr(generator, attr, 0.0))

            value_field = wx.StaticText(panel, label=value)
            value_field.SetForegroundColour(wx.CYAN)

            # Store reference to update later
            setattr(self, f"{attr or 'status'}_field_{idx}", value_field)

            # Add to grid
            grid.Add(lbl, 0, wx.ALIGN_RIGHT | wx.ALIGN_CENTER_VERTICAL)
            grid.Add(value_field, 0, wx.EXPAND)

        sizer.Add(grid, 0, wx.EXPAND | wx.ALL, 10)
        panel.SetSizer(sizer)
        return panel

    def update(self, time_index):
        """Update displayed values."""
        # Helper function to safely convert numpy values to float
        def safe_convert(value):
            try:
                if hasattr(value, 'shape') and value.shape:
                    # If it's a numpy array with elements, get the first element
                    if time_index < len(value):
                        return float(value[time_index])
                    elif len(value) > 0:
                        return float(value[-1])  # Use last value if index out of bounds
                    else:
                        return 0.0
                else:
                    # Otherwise, convert directly to float
                    return float(value)
            except Exception as e:
                log_message(f"Erro ao converter valor: {e}", level="error")
                return 0.0

        try:
            for i, gen in enumerate(self.generators):
                # Update mode
                if hasattr(self, f"mode_field_{i}"):
                    mode_text = "Síncrono" if gen.mode == "synchronous" else "Droop"
                    self.__getattribute__(f"mode_field_{i}").SetLabel(mode_text)

                # Update power
                if hasattr(self, f"P_elec_field_{i}"):
                    power_text = f"{safe_convert(gen.P_elec) * self.simulation.config.s_base / 1e6:.2f}"
                    self.__getattribute__(f"P_elec_field_{i}").SetLabel(power_text)

                # Update voltage
                if hasattr(self, f"V_t_field_{i}"):
                    voltage_text = f"{safe_convert(gen.V_t) * self.simulation.config.v_base / 1000:.2f}"
                    self.__getattribute__(f"V_t_field_{i}").SetLabel(voltage_text)

                # Update frequency
                if hasattr(self, f"omega_field_{i}"):
                    freq_text = f"{safe_convert(gen.omega) * self.simulation.config.f_base:.2f}"
                    self.__getattribute__(f"omega_field_{i}").SetLabel(freq_text)

                # Update status
                if hasattr(self, f"status_field_{i}"):
                    status_text = "Conectado" if self.simulation.system.breaker_status[i + 4] else "Desconectado"
                    self.__getattribute__(f"status_field_{i}").SetLabel(status_text)

        except Exception as e:
            log_message(f"Erro ao atualizar informações do gerador: {e}", level="error")

class TurbineParamsPanel(wx.Panel):
    """Panel for turbine parameters display."""
    def __init__(self, parent, generators, simulation):
        super(TurbineParamsPanel, self).__init__(parent, style=wx.BORDER_SUNKEN)
        self.generators = generators
        self.simulation = simulation
        self.SetBackgroundColour(wx.BLACK)
        self._init_ui()

    def _init_ui(self):
        """Initialize UI components."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)

        # Create title with version-compatible font style
        title = wx.StaticText(self, label="PARÂMETROS GOVERNADOR E TURBINA")
        title.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        title.SetForegroundColour(wx.WHITE)
        main_sizer.Add(title, 0, wx.ALL | wx.CENTER, 5)

        # Create notebook for generator tabs
        notebook = wx.Notebook(self)
        notebook.SetBackgroundColour(wx.Colour(40, 40, 40))

        # Create a tab for each generator
        for i, gen in enumerate(self.generators):
            panel = self._create_generator_tab(notebook, gen, i)
            notebook.AddPage(panel, f"Gerador {chr(65 + i)}")

        main_sizer.Add(notebook, 1, wx.EXPAND | wx.ALL, 5)
        self.SetSizer(main_sizer)

    def _create_generator_tab(self, parent, generator, idx):
        """Create a tab for a single generator's turbine parameters."""
        panel = wx.Panel(parent)
        panel.SetBackgroundColour(wx.Colour(40, 40, 40))
        sizer = wx.BoxSizer(wx.VERTICAL)

        # Parameters grid
        grid = wx.FlexGridSizer(9, 2, 10, 10)
        grid.AddGrowableCol(1)

        # Parameter definitions - mesmos parâmetros do diálogo de configuração
        params = [
            ("K_pgov", "Ganho Proporcional (P)", 0, 20.0),
            ("K_igov", "Ganho Integral (I)", 0, 10.0),
            ("K_dgov", "Ganho Derivativo (D)", 0, 5.0),
            ("K_turb", "Ganho da Turbina", 0, 5.0),
            ("T_turb", "Constante de Tempo (s)", 0, 1.0),
            ("W_fnl", "Vazão Combustível em Vazio", 0, 1.0),
            ("disturb_start", "Início Perturbação (s)", 0, 10.0),
            ("disturb_end", "Fim Perturbação (s)", 0, 10.0),
            ("disturb_value", "Valor Perturbação (pu)", -1.0, 1.0)
        ]

        # Create controls for each parameter
        for param_name, label, min_val, max_val in params:
            lbl = wx.StaticText(panel, label=label)
            lbl.SetForegroundColour(wx.WHITE)

            ctrl = wx.SpinCtrlDouble(panel, min=min_val, max=max_val, inc=0.1)
            ctrl.SetBackgroundColour(wx.Colour(60, 60, 60))
            ctrl.SetForegroundColour(wx.WHITE)

            # Get attribute value, handling numpy arrays
            attr_value = getattr(generator, param_name, 0.0)

            # Convert numpy array or scalar to float
            try:
                if hasattr(attr_value, 'shape') and attr_value.shape:
                    # If it's a numpy array with elements, get the first element
                    value = float(attr_value[0])
                else:
                    # Otherwise, convert directly to float
                    value = float(attr_value)

                if param_name == "P_mech":
                    value /= 1e6  # Convert W to MW
                ctrl.SetValue(value)
            except Exception as e:
                log_message(f"Erro ao converter valor para {param_name}: {e}", level="error")
                ctrl.SetValue(0.0)  # Default value

            setattr(self, f"{param_name}_ctrl_{idx}", ctrl)

            grid.Add(lbl, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)
            grid.Add(ctrl, 0, wx.EXPAND | wx.ALL, 5)

        sizer.Add(grid, 0, wx.EXPAND | wx.ALL, 10)

        # Add apply button
        apply_btn = wx.Button(panel, label="Aplicar Mudanças")
        apply_btn.Bind(wx.EVT_BUTTON, lambda evt: self._on_apply(evt, generator, idx))
        sizer.Add(apply_btn, 0, wx.ALL | wx.CENTER, 10)

        panel.SetSizer(sizer)
        return panel

    def _on_apply(self, event, generator, idx):
        """Handle apply button click."""
        try:
            # Get float values from controls for all parameters
            generator.K_pgov = float(self.__getattribute__(f"K_pgov_ctrl_{idx}").GetValue())
            generator.K_igov = float(self.__getattribute__(f"K_igov_ctrl_{idx}").GetValue())
            generator.K_dgov = float(self.__getattribute__(f"K_dgov_ctrl_{idx}").GetValue())
            generator.K_turb = float(self.__getattribute__(f"K_turb_ctrl_{idx}").GetValue())
            generator.T_turb = float(self.__getattribute__(f"T_turb_ctrl_{idx}").GetValue())
            generator.W_fnl = float(self.__getattribute__(f"W_fnl_ctrl_{idx}").GetValue())
            generator.disturb_start = float(self.__getattribute__(f"disturb_start_ctrl_{idx}").GetValue())
            generator.disturb_end = float(self.__getattribute__(f"disturb_end_ctrl_{idx}").GetValue())
            generator.disturb_value = float(self.__getattribute__(f"disturb_value_ctrl_{idx}").GetValue())

            # Update simulation if needed
            if hasattr(self.simulation, "update_generator_params"):
                self.simulation.update_generator_params(idx)

            wx.MessageBox("Parâmetros atualizados com sucesso", "Sucesso", wx.OK | wx.ICON_INFORMATION)
        except Exception as e:
            wx.MessageBox(f"Erro ao atualizar parâmetros: {e}", "Erro", wx.OK | wx.ICON_ERROR)

    def update(self, time_index):
        """Update displayed values."""
        for i, gen in enumerate(self.generators):
            try:
                # Helper function to safely convert numpy values to float
                def safe_convert(value):
                    try:
                        if hasattr(value, 'shape') and value.shape:
                            # If it's a numpy array with elements, get the first element
                            return float(value[0])
                        else:
                            # Otherwise, convert directly to float
                            return float(value)
                    except Exception as e:
                        log_message(f"Erro ao converter valor: {e}", level="error")
                        return 0.0

                # Convert numpy values to float - atualizar todos os parâmetros
                self.__getattribute__(f"K_pgov_ctrl_{i}").SetValue(safe_convert(gen.K_pgov))
                self.__getattribute__(f"K_igov_ctrl_{i}").SetValue(safe_convert(gen.K_igov))
                self.__getattribute__(f"K_dgov_ctrl_{i}").SetValue(safe_convert(gen.K_dgov))
                self.__getattribute__(f"K_turb_ctrl_{i}").SetValue(safe_convert(gen.K_turb))
                self.__getattribute__(f"T_turb_ctrl_{i}").SetValue(safe_convert(gen.T_turb))
                self.__getattribute__(f"W_fnl_ctrl_{i}").SetValue(safe_convert(gen.W_fnl))
                self.__getattribute__(f"disturb_start_ctrl_{i}").SetValue(safe_convert(gen.disturb_start))
                self.__getattribute__(f"disturb_end_ctrl_{i}").SetValue(safe_convert(gen.disturb_end))
                self.__getattribute__(f"disturb_value_ctrl_{i}").SetValue(safe_convert(gen.disturb_value))
            except Exception as e:
                log_message(f"Erro ao atualizar valores da turbina: {e}", level="error")

class GeneratorControlPanel(wx.Panel):
    """Panel for generator control."""
    def __init__(self, parent, generators, simulation):
        super(GeneratorControlPanel, self).__init__(parent, style=wx.BORDER_SUNKEN)
        self.generators = generators
        self.simulation = simulation
        self.SetBackgroundColour(wx.BLACK)
        self._init_ui()

    def _init_ui(self):
        """Initialize UI components."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)

        # Create title
        title = wx.StaticText(self, label="CONTROLE DOS GERADORES")
        title.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        title.SetForegroundColour(wx.WHITE)
        main_sizer.Add(title, 0, wx.ALL | wx.CENTER, 5)

        # Create notebook for generator tabs
        notebook = wx.Notebook(self)
        notebook.SetBackgroundColour(wx.Colour(40, 40, 40))

        # Create a tab for each generator
        for i, gen in enumerate(self.generators):
            panel = self._create_generator_control(notebook, gen, i)
            notebook.AddPage(panel, f"Gerador {chr(65 + i)}")

        main_sizer.Add(notebook, 1, wx.EXPAND | wx.ALL, 5)
        self.SetSizer(main_sizer)

    def _create_generator_control(self, parent, generator, idx):
        """Create control panel for a single generator."""
        panel = wx.Panel(parent)
        panel.SetBackgroundColour(wx.Colour(40, 40, 40))
        sizer = wx.BoxSizer(wx.VERTICAL)

        # Removido o título do gerador, pois já temos abas com os nomes dos geradores

        # Modo de operação agora está incluído no grid de parâmetros

        # Add control parameters
        params_grid = wx.FlexGridSizer(4, 2, 10, 10)
        params_grid.AddGrowableCol(1)

        # Primeiro, adicionar o modo como parte do grid
        mode_lbl = wx.StaticText(panel, label="Modo Operação:")
        mode_lbl.SetForegroundColour(wx.WHITE)
        params_grid.Add(mode_lbl, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)

        mode_choice = wx.Choice(panel, choices=["Síncrono", "Droop"])
        mode_choice.SetSelection(0 if generator.mode == "synchronous" else 1)
        mode_choice.Bind(wx.EVT_CHOICE, lambda evt: self._on_mode_change(evt, generator, idx))
        mode_choice.SetBackgroundColour(wx.Colour(60, 60, 60))
        mode_choice.SetForegroundColour(wx.WHITE)
        setattr(self, f"mode_ctrl_{idx}", mode_choice)
        params_grid.Add(mode_choice, 0, wx.EXPAND | wx.ALL, 5)

        # Control parameters
        params = [
            ("v_ref", "Tensão Ref. (pu)", 0.9, 1.1),
            ("freq_ref", "Freq. Ref. (Hz)", 59.0, 61.0),
            ("droop", "Droop (%)", 0, 10)
        ]
        for param_name, label, min_val, max_val in params:
            lbl = wx.StaticText(panel, label=label)
            lbl.SetForegroundColour(wx.WHITE)

            ctrl = wx.SpinCtrlDouble(panel, min=min_val, max=max_val, inc=0.1)
            ctrl.SetBackgroundColour(wx.Colour(60, 60, 60))
            ctrl.SetForegroundColour(wx.WHITE)

            value = getattr(generator, param_name, 0.0)
            if param_name == "freq_ref":
                value = value * self.simulation.config.f_base  # Convert to Hz
            elif param_name == "droop":
                value = value * 100  # Convert to percentage
            ctrl.SetValue(value)

            setattr(self, f"{param_name}_ctrl_{idx}", ctrl)

            params_grid.Add(lbl, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)
            params_grid.Add(ctrl, 0, wx.EXPAND | wx.ALL, 5)

        sizer.Add(params_grid, 0, wx.EXPAND | wx.ALL, 10)

        # Add apply button
        apply_btn = wx.Button(panel, label="Aplicar Mudanças")
        apply_btn.Bind(wx.EVT_BUTTON, lambda evt: self._on_apply(evt, generator, idx))
        sizer.Add(apply_btn, 0, wx.ALL | wx.CENTER, 10)

        panel.SetSizer(sizer)
        return panel

    def _on_mode_change(self, event, generator, idx):
        """Handle generator mode change."""
        mode = "synchronous" if event.GetSelection() == 0 else "droop"
        try:
            generator.mode = mode
            if hasattr(self.simulation, "update_generator_mode"):
                self.simulation.update_generator_mode(idx, mode)
            log_message(f"Modo do Gerador {generator.name} alterado para {mode}", level="info")
        except Exception as e:
            log_message(f"Erro ao alterar modo do gerador: {e}", level="error")

    def _on_apply(self, event, generator, idx):
        """Handle apply button click."""
        try:
            # Update generator parameters
            # Atualiza v_ref e V_setpoint para manter consistência
            v_ref_value = self.__getattribute__(f"v_ref_ctrl_{idx}").GetValue()
            generator.v_ref = v_ref_value
            generator.V_setpoint = v_ref_value

            # Atualiza freq_ref
            generator.freq_ref = self.__getattribute__(f"freq_ref_ctrl_{idx}").GetValue() / self.simulation.config.f_base

            # Atualiza droop e droop_coefficient para manter consistência
            droop_value = self.__getattribute__(f"droop_ctrl_{idx}").GetValue() / 100
            generator.droop = droop_value
            generator.droop_coefficient = droop_value

            # Update simulation if needed
            if hasattr(self.simulation, "update_generator_params"):
                self.simulation.update_generator_params(idx)
            wx.MessageBox("Controles atualizados com sucesso", "Sucesso", wx.OK | wx.ICON_INFORMATION)
        except Exception as e:
            wx.MessageBox(f"Erro ao atualizar controles: {e}", "Erro", wx.OK | wx.ICON_ERROR)

    def update(self, time_index):
        """Update displayed values."""
        for i, gen in enumerate(self.generators):
            try:
                # Helper function to safely convert numpy values to float
                def safe_convert(value):
                    try:
                        if hasattr(value, 'shape') and value.shape:
                            # If it's a numpy array with elements, get the first element
                            return float(value[0])
                        else:
                            # Otherwise, convert directly to float
                            return float(value)
                    except Exception as e:
                        log_message(f"Erro ao converter valor: {e}", level="error")
                        return 0.0

                # Update controls with current values
                self.__getattribute__(f"v_ref_ctrl_{i}").SetValue(safe_convert(gen.v_ref))
                self.__getattribute__(f"freq_ref_ctrl_{i}").SetValue(safe_convert(gen.freq_ref) * self.simulation.config.f_base)
                self.__getattribute__(f"droop_ctrl_{i}").SetValue(safe_convert(gen.droop) * 100)
                self.__getattribute__(f"mode_ctrl_{i}").SetSelection(0 if gen.mode == "synchronous" else 1)
            except Exception as e:
                log_message(f"Erro ao atualizar valores de controle: {e}", level="error")

class BusParametersPanel(wx.Panel):
    """Panel for displaying and editing bus parameters."""
    def __init__(self, parent, bus):
        super(BusParametersPanel, self).__init__(parent)
        self.bus = bus
        self.SetBackgroundColour(wx.BLACK)

        # Create the panel contents
        self._init_ui()

    def _init_ui(self):
        """Initialize the UI components."""
        # Main sizer
        main_sizer = wx.BoxSizer(wx.VERTICAL)

        # Title with smaller font for RDP clients
        title = wx.StaticText(self, label="CONFIGURAÇÃO BARRAMENTO")
        title.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        title.SetForegroundColour(wx.Colour(0, 255, 0))  # Green text
        main_sizer.Add(title, 0, wx.ALL | wx.ALIGN_CENTER, 3)

        # Parameters grid with reduced spacing for RDP clients
        grid_sizer = wx.FlexGridSizer(rows=6, cols=2, vgap=5, hgap=5)
        grid_sizer.AddGrowableCol(1, 1)  # Make the second column expandable

        # Define parameters
        self.bus_params = [
            ("load_p", "Carga ativa (MW)"),
            ("load_q", "Carga reativa (MVAR)"),
            ("v_base", "Tensão base (kV)"),
            ("f_base", "Frequência base (Hz)"),
            ("r_eq", "Resistência equivalente (pu)"),
            ("x_eq", "Reatância equivalente (pu)"),
        ]

        # Create input fields
        self.bus_inputs = {}
        for param_name, label in self.bus_params:
            # Label
            param_label = wx.StaticText(self, label=label)
            param_label.SetForegroundColour(wx.Colour(255, 255, 255))  # White text
            param_label.SetFont(wx.Font(9, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_NORMAL))

            # Format value based on parameter type
            try:
                if param_name == "v_base":
                    value = str(getattr(self.bus.config, param_name) / 1000.0)
                    # Set default value for Base Voltage to 13.8 kV
                    if not value or float(value) == 0:
                        value = "13.8"
                elif param_name == "f_base":
                    value = str(getattr(self.bus.config, param_name))
                    # Set default value for Base Frequency to 60.0 Hz
                    if not value or float(value) == 0:
                        value = "60.0"
                elif param_name in ["load_p", "load_q"]:
                    value = str(getattr(self.bus, param_name) / 1e6)
                elif param_name == "r_eq":
                    value = str(getattr(self.bus, param_name))
                    # Set default value for Equivalent Resistance to 0.01 pu
                    if not value or float(value) == 0:
                        value = "0.01"
                elif param_name == "x_eq":
                    value = str(getattr(self.bus, param_name))
                    # Set default value for Equivalent Reactance to 0.1 pu
                    if not value or float(value) == 0:
                        value = "0.1"
                else:
                    value = str(getattr(self.bus, param_name) if hasattr(self.bus, param_name) else
                            getattr(self.bus.config, param_name, ""))
            except Exception as e:
                log_message(f"Erro ao obter valor para {param_name}: {e}", level="error")
                value = ""

            # Text control with smaller size for better fit on RDP clients
            text_ctrl = wx.TextCtrl(self, value=value, style=wx.TE_RIGHT, size=(80, 25))
            text_ctrl.SetBackgroundColour(wx.Colour(40, 40, 40))  # Dark gray background
            text_ctrl.SetForegroundColour(wx.Colour(0, 255, 0))  # Bright green text
            text_ctrl.SetFont(wx.Font(10, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))

            # Add to grid
            grid_sizer.Add(param_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALIGN_RIGHT)
            grid_sizer.Add(text_ctrl, 0, wx.EXPAND)

            # Store reference
            self.bus_inputs[param_name] = text_ctrl

        # Add grid to main sizer with reduced padding for better fit on RDP clients
        main_sizer.Add(grid_sizer, 1, wx.ALL | wx.EXPAND, 5)

        # Apply button with smaller size
        apply_btn = wx.Button(self, label="Aplicar Mudanças", size=(-1, 30))
        apply_btn.SetBackgroundColour(wx.Colour(0, 80, 0))  # Dark green background
        apply_btn.SetForegroundColour(wx.Colour(0, 255, 0))  # Bright green text
        apply_btn.Bind(wx.EVT_BUTTON, self.on_apply)
        main_sizer.Add(apply_btn, 0, wx.ALL | wx.ALIGN_CENTER, 5)

        # Set sizer
        self.SetSizer(main_sizer)
        self.Layout()

    def on_apply(self, event):
        """Apply changes to the bus parameters."""
        try:
            # Update bus parameters
            self.bus.load_p = float(self.bus_inputs["load_p"].GetValue()) * 1e6
            self.bus.load_q = float(self.bus_inputs["load_q"].GetValue()) * 1e6

            # Update config parameters
            self.bus.config.v_base = float(self.bus_inputs["v_base"].GetValue()) * 1000.0
            self.bus.config.f_base = float(self.bus_inputs["f_base"].GetValue())

            # Update bus electrical parameters
            self.bus.r_eq = float(self.bus_inputs["r_eq"].GetValue())
            self.bus.x_eq = float(self.bus_inputs["x_eq"].GetValue())
            self.bus.y_eq = 1 / (self.bus.r_eq + 1j * self.bus.x_eq)

            # Notify parent that parameters have changed
            evt = wx.CommandEvent(wx.EVT_BUTTON.typeId)
            evt.SetEventObject(self)
            evt.SetId(wx.ID_APPLY)
            self.GetEventHandler().ProcessEvent(evt)

            # Show success message
            wx.MessageBox(
                "Parâmetros do barramento atualizados com sucesso.",
                "Sucesso",
                wx.OK | wx.ICON_INFORMATION
            )
        except ValueError as e:
            wx.MessageBox(
                f"Erro: Por favor, insira valores numéricos válidos!\nDetalhes: {e}",
                "Erro de Entrada",
                wx.OK | wx.ICON_ERROR
            )

    def update_values(self):
        """Update the displayed values from the bus object."""
        for param_name, _ in self.bus_params:
            try:
                if param_name == "v_base":
                    value = str(getattr(self.bus.config, param_name) / 1000.0)
                elif param_name in ["load_p", "load_q"]:
                    value = str(getattr(self.bus, param_name) / 1e6)
                else:
                    value = str(getattr(self.bus, param_name) if hasattr(self.bus, param_name) else
                            getattr(self.bus.config, param_name, ""))
                self.bus_inputs[param_name].SetValue(value)
            except Exception as e:
                log_message(f"Erro ao atualizar valor para {param_name}: {e}", level="error")


class LoadsPanel(wx.Panel):
    """Panel for displaying load indicators with logical states from the PLC."""
    def __init__(self, parent, simulation):
        super(LoadsPanel, self).__init__(parent, style=wx.BORDER_SUNKEN)
        self.simulation = simulation
        self.SetBackgroundColour(wx.BLACK)

        # Dictionary to store LED indicators and their corresponding tags
        self.load_indicators = {}

        # Define the load tags and their descriptions
        self.load_tags = [
            ("O86BA101A", "LT1", "MC-123101A"),
            ("O86BA102A", "LT2", "MC-123102A"),
            ("O86BA202A", "LT3", "TF-513202A"),
            ("O86BA101C", "LT4", "MC-123101C"),
            ("O86BA102B", "LT5", "MC-123102B"),
            ("O86BA202B", "LT6", "TF-513202B"),
            ("O86BA101B", "LT7", "MC-123101B"),
            ("O86BA102C", "LT8", "MC-123102C"),
            ("O86BA101D", "LT9", "MC-123101D"),
            ("O86BA101E", "LT10", "MC-123101E"),
            ("O86BD3102A", "LT11", "M-C-3123102A"),
            ("O86BC001A", "LT12", "TF-123001A"),
            ("O86BD3102B", "LT13", "M-C-3123102B"),
            ("O86BD001B", "LT14", "TF-123001B")
        ]

        self._init_ui()

    def _init_ui(self):
        """Initialize UI components."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)

        # Create title
        title = wx.StaticText(self, label="CARGAS")
        title.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        title.SetForegroundColour(wx.WHITE)
        main_sizer.Add(title, 0, wx.ALL | wx.CENTER, 5)

        # Create a scrolled panel for the load indicators
        self.scrolled_panel = wx.ScrolledWindow(self)
        self.scrolled_panel.SetBackgroundColour(wx.BLACK)
        self.scrolled_panel.SetScrollRate(5, 5)

        # Create a sizer for the scrolled panel
        scroll_sizer = wx.BoxSizer(wx.VERTICAL)

        # Create a header row
        header_sizer = wx.BoxSizer(wx.HORIZONTAL)
        header_font = wx.Font(10, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD)

        # Status header
        status_header = wx.StaticText(self.scrolled_panel, label="Status")
        status_header.SetFont(header_font)
        status_header.SetForegroundColour(wx.WHITE)

        # Priority header
        priority_header = wx.StaticText(self.scrolled_panel, label="Prioridade")
        priority_header.SetFont(header_font)
        priority_header.SetForegroundColour(wx.WHITE)

        # Description header
        desc_header = wx.StaticText(self.scrolled_panel, label="Descrição")
        desc_header.SetFont(header_font)
        desc_header.SetForegroundColour(wx.WHITE)

        # Add headers to the header sizer
        header_sizer.Add(status_header, 0, wx.ALIGN_CENTER | wx.RIGHT, 20)
        header_sizer.Add(priority_header, 0, wx.ALIGN_CENTER | wx.RIGHT, 20)
        header_sizer.Add(desc_header, 1, wx.ALIGN_LEFT)

        # Add header sizer to the main scroll sizer
        scroll_sizer.Add(header_sizer, 0, wx.EXPAND | wx.ALL, 10)

        # Add a separator line
        separator = wx.StaticLine(self.scrolled_panel)
        separator.SetForegroundColour(wx.WHITE)
        scroll_sizer.Add(separator, 0, wx.EXPAND | wx.LEFT | wx.RIGHT, 10)

        # Add some space after the separator
        scroll_sizer.Add((-1, 10))  # Vertical spacer

        # Create indicators for each load
        for tag, priority, description in self.load_tags:
            # Create a row sizer for this load
            row_sizer = wx.BoxSizer(wx.HORIZONTAL)

            # Create LED indicator panel
            led_panel = self._create_led_indicator(tag)

            # Create priority label
            priority_label = wx.StaticText(self.scrolled_panel, label=priority)
            priority_label.SetForegroundColour(wx.WHITE)

            # Create description label
            desc_label = wx.StaticText(self.scrolled_panel, label=description)
            desc_label.SetForegroundColour(wx.WHITE)

            # Add to row sizer
            row_sizer.Add(led_panel, 0, wx.ALIGN_CENTER | wx.RIGHT, 20)
            row_sizer.Add(priority_label, 0, wx.ALIGN_CENTER | wx.RIGHT, 20)
            row_sizer.Add(desc_label, 1, wx.ALIGN_LEFT)

            # Add row to the main scroll sizer
            scroll_sizer.Add(row_sizer, 0, wx.EXPAND | wx.ALL, 5)
        self.scrolled_panel.SetSizer(scroll_sizer)

        main_sizer.Add(self.scrolled_panel, 1, wx.EXPAND | wx.ALL, 5)
        self.SetSizer(main_sizer)

    def _create_led_indicator(self, tag):
        """Create an LED indicator for a load."""
        # Create a small panel to act as the LED
        led_panel = wx.Panel(self.scrolled_panel, size=(20, 20))
        led_panel.SetBackgroundColour(wx.RED)  # Default to red (OFF)

        # Store reference to the LED panel
        self.load_indicators[tag] = led_panel

        return led_panel

    def update(self, time_index=None):
        """Update the load indicators based on PLC tag values."""
        if not hasattr(self.simulation, 'plc_comm'):
            log_message("PLC communication not initialized", level="warning")
            return

        plc_comm = self.simulation.plc_comm

        for tag, _, _ in self.load_tags:
            if tag in self.load_indicators:
                try:
                    # Read the tag value from the PLC
                    ret = plc_comm.plc.Read(tag)

                    if ret.Status == "Success":
                        # Update the LED color based on the tag value
                        # True = ON (green), False = OFF (red)
                        if ret.Value:
                            self.load_indicators[tag].SetBackgroundColour(wx.GREEN)
                        else:
                            self.load_indicators[tag].SetBackgroundColour(wx.RED)

                        # Refresh the LED panel
                        self.load_indicators[tag].Refresh()
                    else:
                        log_message(f"Error reading tag {tag}: {ret.Status}", level="error")
                except Exception as e:
                    log_message(f"Error updating load indicator for {tag}: {e}", level="error")
                    # Set to gray to indicate error
                    self.load_indicators[tag].SetBackgroundColour(wx.LIGHT_GREY)
                    self.load_indicators[tag].Refresh()