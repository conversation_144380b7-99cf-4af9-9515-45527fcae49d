"""
Painel de controle do AVR para a interface gráfica.

<PERSON><PERSON> módulo define o painel de controle do AVR (Automatic Voltage Regulator)
para a interface gráfica do sistema elétrico de potência.
"""

import wx
import logging
from typing import List, Dict, Any

from core.generator import Generator
from core.simulation import Simulation
from core.voltage_controller import VoltageController

# Constantes para estilo da fonte
FONT_STYLE_NORMAL = wx.FONTSTYLE_NORMAL

def log_message(message, level="info"):
    """Log a message with the specified level."""
    if level == "debug":
        logging.debug(message)
    elif level == "info":
        logging.info(message)
    elif level == "warning":
        logging.warning(message)
    elif level == "error":
        logging.error(message)
    elif level == "critical":
        logging.critical(message)
    else:
        logging.info(message)

class AVRControlPanel(wx.Panel):
    """
    Painel de controle do AVR (Automatic Voltage Regulator).

    Este painel permite visualizar e ajustar os parâmetros do AVR para cada gerador,
    bem como os parâmetros do controlador de tensão do barramento principal.
    """

    def __init__(self, parent, generators: List[Generator], simulation: Simulation):
        """
        Inicializa o painel de controle do AVR.

        Args:
            parent: Widget pai
            generators: Lista de geradores
            simulation: Objeto de simulação
        """
        super(AVRControlPanel, self).__init__(parent, style=wx.BORDER_SUNKEN)
        self.generators = generators
        self.simulation = simulation
        self.voltage_controller = simulation.system.voltage_controller
        self.SetBackgroundColour(wx.BLACK)
        self._init_ui()

    def _init_ui(self):
        """Inicializa a interface do usuário."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)

        # Título do painel
        title = wx.StaticText(self, label="CONTROLE DE TENSÃO (AVR)")
        title.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        title.SetForegroundColour(wx.WHITE)
        main_sizer.Add(title, 0, wx.ALL | wx.CENTER, 5)

        # Criar notebook para abas
        notebook = wx.Notebook(self)
        notebook.SetBackgroundColour(wx.Colour(40, 40, 40))

        # Aba para o controlador de tensão do barramento principal
        main_bus_panel = self._create_main_bus_tab(notebook)
        notebook.AddPage(main_bus_panel, "Barramento Principal")

        # Abas para cada gerador
        for i, gen in enumerate(self.generators):
            panel = self._create_generator_tab(notebook, gen, i)
            notebook.AddPage(panel, f"Gerador {gen.name}")

        main_sizer.Add(notebook, 1, wx.EXPAND | wx.ALL, 5)
        self.SetSizer(main_sizer)

    def _create_main_bus_tab(self, parent):
        """
        Cria a aba para o controlador de tensão do barramento principal.

        Args:
            parent: Widget pai

        Returns:
            Panel: Painel com os controles do barramento principal
        """
        panel = wx.Panel(parent)
        panel.SetBackgroundColour(wx.Colour(40, 40, 40))
        sizer = wx.BoxSizer(wx.VERTICAL)

        # Adicionar informações sobre o controlador de tensão
        info_text = (
            "O controlador de tensão do barramento principal coordena os AVRs dos geradores "
            "para manter a tensão do barramento principal próxima ao setpoint desejado. "
            "Ele pode operar em dois modos: Droop de tensão ou Compartilhamento de potência reativa."
        )
        info = wx.StaticText(panel, label=info_text)
        info.SetForegroundColour(wx.WHITE)
        info.Wrap(400)  # Quebra o texto em 400 pixels
        sizer.Add(info, 0, wx.ALL | wx.EXPAND, 10)

        # Adicionar parâmetros do controlador
        params_grid = wx.FlexGridSizer(5, 2, 10, 10)
        params_grid.AddGrowableCol(1)

        # Parâmetros do controlador
        params = [
            ("v_setpoint", "Setpoint de Tensão (pu)", 0.9, 1.1),
            ("v_droop", "Coeficiente de Droop", 0.0, 0.2),
            ("K_p", "Ganho Proporcional", 0.0, 10.0),
            ("K_i", "Ganho Integral", 0.0, 5.0),
            ("K_d", "Ganho Derivativo", 0.0, 1.0)
        ]

        # Adicionar controles para cada parâmetro
        for param_name, label, min_val, max_val in params:
            lbl = wx.StaticText(panel, label=label + ":")
            lbl.SetForegroundColour(wx.WHITE)

            ctrl = wx.SpinCtrlDouble(panel, min=min_val, max=max_val, inc=0.01)
            ctrl.SetBackgroundColour(wx.Colour(60, 60, 60))
            ctrl.SetForegroundColour(wx.WHITE)

            # Obter valor atual do parâmetro
            value = getattr(self.voltage_controller, param_name, 0.0)
            ctrl.SetValue(value)

            # Armazenar referência para atualização posterior
            setattr(self, f"main_bus_{param_name}_ctrl", ctrl)

            # Adicionar ao grid
            params_grid.Add(lbl, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)
            params_grid.Add(ctrl, 0, wx.EXPAND | wx.ALL, 5)

        sizer.Add(params_grid, 0, wx.EXPAND | wx.ALL, 10)

        # Adicionar checkbox para compartilhamento de potência reativa
        q_sharing_box = wx.CheckBox(panel, label="Habilitar compartilhamento de potência reativa")
        q_sharing_box.SetForegroundColour(wx.WHITE)
        q_sharing_box.SetValue(self.voltage_controller.q_sharing_enabled)
        self.q_sharing_ctrl = q_sharing_box
        sizer.Add(q_sharing_box, 0, wx.ALL, 10)

        # Adicionar botão para aplicar alterações
        apply_btn = wx.Button(panel, label="Aplicar Alterações")
        apply_btn.Bind(wx.EVT_BUTTON, self.on_apply_main_bus)
        sizer.Add(apply_btn, 0, wx.ALL | wx.CENTER, 10)

        # Adicionar área de status
        status_box = wx.StaticBox(panel, label="Status Atual")
        status_box.SetForegroundColour(wx.WHITE)
        status_sizer = wx.StaticBoxSizer(status_box, wx.VERTICAL)

        # Criar grid para status
        status_grid = wx.FlexGridSizer(3, 2, 5, 10)
        status_grid.AddGrowableCol(1)

        # Adicionar campos de status
        status_fields = [
            ("Tensão Atual", "main_bus_voltage"),
            ("Erro de Tensão", "v_error"),
            ("Modo de Controle", "control_mode")
        ]

        for label, field_name in status_fields:
            lbl = wx.StaticText(panel, label=label + ":")
            lbl.SetForegroundColour(wx.WHITE)

            value_field = wx.StaticText(panel, label="---")
            value_field.SetForegroundColour(wx.CYAN)

            # Armazenar referência para atualização posterior
            setattr(self, f"{field_name}_field", value_field)

            # Adicionar ao grid
            status_grid.Add(lbl, 0, wx.ALIGN_RIGHT | wx.ALIGN_CENTER_VERTICAL)
            status_grid.Add(value_field, 0, wx.EXPAND)

        status_sizer.Add(status_grid, 0, wx.EXPAND | wx.ALL, 5)
        sizer.Add(status_sizer, 0, wx.EXPAND | wx.ALL, 10)

        panel.SetSizer(sizer)
        return panel

    def _create_generator_tab(self, parent, generator, idx):
        """
        Cria uma aba para um gerador específico.

        Args:
            parent: Widget pai
            generator: Objeto gerador
            idx: Índice do gerador

        Returns:
            Panel: Painel com os controles do gerador
        """
        panel = wx.Panel(parent)
        panel.SetBackgroundColour(wx.Colour(40, 40, 40))
        sizer = wx.BoxSizer(wx.VERTICAL)

        # Adicionar informações sobre o AVR
        info_text = (
            f"O AVR (Automatic Voltage Regulator) do Gerador {generator.name} controla a tensão terminal "
            f"do gerador para mantê-la próxima ao setpoint desejado. Ele utiliza um controlador PID "
            f"para calcular a saída de controle."
        )
        info = wx.StaticText(panel, label=info_text)
        info.SetForegroundColour(wx.WHITE)
        info.Wrap(400)  # Quebra o texto em 400 pixels
        sizer.Add(info, 0, wx.ALL | wx.EXPAND, 10)

        # Adicionar equação do AVR
        equation_box = wx.StaticBox(panel, label="Equação do AVR")
        equation_box.SetForegroundColour(wx.WHITE)
        equation_sizer = wx.StaticBoxSizer(equation_box, wx.VERTICAL)

        equation_text = (
            "AVR_output = K_p * e(t) + K_i * ∫e(t)dt + K_d * de(t)/dt\n"
            "onde e(t) = V_setpoint - V_t"
        )
        equation = wx.StaticText(panel, label=equation_text)
        equation.SetForegroundColour(wx.WHITE)
        equation_sizer.Add(equation, 0, wx.ALL, 10)

        sizer.Add(equation_sizer, 0, wx.EXPAND | wx.ALL, 10)

        # Adicionar parâmetros do AVR
        params_grid = wx.FlexGridSizer(6, 2, 10, 10)
        params_grid.AddGrowableCol(1)

        # Parâmetros do AVR
        params = [
            ("V_setpoint", "Setpoint de Tensão (pu)", 0.9, 1.1),
            ("K_pavr", "Ganho Proporcional (P)", 0.0, 20.0),
            ("K_iavr", "Ganho Integral (I)", 0.0, 5.0),
            ("K_davr", "Ganho Derivativo (D)", 0.0, 1.0),
            ("T_avr", "Constante de Tempo (s)", 0.01, 0.5),
            ("V_max", "Limite Máximo de Tensão", 1.0, 1.2)
        ]

        # Adicionar controles para cada parâmetro
        for param_name, label, min_val, max_val in params:
            lbl = wx.StaticText(panel, label=label + ":")
            lbl.SetForegroundColour(wx.WHITE)

            ctrl = wx.SpinCtrlDouble(panel, min=min_val, max=max_val, inc=0.01)
            ctrl.SetBackgroundColour(wx.Colour(60, 60, 60))
            ctrl.SetForegroundColour(wx.WHITE)

            # Obter valor atual do parâmetro
            value = getattr(generator, param_name, 0.0)
            ctrl.SetValue(value)

            # Armazenar referência para atualização posterior
            setattr(self, f"{param_name}_ctrl_{idx}", ctrl)

            # Adicionar ao grid
            params_grid.Add(lbl, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)
            params_grid.Add(ctrl, 0, wx.EXPAND | wx.ALL, 5)

        sizer.Add(params_grid, 0, wx.EXPAND | wx.ALL, 10)

        # Adicionar botão para aplicar alterações
        apply_btn = wx.Button(panel, label="Aplicar Alterações")
        apply_btn.Bind(wx.EVT_BUTTON, lambda event, gen_idx=idx: self.on_apply_generator(event, gen_idx))
        sizer.Add(apply_btn, 0, wx.ALL | wx.CENTER, 10)

        # Adicionar área de status
        status_box = wx.StaticBox(panel, label="Status Atual")
        status_box.SetForegroundColour(wx.WHITE)
        status_sizer = wx.StaticBoxSizer(status_box, wx.VERTICAL)

        # Criar grid para status
        status_grid = wx.FlexGridSizer(4, 2, 5, 10)
        status_grid.AddGrowableCol(1)

        # Adicionar campos de status
        status_fields = [
            ("Tensão Terminal", f"V_t_{idx}"),
            ("Erro de Tensão", f"v_error_{idx}"),
            ("Potência Reativa", f"Q_elec_{idx}"),
            ("Estado", f"state_{idx}")
        ]

        for label, field_name in status_fields:
            lbl = wx.StaticText(panel, label=label + ":")
            lbl.SetForegroundColour(wx.WHITE)

            value_field = wx.StaticText(panel, label="---")
            value_field.SetForegroundColour(wx.CYAN)

            # Armazenar referência para atualização posterior
            setattr(self, f"{field_name}_field", value_field)

            # Adicionar ao grid
            status_grid.Add(lbl, 0, wx.ALIGN_RIGHT | wx.ALIGN_CENTER_VERTICAL)
            status_grid.Add(value_field, 0, wx.EXPAND)

        status_sizer.Add(status_grid, 0, wx.EXPAND | wx.ALL, 5)
        sizer.Add(status_sizer, 0, wx.EXPAND | wx.ALL, 10)

        panel.SetSizer(sizer)
        return panel

    def on_apply_main_bus(self, event):
        """
        Aplica as alterações nos parâmetros do controlador de tensão do barramento principal.

        Args:
            event: Evento de clique no botão
        """
        try:
            # Obter valores dos controles
            v_setpoint = self.main_bus_v_setpoint_ctrl.GetValue()
            v_droop = self.main_bus_v_droop_ctrl.GetValue()
            k_p = self.main_bus_K_p_ctrl.GetValue()
            k_i = self.main_bus_K_i_ctrl.GetValue()
            k_d = self.main_bus_K_d_ctrl.GetValue()
            q_sharing = self.q_sharing_ctrl.GetValue()

            # Aplicar valores ao controlador
            self.voltage_controller.set_main_bus_voltage_setpoint(v_setpoint)
            self.voltage_controller.set_voltage_droop(v_droop)
            self.voltage_controller.K_p = k_p
            self.voltage_controller.K_i = k_i
            self.voltage_controller.K_d = k_d
            self.voltage_controller.enable_q_sharing(q_sharing)

            # Registrar alterações
            log_message(f"Parâmetros do controlador de tensão atualizados: "
                        f"v_setpoint={v_setpoint:.4f}, v_droop={v_droop:.4f}, "
                        f"K_p={k_p:.4f}, K_i={k_i:.4f}, K_d={k_d:.4f}, "
                        f"q_sharing={q_sharing}")

            # Exibir mensagem de sucesso
            wx.MessageBox("Parâmetros do controlador de tensão atualizados com sucesso!",
                          "Sucesso", wx.OK | wx.ICON_INFORMATION)

        except Exception as e:
            # Exibir mensagem de erro
            log_message(f"Erro ao atualizar parâmetros do controlador de tensão: {e}", level="error")
            wx.MessageBox(f"Erro ao atualizar parâmetros: {e}",
                          "Erro", wx.OK | wx.ICON_ERROR)

    def on_apply_generator(self, event, idx):
        """
        Aplica as alterações nos parâmetros do AVR de um gerador.

        Args:
            event: Evento de clique no botão
            idx: Índice do gerador
        """
        try:
            # Obter gerador
            generator = self.generators[idx]

            # Obter valores dos controles
            v_setpoint = getattr(self, f"V_setpoint_ctrl_{idx}").GetValue()
            k_pavr = getattr(self, f"K_pavr_ctrl_{idx}").GetValue()
            k_iavr = getattr(self, f"K_iavr_ctrl_{idx}").GetValue()
            k_davr = getattr(self, f"K_davr_ctrl_{idx}").GetValue()
            t_avr = getattr(self, f"T_avr_ctrl_{idx}").GetValue()
            v_max = getattr(self, f"V_max_ctrl_{idx}").GetValue()

            # Aplicar valores ao gerador
            generator.V_setpoint = v_setpoint
            generator.K_pavr = k_pavr
            generator.K_iavr = k_iavr
            generator.K_davr = k_davr
            generator.T_avr = t_avr
            generator.V_max = v_max
            generator.V_min = 2.0 - v_max  # Mantém simetria em torno de 1.0

            # Registrar alterações
            log_message(f"Parâmetros do AVR do Gerador {generator.name} atualizados: "
                        f"V_setpoint={v_setpoint:.4f}, K_pavr={k_pavr:.4f}, "
                        f"K_iavr={k_iavr:.4f}, K_davr={k_davr:.4f}, "
                        f"T_avr={t_avr:.4f}, V_max={v_max:.4f}")

            # Exibir mensagem de sucesso
            wx.MessageBox(f"Parâmetros do AVR do Gerador {generator.name} atualizados com sucesso!",
                          "Sucesso", wx.OK | wx.ICON_INFORMATION)

        except Exception as e:
            # Exibir mensagem de erro
            log_message(f"Erro ao atualizar parâmetros do AVR: {e}", level="error")
            wx.MessageBox(f"Erro ao atualizar parâmetros: {e}",
                          "Erro", wx.OK | wx.ICON_ERROR)

    def update(self, time_index):
        """
        Atualiza os campos de status com os valores atuais.

        Args:
            time_index: Índice de tempo atual
        """
        try:
            # Atualizar status do barramento principal
            main_bus_voltage = self.simulation.system.main_bus.voltage
            v_setpoint = self.voltage_controller.v_setpoint
            v_error = v_setpoint - main_bus_voltage
            control_mode = "Compartilhamento Q" if self.voltage_controller.q_sharing_enabled else "Droop"

            self.main_bus_voltage_field.SetLabel(f"{main_bus_voltage:.4f} pu")
            self.v_error_field.SetLabel(f"{v_error:.4f} pu")
            self.control_mode_field.SetLabel(control_mode)

            # Atualizar status dos geradores
            for idx, gen in enumerate(self.generators):
                # Verificar se os campos existem
                if hasattr(self, f"V_t_{idx}_field") and time_index < len(gen.V_t):
                    self.__getattribute__(f"V_t_{idx}_field").SetLabel(f"{gen.V_t[time_index]:.4f} pu")

                if hasattr(self, f"v_error_{idx}_field") and time_index < len(gen.V_t):
                    v_error = gen.V_setpoint - gen.V_t[time_index]
                    self.__getattribute__(f"v_error_{idx}_field").SetLabel(f"{v_error:.4f} pu")

                if hasattr(self, f"Q_elec_{idx}_field") and time_index < len(gen.Q_elec):
                    self.__getattribute__(f"Q_elec_{idx}_field").SetLabel(f"{gen.Q_elec[time_index]:.4f} pu")

                if hasattr(self, f"state_{idx}_field"):
                    state_str = gen.state.upper()
                    self.__getattribute__(f"state_{idx}_field").SetLabel(state_str)

        except Exception as e:
            log_message(f"Erro ao atualizar status do AVR: {e}", level="error")
