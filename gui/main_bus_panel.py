#!/usr/bin/env python3
"""
Painel de indicadores para <PERSON><PERSON> (Principal).
"""

import wx
from gui.dialogs import log_message

# Check wxPython version for font compatibility
WX_VERSION = tuple(map(int, wx.version().split('.')[:2]))
if WX_VERSION >= (4, 1):
    FONT_STYLE_NORMAL = wx.FONTSTYLE_NORMAL
else:
    FONT_STYLE_NORMAL = wx.FONTSTYLE_NORMAL


class MainBusIndicatorPanel(wx.Panel):
    """Painel para exibir indicadores do barramento principal."""
    
    def __init__(self, parent, simulation):
        super().__init__(parent)
        self.simulation = simulation
        self.main_bus = simulation.system.main_bus
        self.config = simulation.config
        
        self.SetBackgroundColour(wx.Colour(30, 30, 30))
        self._init_ui()
    
    def _init_ui(self):
        """Inicializa a interface do usuário."""
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # Título principal
        title = wx.StaticText(self, label="BARRAMENTO COMUM - INDICADORES")
        title.SetFont(wx.Font(14, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        title.SetForegroundColour(wx.Colour(255, 255, 255))
        main_sizer.Add(title, 0, wx.ALL | wx.CENTER, 10)
        
        # Subtítulo
        subtitle = wx.StaticText(self, label="Barramento Principal 13,8 kV")
        subtitle.SetFont(wx.Font(10, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
        subtitle.SetForegroundColour(wx.Colour(200, 200, 200))
        main_sizer.Add(subtitle, 0, wx.ALL | wx.CENTER, 5)
        
        # Painel de indicadores principais
        indicators_panel = self._create_main_indicators()
        main_sizer.Add(indicators_panel, 0, wx.EXPAND | wx.ALL, 10)
        
        # Painel de informações do sistema
        system_info_panel = self._create_system_info()
        main_sizer.Add(system_info_panel, 0, wx.EXPAND | wx.ALL, 10)
        
        # Painel de status
        status_panel = self._create_status_panel()
        main_sizer.Add(status_panel, 0, wx.EXPAND | wx.ALL, 10)
        
        self.SetSizer(main_sizer)
        self.Layout()
    
    def _create_main_indicators(self):
        """Cria o painel com os indicadores principais."""
        panel = wx.Panel(self)
        panel.SetBackgroundColour(wx.Colour(40, 40, 40))
        
        # Borda
        border_sizer = wx.StaticBoxSizer(
            wx.StaticBox(panel, label="Parâmetros Elétricos"), 
            wx.VERTICAL
        )
        border_sizer.GetStaticBox().SetForegroundColour(wx.WHITE)
        
        # Grid para os indicadores
        grid_sizer = wx.FlexGridSizer(3, 4, 10, 20)  # 3 linhas, 4 colunas
        grid_sizer.AddGrowableCol(1)
        grid_sizer.AddGrowableCol(3)
        
        # Indicadores principais
        indicators = [
            ("Tensão:", "voltage", "kV", wx.Colour(0, 255, 255)),      # Cyan
            ("Frequência:", "frequency", "Hz", wx.Colour(255, 165, 0)), # Orange
            ("Potência:", "power", "MW", wx.Colour(0, 255, 0)),        # Green
        ]
        
        for label_text, attr_name, unit, color in indicators:
            # Label
            label = wx.StaticText(panel, label=label_text)
            label.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
            label.SetForegroundColour(wx.WHITE)
            
            # Valor
            value_label = wx.StaticText(panel, label="---")
            value_label.SetFont(wx.Font(16, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
            value_label.SetForegroundColour(color)
            setattr(self, f"{attr_name}_value", value_label)
            
            # Unidade
            unit_label = wx.StaticText(panel, label=unit)
            unit_label.SetFont(wx.Font(10, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
            unit_label.SetForegroundColour(wx.Colour(200, 200, 200))
            
            # Valor em pu
            pu_label = wx.StaticText(panel, label="(--- pu)")
            pu_label.SetFont(wx.Font(9, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
            pu_label.SetForegroundColour(wx.Colour(150, 150, 150))
            setattr(self, f"{attr_name}_pu", pu_label)
            
            grid_sizer.Add(label, 0, wx.ALIGN_CENTER_VERTICAL)
            grid_sizer.Add(value_label, 0, wx.ALIGN_CENTER)
            grid_sizer.Add(unit_label, 0, wx.ALIGN_CENTER_VERTICAL)
            grid_sizer.Add(pu_label, 0, wx.ALIGN_CENTER_VERTICAL)
        
        border_sizer.Add(grid_sizer, 1, wx.EXPAND | wx.ALL, 10)
        panel.SetSizer(border_sizer)
        
        return panel
    
    def _create_system_info(self):
        """Cria o painel com informações do sistema."""
        panel = wx.Panel(self)
        panel.SetBackgroundColour(wx.Colour(40, 40, 40))
        
        border_sizer = wx.StaticBoxSizer(
            wx.StaticBox(panel, label="Informações do Sistema"), 
            wx.VERTICAL
        )
        border_sizer.GetStaticBox().SetForegroundColour(wx.WHITE)
        
        # Grid para informações
        info_grid = wx.FlexGridSizer(2, 4, 5, 15)
        info_grid.AddGrowableCol(1)
        info_grid.AddGrowableCol(3)
        
        # Informações do sistema
        info_items = [
            ("Tensão Base:", f"{self.config.v_base/1000:.1f} kV"),
            ("Potência Base:", f"{self.config.s_base/1e6:.1f} MVA"),
            ("Frequência Base:", f"{self.config.f_base:.1f} Hz"),
            ("Impedância Base:", f"{self.config.z_base:.3f} Ω"),
        ]
        
        for label_text, value_text in info_items:
            label = wx.StaticText(panel, label=label_text)
            label.SetFont(wx.Font(9, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
            label.SetForegroundColour(wx.Colour(200, 200, 200))
            
            value = wx.StaticText(panel, label=value_text)
            value.SetFont(wx.Font(9, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
            value.SetForegroundColour(wx.Colour(255, 255, 255))
            
            info_grid.Add(label, 0, wx.ALIGN_CENTER_VERTICAL)
            info_grid.Add(value, 0, wx.ALIGN_CENTER_VERTICAL)
        
        border_sizer.Add(info_grid, 1, wx.EXPAND | wx.ALL, 10)
        panel.SetSizer(border_sizer)
        
        return panel
    
    def _create_status_panel(self):
        """Cria o painel de status."""
        panel = wx.Panel(self)
        panel.SetBackgroundColour(wx.Colour(40, 40, 40))
        
        border_sizer = wx.StaticBoxSizer(
            wx.StaticBox(panel, label="Status do Sistema"), 
            wx.VERTICAL
        )
        border_sizer.GetStaticBox().SetForegroundColour(wx.WHITE)
        
        # Grid para status
        status_grid = wx.FlexGridSizer(2, 2, 10, 20)
        status_grid.AddGrowableCol(1)
        
        # Status items
        status_items = [
            ("Geradores Conectados:", "connected_generators"),
            ("Barramentos Ativos:", "active_buses"),
        ]
        
        for label_text, attr_name in status_items:
            label = wx.StaticText(panel, label=label_text)
            label.SetFont(wx.Font(10, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
            label.SetForegroundColour(wx.Colour(200, 200, 200))
            
            value = wx.StaticText(panel, label="---")
            value.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
            value.SetForegroundColour(wx.Colour(0, 255, 0))
            setattr(self, f"{attr_name}_value", value)
            
            status_grid.Add(label, 0, wx.ALIGN_CENTER_VERTICAL)
            status_grid.Add(value, 0, wx.ALIGN_CENTER_VERTICAL)
        
        border_sizer.Add(status_grid, 1, wx.EXPAND | wx.ALL, 10)
        panel.SetSizer(border_sizer)
        
        return panel
    
    def update(self, time_index):
        """
        Atualiza os indicadores com os valores atuais.
        
        Args:
            time_index: Índice de tempo atual
        """
        try:
            # Obter valores do barramento principal
            voltage_pu = self.main_bus.voltage
            frequency_pu = self.main_bus.frequency
            power_pu = self.main_bus.power
            
            # Converter para unidades reais
            voltage_kv = self.main_bus.get_voltage_kv()
            frequency_hz = self.main_bus.get_frequency_hz()
            power_mw = self.main_bus.get_power_mw()
            
            # Atualizar indicadores principais
            self.voltage_value.SetLabel(f"{voltage_kv:.2f}")
            self.voltage_pu.SetLabel(f"({voltage_pu:.4f} pu)")
            
            self.frequency_value.SetLabel(f"{frequency_hz:.2f}")
            self.frequency_pu.SetLabel(f"({frequency_pu:.4f} pu)")
            
            self.power_value.SetLabel(f"{power_mw:.2f}")
            self.power_pu.SetLabel(f"({power_pu:.4f} pu)")
            
            # Atualizar status do sistema
            connected_generators = sum(1 for i in range(4) if self.simulation.system.breaker_status[i + 4])
            active_buses = sum(1 for i in range(4) if self.simulation.system.breaker_status[i])
            
            self.connected_generators_value.SetLabel(f"{connected_generators}/4")
            self.active_buses_value.SetLabel(f"{active_buses}/4")
            
            # Atualizar cores baseadas nos valores
            self._update_indicator_colors(voltage_pu, frequency_pu, power_pu)
            
        except Exception as e:
            log_message(f"Erro ao atualizar indicadores do barramento principal: {e}", level="error")
    
    def _update_indicator_colors(self, voltage_pu, frequency_pu, power_pu):
        """Atualiza as cores dos indicadores baseado nos valores."""
        try:
            # Cores baseadas em faixas operacionais
            
            # Tensão: Verde (0.95-1.05), Amarelo (0.9-0.95 ou 1.05-1.1), Vermelho (fora)
            if 0.95 <= voltage_pu <= 1.05:
                voltage_color = wx.Colour(0, 255, 0)  # Verde
            elif 0.9 <= voltage_pu <= 1.1:
                voltage_color = wx.Colour(255, 255, 0)  # Amarelo
            else:
                voltage_color = wx.Colour(255, 0, 0)  # Vermelho
            self.voltage_value.SetForegroundColour(voltage_color)
            
            # Frequência: Verde (0.98-1.02), Amarelo (0.95-0.98 ou 1.02-1.05), Vermelho (fora)
            if 0.98 <= frequency_pu <= 1.02:
                freq_color = wx.Colour(255, 165, 0)  # Orange (normal)
            elif 0.95 <= frequency_pu <= 1.05:
                freq_color = wx.Colour(255, 255, 0)  # Amarelo
            else:
                freq_color = wx.Colour(255, 0, 0)  # Vermelho
            self.frequency_value.SetForegroundColour(freq_color)
            
            # Potência: Verde (normal), Amarelo (alta), Vermelho (muito alta)
            if abs(power_pu) <= 0.8:
                power_color = wx.Colour(0, 255, 0)  # Verde
            elif abs(power_pu) <= 1.0:
                power_color = wx.Colour(255, 255, 0)  # Amarelo
            else:
                power_color = wx.Colour(255, 0, 0)  # Vermelho
            self.power_value.SetForegroundColour(power_color)
            
        except Exception as e:
            log_message(f"Erro ao atualizar cores dos indicadores: {e}", level="error")
