# gui_fixed.py
"""
Este módulo contém a lógica da GUI para o diagrama de barramento elétrico.

Ele fornece classes para exibir o esquema do sistema elétrico de potência, dados em tempo real
e controles interativos para configuração.
"""

# Standard library imports
import wx
import json
import os
import numpy as np

# Matplotlib setup
import matplotlib
matplotlib.use('WXAgg')  # Must be before importing pyplot
from matplotlib.backends.backend_wxagg import FigureCanvasWxAgg
import matplotlib.pyplot as plt
from matplotlib.ticker import MultipleLocator

# Local imports
from gui.panels import (
    GeneratorInfoPanel,
    TurbineParamsPanel,
    GeneratorControlPanel,
    BusParametersPanel,
    LoadsPanel  # New panel for load indicators
)
from gui.avr_panel import AVRControlPanel
from gui.dialogs import (
    ConfigDialog,
    BreakerConfigDialog,
    GeneratorConfigDialog,
    CurrentValuesDialog,
    log_message
)

# Check wxPython version
WX_VERSION = tuple(map(int, wx.version().split('.')[:2]))

# Define font style based on wx version
if WX_VERSION >= (4, 1):
    FONT_STYLE_NORMAL = wx.FONTSTYLE_NORMAL
else:
    FONT_STYLE_NORMAL = wx.FONTSTYLE_NORMAL  # Use the same for older versions

# Usa o canvas matplotlib WXAgg diretamente para melhor integração
class CustomCanvas(FigureCanvasWxAgg):
    def __init__(self, parent, figure, **kwargs):
        # Inicializa o FigureCanvasWxAgg com a figura
        FigureCanvasWxAgg.__init__(self, parent, -1, figure)
        self.SetBackgroundColour(wx.BLACK)  # Define explicitamente o fundo preto
        self.figure = figure
        self.redraw_needed = True  # Flag para controlar redesenhos
        self.redraw_counter = 0  # Contador para redesenhos periódicos
        self.is_drawing = False  # Flag para evitar desenho recursivo

        # Define um tamanho mínimo para garantir que os gráficos sejam visíveis
        self.SetMinSize(wx.Size(800, 600))

        # Vincula evento de tamanho para lidar com o redimensionamento adequadamente
        self.Bind(wx.EVT_SIZE, self.OnSize)

    def draw_figure(self, force=False):
        # Evita desenho recursivo
        if self.is_drawing:
            return

        # Define flag de desenho
        self.is_drawing = True

        try:
            # Incrementa contador de redesenho
            self.redraw_counter += 1

            # Tenta identificar qual barramento está sendo desenhado
            parent = self.GetParent()
            bus_idx = getattr(parent, 'bus_idx', -1)
            bus_name = chr(65 + bus_idx) if bus_idx >= 0 else 'Desconhecido'

            # Verifica se é uma renderização final
            is_final_render = hasattr(parent, 'final_render_done')

            # Para otimização RDP: Redesenha apenas se forçado ou realmente necessário
            # Isso reduz significativamente o número de redesenhos
            if not force and not self.redraw_needed:
                log_message(f"Pulando redesenho para Barramento {bus_name} - não necessário e não forçado", level="debug")
                return

            # Obtém tamanho atual
            width, height = self.GetSize()
            if width <= 0 or height <= 0:
                width, height = 800, 600  # Tamanho padrão maior

            log_message(f"Desenhando figura para Barramento {bus_name} (forçado={force}, necessário={self.redraw_needed}, contador={self.redraw_counter}, renderização_final={is_final_render})", level="debug")

            # Define o tamanho da figura para corresponder ao tamanho do painel
            dpi = 100  # DPI mais alto para melhor qualidade
            self.figure.set_size_inches(width/dpi, height/dpi)
            self.figure.set_dpi(dpi)

            # Garante que a figura tenha fundo preto
            self.figure.patch.set_facecolor('black')
            self.figure.patch.set_alpha(1.0)

            # Garante que todos os títulos estejam visíveis com estilo adequado
            parent = self.GetParent()
            if hasattr(parent, 'bus_idx'):
                # Get scaled font sizes if parent has the method
                if hasattr(parent, '_get_scaled_font_sizes'):
                    font_sizes = parent._get_scaled_font_sizes()
                    title_size = font_sizes['title']
                    subtitle_size = font_sizes['subtitle']
                else:
                    # Fallback to smaller hardcoded sizes
                    title_size = 10
                    subtitle_size = 9

                # Atualiza o título principal da figura
                self.figure.suptitle(f"Dados Barramento {chr(65 + parent.bus_idx)}", color='white', fontsize=title_size, fontweight='bold')

                # Garante que os títulos dos subplots estejam visíveis
                if hasattr(parent, 'ax_power') and hasattr(parent, 'ax_freq') and hasattr(parent, 'ax_volt') and hasattr(parent, 'ax_current'):
                    parent.ax_power.set_title(f"Barramento {chr(65 + parent.bus_idx)} - Potência", color='white', fontsize=subtitle_size, fontweight='bold')
                    parent.ax_freq.set_title(f"Barramento {chr(65 + parent.bus_idx)} - Frequência", color='white', fontsize=subtitle_size, fontweight='bold')
                    parent.ax_volt.set_title(f"Barramento {chr(65 + parent.bus_idx)} - Tensão", color='white', fontsize=subtitle_size, fontweight='bold')
                    parent.ax_current.set_title(f"Barramento {chr(65 + parent.bus_idx)} - Corrente", color='white', fontsize=subtitle_size, fontweight='bold')

            # Desenha a figura usando o canvas WXAgg
            log_message(f"Chamando draw() para Barramento {bus_name}", level="debug")
            try:
                # Use draw_idle() instead of draw() for better compatibility
                if hasattr(self, 'draw_idle'):
                    self.draw_idle()
                else:
                    self.draw()
                self.redraw_needed = False
            except Exception as e:
                log_message(f"Erro ao desenhar canvas: {e}", level="error")

            log_message(f"Canvas redesenhado para Barramento {bus_name} (forçado={force}, contador={self.redraw_counter})", level="debug")
        finally:
            # Reseta a flag de desenho quando terminar
            self.is_drawing = False

    def OnSize(self, event):
        self.redraw_needed = True
        # Obtém o tamanho atual
        size = self.GetSize()

        # Agenda um redesenho apenas se tivermos um tamanho válido
        if size.width > 50 and size.height > 50:
            # Atrasa o redesenho para evitar múltiplos redesenhos durante o redimensionamento
            # Usa um temporizador para atrasar ainda mais o redesenho até que o redimensionamento esteja completo
            if hasattr(self, '_resize_timer'):
                self._resize_timer.Stop()
            else:
                self._resize_timer = wx.Timer(self)
                self.Bind(wx.EVT_TIMER, self._on_resize_timer)

            # Inicia o temporizador com um atraso de 200ms
            self._resize_timer.Start(200, oneShot=True)

        # Allow the event to propagate to parent handlers
        event.Skip()

    def _on_resize_timer(self, event):
        """Trata evento do temporizador de redimensionamento - chamado após as operações de redimensionamento estarem completas."""
        # Obter o tamanho atual
        size = self.GetSize()

        # Atualizar o tamanho da figura para corresponder ao canvas
        if hasattr(self, 'figure') and self.figure is not None:
            dpi = self.figure.get_dpi()
            self.figure.set_size_inches(size.width / dpi, size.height / dpi)

            # Forçar um redesenho do canvas
            self.draw()

            # Marcar que o redesenho não é mais necessário
            self.redraw_needed = False

            log_message(f"Canvas redimensionado para {size.width}x{size.height}, tamanho da figura atualizado", level="debug")

# Usa nosso canvas personalizado em vez do FigureCanvas
FigureCanvas = CustomCanvas

class GraphPanel(wx.Panel):
    """Painel para exibir gráficos de dados do barramento."""

    def __init__(self, parent, simulation, bus_idx):
        super(GraphPanel, self).__init__(parent)
        self.SetBackgroundColour(wx.BLACK)  # Define fundo preto
        self.simulation = simulation
        self.bus_idx = bus_idx
        self.time_index = 0
        self.last_update_time = -1  # Forçar atualização inicial

        # Definir um tamanho mínimo para garantir que o painel de gráfico esteja sempre visível
        self.SetMinSize(wx.Size(600, 400))

        # Fechar quaisquer figuras matplotlib existentes para evitar vazamentos de memória e sobreposição de gráficos
        plt.close('all')

        # Criar layout
        vbox = wx.BoxSizer(wx.VERTICAL)

        # Adicionar um título para o painel de gráfico para separá-lo claramente do esquemático
        title_panel = wx.Panel(self)
        title_panel.SetBackgroundColour(wx.Colour(40, 40, 40))  # Fundo cinza escuro
        title_sizer = wx.BoxSizer(wx.HORIZONTAL)

        # Criar título com indicador de barramento
        self.title_text = wx.StaticText(title_panel, label=f"GRÁFICOS SIMULAÇÃO - BARRAMENTO {chr(65 + self.bus_idx)}")
        self.title_text.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        self.title_text.SetForegroundColour(wx.Colour(200, 200, 200))  # Texto cinza claro
        title_sizer.Add(self.title_text, 1, wx.ALIGN_CENTER | wx.ALL, 5)

        # Adicionar um indicador de barramento com uma cor diferente para destacá-lo
        self.bus_indicator = wx.StaticText(title_panel, label=f"BARRAMENTO {chr(65 + self.bus_idx)}")
        self.bus_indicator.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        self.bus_indicator.SetForegroundColour(wx.Colour(0, 255, 0))  # Texto verde brilhante
        title_sizer.Add(self.bus_indicator, 0, wx.ALIGN_CENTER | wx.ALL, 5)

        # Adicionar seletor de tipo de gráfico
        graph_type_label = wx.StaticText(title_panel, label="Tipo gráfico:")
        graph_type_label.SetFont(wx.Font(10, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
        graph_type_label.SetForegroundColour(wx.Colour(200, 200, 200))  # Texto cinza claro
        title_sizer.Add(graph_type_label, 0, wx.ALIGN_CENTER | wx.LEFT, 15)

        # Criar controle de escolha do tipo de gráfico
        self.graph_type_choice = wx.Choice(title_panel, choices=["Padrão", "Balanço Potência"])
        self.graph_type_choice.SetSelection(0)  # Padrão para gráficos padrão
        self.graph_type_choice.SetBackgroundColour(wx.Colour(60, 60, 60))  # Fundo cinza escuro
        self.graph_type_choice.SetForegroundColour(wx.Colour(255, 255, 255))  # Texto branco
        self.graph_type_choice.Bind(wx.EVT_CHOICE, self.on_graph_type_change)
        title_sizer.Add(self.graph_type_choice, 0, wx.ALIGN_CENTER | wx.LEFT, 5)

        # Adicionar seletor de tipo de dados
        data_type_label = wx.StaticText(title_panel, label="Exibir:")
        data_type_label.SetFont(wx.Font(10, wx.FONTFAMILY_DEFAULT, FONT_STYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
        data_type_label.SetForegroundColour(wx.Colour(200, 200, 200))  # Texto cinza claro
        title_sizer.Add(data_type_label, 0, wx.ALIGN_CENTER | wx.LEFT, 15)

        # Criar controle de escolha do tipo de dados
        self.data_type_choice = wx.Choice(title_panel, choices=["Potência", "Frequência", "Tensão", "Corrente"])
        self.data_type_choice.SetSelection(0)  # Potência como padrão
        self.data_type_choice.SetBackgroundColour(wx.Colour(60, 60, 60))  # Fundo cinza escuro
        self.data_type_choice.SetForegroundColour(wx.Colour(255, 255, 255))  # Texto branco
        self.data_type_choice.Bind(wx.EVT_CHOICE, self.on_data_type_change)
        title_sizer.Add(self.data_type_choice, 0, wx.ALIGN_CENTER | wx.LEFT, 5)

        title_panel.SetSizer(title_sizer)
        vbox.Add(title_panel, 0, wx.EXPAND | wx.BOTTOM, 5)

        # Armazenar o tipo de gráfico e tipo de dados atuais
        self.current_graph_type = "Padrão"
        self.current_data_type = "Potência"

        # Criar figura matplotlib com um único subplot
        try:
            # Criar figura com fundo preto explícito e transparent=True
            # Tamanho aumentado significativamente para melhor visualização
            self.figure = plt.figure(figsize=(12, 8), dpi=80, facecolor='black', edgecolor='black')
            self.figure.patch.set_facecolor('black')  # Definir explicitamente a cor de fundo
            self.figure.patch.set_alpha(1.0)  # Garantir que está totalmente opaco
            log_message("Figura matplotlib com fundo preto criada com sucesso", level="info")
        except Exception as e:
            log_message(f"Erro ao criar figura matplotlib: {e}", level="error")
            # Fallback para figura padrão com tamanho maior
            self.figure = plt.figure(figsize=(12, 8), dpi=80)
            self.figure.patch.set_facecolor('black')
            self.figure.patch.set_alpha(1.0)

        # Criar um único subplot
        self.ax_plot = self.figure.add_subplot(111)

        # Configurar aparência do gráfico
        self._configure_plot_appearance()

        # Criar linhas de dados iniciais
        self._create_data_lines()

        # Criar canvas para renderizar a figura
        # Usar o canvas WXAgg diretamente
        self.canvas = FigureCanvasWxAgg(self, -1, self.figure)
        self.canvas.SetBackgroundColour(wx.BLACK)  # Definir explicitamente o fundo preto

        # Vincular evento de tamanho para manter tamanho consistente
        self.canvas.Bind(wx.EVT_SIZE, self.on_canvas_size)

        vbox.Add(self.canvas, 1, wx.EXPAND | wx.ALL, 5)

        self.SetSizer(vbox)
        self.Layout()

        # Vincular evento de tamanho para lidar com o redimensionamento adequadamente
        self.Bind(wx.EVT_SIZE, self.on_size)

    def _get_scaled_font_sizes(self):
        """Calculate font sizes based on figure size for proportional scaling."""
        # Get current figure size in inches
        fig_width, fig_height = self.figure.get_size_inches()

        # Calculate a base scale factor based on figure area
        # Use a reference size of 12x8 inches as baseline
        reference_area = 12 * 8
        current_area = fig_width * fig_height
        scale_factor = min(1.5, max(0.6, (current_area / reference_area) ** 0.5))

        # Define base font sizes and scale them
        base_title_size = 10  # Reduced from 14 to make main title smaller
        base_subtitle_size = 9  # Reduced from 12 to make subplot titles smaller
        base_label_size = 10
        base_legend_size = 10
        base_tick_size = 8

        return {
            'title': int(base_title_size * scale_factor),
            'subtitle': int(base_subtitle_size * scale_factor),
            'label': int(base_label_size * scale_factor),
            'legend': int(base_legend_size * scale_factor),
            'tick': int(base_tick_size * scale_factor)
        }

    def _configure_plot_appearance(self):
        """Configura a aparência do gráfico."""
        try:
            # Definir cor de fundo como preto com opacidade total
            self.figure.patch.set_facecolor('black')
            self.figure.patch.set_alpha(1.0)  # Garantir opacidade total

            # Configurar aparência do eixo
            self.ax_plot.set_facecolor('black')
            self.ax_plot.patch.set_alpha(1.0)  # Garantir opacidade total

            # Configurar grade mais visível e bem definida
            self.ax_plot.grid(True, color='gray', alpha=0.5, linestyle='-', linewidth=0.5)
            self.ax_plot.grid(True, which='minor', color='gray', alpha=0.25, linestyle='--', linewidth=0.3)
            self.ax_plot.minorticks_on()  # Ativar marcações menores para grade mais detalhada

            # Configurar bordas
            for spine in self.ax_plot.spines.values():
                spine.set_color('white')

            # Configurar marcações com font size escalado
            font_sizes = self._get_scaled_font_sizes()
            self.ax_plot.tick_params(axis='both', colors='white', labelsize=font_sizes['tick'])

            # Adicionar preenchimento
            self.ax_plot.margins(x=0.01, y=0.1)

            # Definir explicitamente os limites do eixo x para 0-16 segundos
            self.ax_plot.set_xlim(0, 16.0)

            log_message("Aparência do gráfico configurada com sucesso com tema escuro", level="info")
        except Exception as e:
            log_message(f"Erro ao configurar aparência do gráfico: {e}", level="error")

        # Definir títulos e rótulos
        try:
            # Get scaled font sizes
            font_sizes = self._get_scaled_font_sizes()

            # Definir título principal da figura com mais espaço acima
            self.figure.suptitle(f"Barramento {chr(65 + self.bus_idx)}", color='white',
                               fontsize=font_sizes['title'], fontweight='bold', y=0.95)

            # Definir título do gráfico com base no tipo de dados atual
            self._update_plot_title()

            # Definir rótulos dos eixos
            self.ax_plot.set_xlabel("Tempo (s)", color='white', fontsize=font_sizes['label'])

            # O rótulo do eixo y será definido com base no tipo de dados selecionado
            if self.current_data_type == "Potência":
                self.ax_plot.set_ylabel("Potência (MW)", color='white', fontsize=font_sizes['label'])
            elif self.current_data_type == "Frequência":
                self.ax_plot.set_ylabel("Frequência (Hz)", color='white', fontsize=font_sizes['label'])
            elif self.current_data_type == "Tensão":
                self.ax_plot.set_ylabel("Tensão (kV)", color='white', fontsize=font_sizes['label'])
            elif self.current_data_type == "Corrente":
                self.ax_plot.set_ylabel("Corrente (A)", color='white', fontsize=font_sizes['label'])

            log_message("Títulos e rótulos do gráfico definidos com sucesso", level="info")
        except Exception as e:
            log_message(f"Erro ao definir títulos e rótulos do gráfico: {e}", level="error")

        # Definir limites padrão do eixo y para melhor visibilidade
        try:
            # Os limites serão definidos dinamicamente na atualização do gráfico
            # Definir valores iniciais padrão
            if self.current_data_type == "Potência":
                self.ax_plot.set_ylim(0, 30)  # Faixa padrão para potência (0-30 MW)
            elif self.current_data_type == "Frequência":
                self.ax_plot.set_ylim(57, 63)  # Faixa padrão para sistema de 60Hz
            elif self.current_data_type == "Tensão":
                self.ax_plot.set_ylim(10, 16)  # Faixa padrão para tensão (10-16 kV)
            elif self.current_data_type == "Corrente":
                self.ax_plot.set_ylim(0, 1000)  # Faixa padrão para corrente em amperes

            log_message("Limites padrão do eixo y definidos com sucesso", level="info")
        except Exception as e:
            log_message(f"Erro ao definir limites padrão do eixo y: {e}", level="error")

        # Ajustar espaçamento para melhor visualização
        try:
            # Aplicar tight_layout para calcular o espaço necessário
            self.figure.tight_layout()

            # Ajustar manualmente para garantir espaço adequado
            self.figure.subplots_adjust(
                top=0.90,      # Espaço para o título principal
                bottom=0.12,   # Espaço para o rótulo do eixo x
                left=0.12,     # Espaço para rótulos do eixo y
                right=0.95     # Margem direita
            )

            # Garantir que todos os elementos da figura tenham o fundo correto
            # Definir cores para todos os elementos de texto
            for item in [self.ax_plot.title, self.ax_plot.xaxis.label, self.ax_plot.yaxis.label]:
                if item is not None:
                    item.set_color('white')
            for label in self.ax_plot.get_xticklabels() + self.ax_plot.get_yticklabels():
                label.set_color('white')

            log_message("Espaçamento e cores do gráfico ajustados com sucesso", level="info")
        except Exception as e:
            log_message(f"Erro ao ajustar espaçamento do gráfico: {e}", level="error")

    def _create_data_lines(self):
        """Cria as linhas de dados para o gráfico."""
        try:
            # Criar uma única linha de dados com cor e rótulo apropriados
            self.line_plot, = self.ax_plot.plot([], [], linewidth=2.0)

            # Definir a cor e o rótulo com base no tipo de dados selecionado
            self._update_line_properties()

            log_message("Linha de dados criada com sucesso", level="info")

            # Adicionar legenda com font size escalado
            font_sizes = self._get_scaled_font_sizes()
            self.ax_plot.legend(handles=[self.line_plot], loc='upper right',
                    facecolor='black', edgecolor='white',
                    labelcolor='white', fontsize=font_sizes['legend'])
            log_message("Legenda adicionada com sucesso", level="info")
        except Exception as e:
            log_message(f"Erro ao criar linha de dados: {e}", level="error")

    def _update_line_properties(self):
        """Atualiza as propriedades da linha com base no tipo de dados selecionado."""
        try:
            # Definir cor e rótulo com base no tipo de dados
            if self.current_data_type == "Potência":
                self.line_plot.set_color("cyan")
                if self.current_graph_type == "Padrão":
                    self.line_plot.set_label("Potência (MW)")
                else:
                    self.line_plot.set_label("Potência Gerada (MW)")
            elif self.current_data_type == "Frequência":
                self.line_plot.set_color("orange")
                self.line_plot.set_label("Frequência (Hz)")
            elif self.current_data_type == "Tensão":
                self.line_plot.set_color("lime")
                self.line_plot.set_label("Tensão (kV)")
            elif self.current_data_type == "Corrente":
                self.line_plot.set_color("red")
                self.line_plot.set_label("Corrente (A)")
            elif self.current_data_type == "Balanço Potência" and self.current_graph_type == "Balanço Potência":
                self.line_plot.set_color("yellow")
                self.line_plot.set_label("Balanço de Potência (MW)")

            # Atualizar a legenda com font size escalado
            font_sizes = self._get_scaled_font_sizes()
            self.ax_plot.legend(handles=[self.line_plot], loc='upper right',
                    facecolor='black', edgecolor='white',
                    labelcolor='white', fontsize=font_sizes['legend'])

            # Atualizar o título do gráfico
            self._update_plot_title()

            log_message(f"Propriedades da linha atualizadas para {self.current_data_type}", level="info")
        except Exception as e:
            log_message(f"Erro ao atualizar propriedades da linha: {e}", level="error")

    def _update_plot_title(self):
        """Atualiza o título do gráfico com base no tipo de dados selecionado."""
        try:
            # Get scaled font sizes
            font_sizes = self._get_scaled_font_sizes()

            # Definir título com base no tipo de dados
            if self.current_data_type == "Potência":
                if self.current_graph_type == "Padrão":
                    self.ax_plot.set_title("Potência", color='white', fontsize=font_sizes['subtitle'], fontweight='bold', pad=10, loc='left')
                else:
                    self.ax_plot.set_title("Potência Gerada", color='white', fontsize=font_sizes['subtitle'], fontweight='bold', pad=10, loc='left')
            elif self.current_data_type == "Frequência":
                self.ax_plot.set_title("Frequência", color='white', fontsize=font_sizes['subtitle'], fontweight='bold', pad=10, loc='left')
            elif self.current_data_type == "Tensão":
                self.ax_plot.set_title("Tensão", color='white', fontsize=font_sizes['subtitle'], fontweight='bold', pad=10, loc='left')
            elif self.current_data_type == "Corrente":
                self.ax_plot.set_title("Corrente", color='white', fontsize=font_sizes['subtitle'], fontweight='bold', pad=10, loc='left')
            elif self.current_data_type == "Balanço Potência" and self.current_graph_type == "Balanço Potência":
                self.ax_plot.set_title("Balanço de Potência", color='white', fontsize=font_sizes['subtitle'], fontweight='bold', pad=10, loc='left')

            # Configurar grade específica para cada tipo de dados
            if self.current_data_type == "Frequência":
                # Para frequência, queremos uma grade mais detalhada para mostrar pequenas variações
                self.ax_plot.yaxis.set_major_locator(MultipleLocator(0.5))  # Marcações principais a cada 0.5 Hz
                self.ax_plot.yaxis.set_minor_locator(MultipleLocator(0.1))  # Marcações secundárias a cada 0.1 Hz
            elif self.current_data_type == "Potência":
                # Para potência, marcações a cada 5 MW com submarcações a cada 1 MW
                self.ax_plot.yaxis.set_major_locator(MultipleLocator(5))
                self.ax_plot.yaxis.set_minor_locator(MultipleLocator(1))
            elif self.current_data_type == "Tensão":
                # Para tensão, marcações a cada 1 kV com submarcações a cada 0.2 kV
                self.ax_plot.yaxis.set_major_locator(MultipleLocator(1))
                self.ax_plot.yaxis.set_minor_locator(MultipleLocator(0.2))
            elif self.current_data_type == "Corrente":
                # Para corrente, marcações a cada 200 A com submarcações a cada 50 A
                self.ax_plot.yaxis.set_major_locator(MultipleLocator(200))
                self.ax_plot.yaxis.set_minor_locator(MultipleLocator(50))

            # Configurar marcações do eixo x (tempo) - marcações principais a cada 2s, secundárias a cada 0.5s
            self.ax_plot.xaxis.set_major_locator(MultipleLocator(2))
            self.ax_plot.xaxis.set_minor_locator(MultipleLocator(0.5))

            log_message(f"Título do gráfico atualizado para {self.current_data_type}", level="info")
        except Exception as e:
            log_message(f"Erro ao atualizar título do gráfico: {e}", level="error")

    def on_data_type_change(self, event):
        """Trata mudança de seleção do tipo de dados."""
        selection = self.data_type_choice.GetSelection()
        data_type = self.data_type_choice.GetString(selection)

        if data_type != self.current_data_type:
            log_message(f"Alterando tipo de dados de {self.current_data_type} para {data_type}", level="info")
            self.current_data_type = data_type

            # Forçar uma atualização completa do gráfico
            self.last_update_time = -1

            # Atualizar as propriedades da linha
            self._update_line_properties()

            # Atualizar o gráfico com o novo tipo de dados
            self.update_plots()

            # Forçar um redesenho do canvas
            try:
                # Use draw_idle() instead of draw() for better compatibility
                if hasattr(self.canvas, 'draw_idle'):
                    self.canvas.draw_idle()
                else:
                    self.canvas.draw()
            except Exception as e:
                log_message(f"Erro ao desenhar canvas: {e}", level="error")

            # Forçar atualização de layout
            self.Layout()

            # Chamar explicitamente Refresh() para acionar um redesenho adequado
            self.Refresh()

    def on_size(self, event):
        """Trata eventos de redimensionamento da janela."""
        # Pular se estivermos no meio da inicialização
        if not hasattr(self, 'canvas') or not self.canvas:
            event.Skip()
            return

        # Obter o tamanho atual
        size = self.GetSize()

        # Redesenhar apenas se tivermos um tamanho válido
        if size.width > 50 and size.height > 50:
            # Armazenar dados e estado atuais
            bus_idx = self.bus_idx

            # Chamar explicitamente Refresh() para acionar um redesenho adequado
            self.Refresh()

            # Atrasar o tratamento real do redimensionamento para evitar múltiplos redesenhos
            if hasattr(self, '_resize_timer'):
                self._resize_timer.Stop()
            else:
                self._resize_timer = wx.Timer(self)
                self.Bind(wx.EVT_TIMER, self._on_resize_timer)

            # Iniciar o temporizador com um pequeno atraso
            self._resize_timer.Start(100, oneShot=True)

        event.Skip()

    def _on_resize_timer(self, event):
        """Trata evento do temporizador de redimensionamento - chamado após as operações de redimensionamento estarem completas."""
        # Obter o tamanho atual
        size = self.GetSize()

        # Atualizar apenas se tivermos um tamanho válido
        if size.width > 50 and size.height > 50 and hasattr(self, 'figure'):
            # Atualizar o tamanho da figura para corresponder ao canvas
            dpi = self.figure.get_dpi()
            self.figure.set_size_inches(size.width / dpi, size.height / dpi)

            # Forçar um redesenho do canvas
            if hasattr(self, 'canvas') and self.canvas:
                try:
                    # Use draw_idle() instead of draw() for better compatibility
                    if hasattr(self.canvas, 'draw_idle'):
                        self.canvas.draw_idle()
                    else:
                        self.canvas.draw()
                except Exception as e:
                    log_message(f"Erro ao desenhar canvas: {e}", level="error")

            # Forçar uma atualização completa dos gráficos
            self.last_update_time = -1
            self.update_plots()

            log_message(f"Painel de gráfico redimensionado para {size.width}x{size.height}, tamanho da figura atualizado", level="debug")

        # Forçar atualização de layout
        self.Layout()

        # Chamar explicitamente Refresh() para acionar um redesenho adequado
        self.Refresh()

    def on_canvas_size(self, event):
        """Trata eventos de tamanho do canvas para manter o tamanho da figura consistente."""
        # Obter o novo tamanho
        size = event.GetSize()

        # Atualizar apenas se tivermos um tamanho válido
        if size.width > 50 and size.height > 50 and hasattr(self, 'figure'):
            # Armazenar o tamanho atual para referência futura
            self.last_canvas_size = size

            # Atualizar o tamanho da figura para corresponder ao canvas
            dpi = self.figure.get_dpi()
            self.figure.set_size_inches(size.width / dpi, size.height / dpi)

            # Forçar um redesenho do canvas
            if hasattr(self, 'canvas') and self.canvas:
                try:
                    # Use draw_idle() instead of draw() for better compatibility
                    if hasattr(self.canvas, 'draw_idle'):
                        self.canvas.draw_idle()
                    else:
                        self.canvas.draw()
                except Exception as e:
                    log_message(f"Erro ao desenhar canvas: {e}", level="error")

            log_message(f"Canvas redimensionado para {size.width}x{size.height}, tamanho da figura atualizado", level="debug")

        # Permitir que o evento se propague
        event.Skip()

    def on_graph_type_change(self, event):
        """Trata mudança de seleção do tipo de gráfico."""
        selection = self.graph_type_choice.GetSelection()
        graph_type = self.graph_type_choice.GetString(selection)

        if graph_type != self.current_graph_type:
            log_message(f"Alterando tipo de gráfico de {self.current_graph_type} para {graph_type}", level="info")
            self.current_graph_type = graph_type

            # Forçar uma atualização completa do gráfico
            self.last_update_time = -1

            # Atualizar as propriedades da linha
            self._update_line_properties()

            # Atualizar o gráfico com o novo tipo de gráfico
            self.update_plots()

            # Forçar um redesenho do canvas
            try:
                # Use draw_idle() instead of draw() for better compatibility
                if hasattr(self.canvas, 'draw_idle'):
                    self.canvas.draw_idle()
                else:
                    self.canvas.draw()
            except Exception as e:
                log_message(f"Erro ao desenhar canvas: {e}", level="error")

        # Forçar atualização de layout
        self.Layout()

        # Chamar explicitamente Refresh() para acionar um redesenho adequado
        self.Refresh()

    def update_plots(self, time_index=None):
        """
        Atualiza o gráfico com os dados mais recentes da simulação.
        Otimizado para clientes RDP com frequência de atualização reduzida.

        Args:
            time_index: Índice de tempo atual da simulação (opcional)
        """
        # Atualizar índice de tempo se fornecido
        if time_index is not None:
            self.time_index = time_index

        # Pular atualização se não houver novos dados ou se já atualizamos neste índice de tempo
        if self.time_index == self.last_update_time:
            log_message(f"Pulando atualização de gráfico para Barramento {chr(65 + self.bus_idx)} - já atualizado no índice de tempo {self.time_index}", level="debug")
            return

        # Obter dados para o barramento selecionado
        try:
            log_message(f"Obtendo dados para Barramento {chr(65 + self.bus_idx)} no índice de tempo {self.time_index}", level="debug")
            data = self.simulation.get_secondary_bus_data(self.bus_idx)

            if len(data["t"]) == 0:
                log_message(f"Sem dados disponíveis para Barramento {chr(65 + self.bus_idx)} - pulando atualização", level="debug")
                return

            # Informação de depuração - imprimir apenas quando atualizando explicitamente
            # Imprimir apenas ocasionalmente para reduzir spam no console
            if self.time_index % 100 == 0 or self.time_index == len(data["t"]) - 1:
                log_message(f"Atualizando gráfico para Barramento {chr(65 + self.bus_idx)} com {len(data['t'])} pontos de dados", level="info")

            # Obter dados atuais para o barramento selecionado
            current_data = self.simulation.get_generator_current_data(self.bus_idx)
            log_message(f"Dados de corrente obtidos para Barramento {chr(65 + self.bus_idx)}", level="debug")

            # Limpar quaisquer dados existentes primeiro
            self.line_plot.set_data([], [])

            # Atualizar dados do gráfico com base no tipo de dados e tipo de gráfico selecionados
            if self.current_data_type == "Potência":
                if self.current_graph_type == "Padrão":
                    self.line_plot.set_data(data["t"], data["power"])
                    self.line_plot.set_label("Potência (MW)")
                    # Ajustar limites do eixo y
                    if len(data["power"]) > 0:
                        power_max = max(30.0, max(data["power"]) * 1.2)  # At least 30 MW range
                        self.ax_plot.set_ylim(0, power_max)
                else:
                    self.line_plot.set_data(data["t"], data["power"])
                    self.line_plot.set_label("Potência Gerada (MW)")
                    # Ajustar limites do eixo y
                    if len(data["power"]) > 0:
                        power_max = max(30.0, max(data["power"]) * 1.2)  # At least 30 MW range
                        self.ax_plot.set_ylim(0, power_max)
            elif self.current_data_type == "Frequência":
                self.line_plot.set_data(data["t"], data["frequency"])
                self.line_plot.set_label("Frequência (Hz)")
                # Ajustar limites do eixo y
                if len(data["frequency"]) > 0:
                    self.ax_plot.set_ylim(57.0, 63.0)  # Fixed 57-63 Hz range
            elif self.current_data_type == "Tensão":
                self.line_plot.set_data(data["t"], data["voltage"] / 1000.0)  # Converter para kV
                self.line_plot.set_label("Tensão (kV)")
                # Ajustar limites do eixo y
                if len(data["voltage"]) > 0:
                    self.ax_plot.set_ylim(10.0, 16.0)  # Fixed 10-16 kV range
            elif self.current_data_type == "Corrente":
                self.line_plot.set_data(data["t"], current_data)
                self.line_plot.set_label("Corrente (A)")
                # Ajustar limites do eixo y
                if len(current_data) > 0 and any(current_data > 0):
                    current_max = max(1000.0, max(current_data) * 1.2)  # At least 1000A range
                    self.ax_plot.set_ylim(0, current_max)
            elif self.current_data_type == "Balanço Potência" and self.current_graph_type == "Balanço Potência":
                # Balanço de potência (geração - carga)
                if "power_budget" in data and len(data["power_budget"]) > 0:
                    self.line_plot.set_data(data["t"], data["power_budget"])
                    self.line_plot.set_label("Balanço de Potência (MW)")
                    # Ajustar limites do eixo y
                    budget_min = min(data["power_budget"])
                    budget_max = max(data["power_budget"])
                    # Ensure at least +/- 5 MW range and symmetric around zero
                    budget_range = max(5.0, max(abs(budget_min), abs(budget_max)) * 1.2)
                    self.ax_plot.set_ylim(-budget_range, budget_range)
                else:
                    self.line_plot.set_data([], [])
                    self.line_plot.set_label("Balanço de Potência (MW)")

            # Atualizar título principal da figura com font size escalado
            font_sizes = self._get_scaled_font_sizes()
            self.figure.suptitle(f"Barramento {chr(65 + self.bus_idx)}", color='white',
                               fontsize=font_sizes['title'], fontweight='bold', y=0.95)

            # Garantir que os limites do eixo x estejam fixados em 0-16 segundos
            self.ax_plot.set_xlim(0, 16.0)

            # Atualizar a legenda com font size escalado
            self.ax_plot.legend(handles=[self.line_plot], loc='upper right',
                    facecolor='black', edgecolor='white',
                    labelcolor='white', fontsize=font_sizes['legend'])

            # Mark that redraw is needed, but don't actually redraw here
            # Let the parent ElectricalBusGUI handle when to redraw
            if hasattr(self.canvas, 'redraw_needed'):
                self.canvas.redraw_needed = True

            # Determine if this is the final frame
            is_final_frame = (self.time_index == len(data["t"]) - 1)

            log_message(f"Barramento {chr(65 + self.bus_idx)} - quadro_final={is_final_frame}, índice_tempo={self.time_index}, total_quadros={len(data['t'])}", level="debug")

            # Check if we already have a final render flag
            has_final_render_flag = hasattr(self, 'final_render_done')
            log_message(f"Barramento {chr(65 + self.bus_idx)} - flag_renderização_final={has_final_render_flag}", level="debug")

            # For all frames, just update the data but don't redraw
            # This is critical for RDP performance - only redraw when explicitly told to
            log_message(f"Barramento {chr(65 + self.bus_idx)} - Dados atualizados, adiando redesenho para o componente pai", level="debug")

            # We don't redraw here at all - the parent will handle it in update_all_ui_components
            # This ensures we only redraw when necessary (at the end of simulation or when changing buses)

            self.last_update_time = self.time_index

        except Exception as e:
            log_message(f"Erro ao atualizar gráfico: {e}", level="error")

    def set_bus_index(self, bus_idx):
        """Altera os dados do barramento exibido mantendo o tamanho do gráfico."""
        if self.bus_idx != bus_idx:
            log_message(f"Alterando exibição do gráfico do Barramento {chr(65 + self.bus_idx)} para Barramento {chr(65 + bus_idx)}", level="info")
            self.bus_idx = bus_idx

            # Update the title panel text to include the bus name
            if hasattr(self, 'title_text'):
                self.title_text.SetLabel(f"GRÁFICOS SIMULAÇÃO - BARRAMENTO {chr(65 + self.bus_idx)}")

            # Update the bus indicator text as well
            if hasattr(self, 'bus_indicator'):
                self.bus_indicator.SetLabel(f"BARRAMENTO {chr(65 + self.bus_idx)}")

            # Reset update tracking to force a complete refresh
            log_message(f"Redefinindo last_update_time de {self.last_update_time} para -1", level="debug")
            self.last_update_time = -1  # Force update

            # Reset any flags that might prevent updates
            if hasattr(self, 'final_render_done'):
                log_message("Removendo flag final_render_done", level="debug")
                delattr(self, 'final_render_done')

            # Store current canvas size if it exists
            current_canvas_size = None
            if hasattr(self, 'figure') and self.figure is not None:
                if hasattr(self, 'canvas') and self.canvas is not None:
                    current_canvas_size = self.canvas.GetSize()  # Store current canvas size
            if not hasattr(self, 'canvas') or self.canvas is None:
                print(f"[BUS CHANGE] Creating new canvas for Bus {chr(65 + self.bus_idx)}")
                self.canvas = FigureCanvasWxAgg(self, -1, self.figure)
                self.canvas.SetBackgroundColour(wx.BLACK)

                # Add the canvas to the sizer
                sizer = self.GetSizer()
                sizer.Add(self.canvas, 1, wx.EXPAND | wx.ALL, 5)

                # Apply the stored canvas size if available
                if current_canvas_size is not None:  # Use the new variable name
                    self.canvas.SetSize(current_canvas_size)

            # Atualizar o título principal da figura
            self.figure.suptitle(f"Barramento {chr(65 + self.bus_idx)}", color='white', fontsize=14, fontweight='bold', y=0.95)

            # Atualizar as propriedades da linha com base no tipo de dados atual
            self._update_line_properties()

            # Update with the new bus data
            print(f"[BUS CHANGE] Calling update_plots() to load data for Bus {chr(65 + self.bus_idx)}")
            self.update_plots()

            # Force a layout update to ensure proper sizing
            self.Layout()

            # Only now, after all data is loaded, do we force a redraw
            print(f"[BUS CHANGE] Forcing complete redraw of canvas for Bus {chr(65 + self.bus_idx)}")
            # Force the canvas to redraw
            try:
                # Use draw_idle() instead of draw() for better compatibility
                if hasattr(self.canvas, 'draw_idle'):
                    self.canvas.draw_idle()
                else:
                    self.canvas.draw()
            except Exception as e:
                log_message(f"Erro ao desenhar canvas: {e}", level="error")

            # Explicitly call Refresh() to trigger a proper redraw
            self.Refresh()


class ElectricalBusGUI(wx.Frame):
    """Main GUI frame for the electrical bus simulation."""

    def __init__(self, parent, t, generators, main_bus, simulation):
        # Get screen size to optimize for RDP clients
        display_size = wx.DisplaySize()
        # Use 80% of screen width and height for better fit on RDP clients
        width = int(display_size[0] * 0.8)
        height = int(display_size[1] * 0.8)
        # Ensure minimum size
        width = max(width, 800)
        height = max(height, 600)

        super(ElectricalBusGUI, self).__init__(
            parent, title="Simulador Sistema Elétrico Potência", size=wx.Size(width, height),
            style=wx.DEFAULT_FRAME_STYLE | wx.MAXIMIZE_BOX | wx.RESIZE_BORDER
        )
        # Set the frame background color
        self.SetBackgroundColour(wx.BLACK)

        # Store references to simulation components
        self.t = t
        self.generators = generators
        self.main_bus = main_bus
        self.simulation = simulation
        self.secondary_buses = simulation.system.secondary_buses
        self.time_index = 0

        # Initial breaker status (all closed by default)
        self.breaker_status = [True] * 8
        self._load_breaker_status()

        # Current bus selection
        self.current_bus_idx = 0

        # Initialize UI components
        self._init_ui()

        # Update simulation's breaker status
        self._update_simulation_breakers()

        # Set initial UI update frequency to match the dropdown selection
        self.simulation.ui_update_frequency = 0  # Default to end-only updates

        # Start the simulation timer
        self.timer = wx.Timer(self)
        self.Bind(wx.EVT_TIMER, self.on_timer, self.timer)
        self.timer.Start(50)  # 50ms refresh rate (20 FPS) for smoother animation

        # Initialize the elapsed time counter
        if hasattr(self, 'time_counter'):
            self.time_counter.SetLabel(self._format_elapsed_time(0))

        # Initialize display
        self.schematic_panel.Refresh()
        self.graph_panel.update_plots(0)

        # Center the frame on screen
        self.Centre()

        log_message("Inicialização da interface gráfica completa.", level="info")

    def _load_breaker_status(self):
        """Load breaker status from config.json."""
        try:
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.json")
            with open(config_path, "r") as f:
                config_data = json.load(f)
                if "breaker_status" in config_data:
                    self.breaker_status = config_data["breaker_status"]
                    # Ensure proper length
                    if len(self.breaker_status) != 8:
                        self.breaker_status = [True] * 8
                    log_message("Status dos disjuntores carregado de config.json", level="info")
        except Exception as e:
            log_message(f"Não foi possível carregar o status dos disjuntores: {e}", level="warning")
            self.breaker_status = [True] * 8

    def _update_simulation_breakers(self):
        """Update the simulation's breaker status with the GUI's breaker status."""
        try:
            # Copy breaker status to simulation
            self.simulation.system.breaker_status = self.breaker_status.copy()

            # Force network update
            self.simulation.system.update_network()
        except Exception as e:
            log_message(f"Erro ao atualizar status dos disjuntores na simulação: {e}", level="error")

    def on_paint_schematic(self, event):
        """Draw the power system schematic."""
        # Use BufferedPaintDC for double buffering to prevent flickering
        dc = wx.BufferedPaintDC(self.schematic_panel)

        # Create a memory DC to draw on the buffer
        memdc = wx.MemoryDC(self.schematic_buffer)
        # Set background color to black before clearing
        try:
            # Set background color
            memdc.SetBrush(wx.Brush(wx.BLACK))
            memdc.SetPen(wx.Pen(wx.BLACK))
            width, height = self.schematic_buffer.GetWidth(), self.schematic_buffer.GetHeight()
            memdc.DrawRectangle(0, 0, width, height)

            # Scale the DC for high-DPI displays
            memdc.SetUserScale(self.display_scale, self.display_scale)
        except Exception as e:
            log_message(f"Erro ao definir o fundo: {e}", level="error")

        # Draw on the memory DC
        self._draw_schematic(memdc)

        # Get panel size for blitting
        panel_width, panel_height = self.schematic_panel.GetSize()

        # Copy the buffer to the screen with scaling
        # Use high-quality scaling for better appearance
        dc.SetUserScale(1.0/self.display_scale, 1.0/self.display_scale)
        dc.Blit(0, 0, panel_width, panel_height, memdc, 0, 0)
        del memdc

    def _draw_schematic(self, dc):
        """Draw the power system schematic on the given DC."""
        # Clear the DC with black background
        try:
            # Alternative way to set background color
            dc.SetBrush(wx.Brush(wx.BLACK))
            dc.SetPen(wx.Pen(wx.BLACK))
            width, height = self.schematic_panel.GetSize()
            dc.DrawRectangle(0, 0, width, height)
        except Exception as e:
            log_message(f"Erro ao definir o fundo em _draw_schematic: {e}", level="error")

        # Draw main bus (moved 40px up)
        dc.SetPen(wx.Pen(wx.WHITE, 6))
        main_bus_start_x = 100
        main_bus_end_x = 800
        main_bus_y = 10
        dc.DrawLine(main_bus_start_x, main_bus_y, main_bus_end_x, main_bus_y)  # Y coordinate changed from 50 to 10
        dc.SetTextForeground(wx.WHITE)
        dc.SetFont(wx.Font(14, wx.FONTFAMILY_SWISS, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))

        # Calculate center position for the main bus label
        main_bus_label = "Barramento Principal 13,8kV"
        text_width, text_height = dc.GetTextExtent(main_bus_label)
        main_bus_center_x = main_bus_start_x + (main_bus_end_x - main_bus_start_x) / 2
        label_x = int(main_bus_center_x - text_width / 2)  # Center the text horizontally, convert to int
        dc.DrawText(main_bus_label, label_x, int(25))  # Y coordinate changed from 5 to 25, convert to int

        # Draw generators, secondary buses, and their connections (moved 60px up)
        # Coordinates with increased horizontal spacing for better display on RDP clients
        generator_coords = [(130, 190), (300, 190), (470, 190), (640, 190)]  # X coordinates with wider spacing
        generator_labels = ['A', 'B', 'C', 'D']

        for i, (x, y) in enumerate(generator_coords):
            # Define offsets for clarity (reduced for smaller drawing)
            sec_bus_y_offset = -70  # Changed from -80 to -70
            cb_main_y_offset = -45  # Changed from -25 to -45 (moved 20px higher)
            cb_gen_y_offset = 25    # Changed from 30 to 25

            # Draw secondary bus
            sec_bus_y = y + sec_bus_y_offset
            dc.SetPen(wx.Pen(wx.WHITE, 3))
            dc.DrawLine(x - 45, sec_bus_y, x + 45, sec_bus_y)  # Width reduced from 100 to 90
            dc.SetTextForeground(wx.WHITE)
            dc.SetFont(wx.Font(9, wx.FONTFAMILY_SWISS, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))  # Font size reduced
            dc.DrawText("Barramento Sec", x - 50, sec_bus_y - 30)  # First line of text moved 20px to the left and 5px higher
            dc.DrawText(generator_labels[i], x - 25, sec_bus_y - 17)  # Second line moved 20px to the left and 5px higher

            # Draw connections
            dc.SetPen(wx.Pen(wx.WHITE, 2))
            dc.DrawLine(x, 10, x, sec_bus_y)  # Main bus Y coordinate changed from 50 to 10
            dc.DrawLine(x, sec_bus_y, x, y - 25)  # Generator connection

            # Draw main circuit breaker (CB1-CB4)
            dc.SetFont(wx.Font(9, wx.FONTFAMILY_SWISS, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))  # Font size reduced
            dc.DrawText(f"CB{i+1}", x + 10, sec_bus_y + cb_main_y_offset - 5)

            dc.SetPen(wx.Pen(wx.RED if self.breaker_status[i] else wx.GREEN, 3))
            dc.SetBrush(wx.Brush(wx.RED if self.breaker_status[i] else wx.GREEN))
            dc.DrawRectangle(x - 4, sec_bus_y + cb_main_y_offset, 8, 8)  # Size reduced from 10x10 to 8x8

            # Draw generator circuit breaker (CB5-CB8)
            dc.SetPen(wx.Pen(wx.RED if self.breaker_status[i + 4] else wx.GREEN, 3))
            dc.SetBrush(wx.Brush(wx.RED if self.breaker_status[i + 4] else wx.GREEN))
            dc.DrawRectangle(x - 4, sec_bus_y + cb_gen_y_offset, 8, 8)  # Size reduced from 10x10 to 8x8
            dc.SetTextForeground(wx.WHITE)
            dc.DrawText(f"CB{i+5}", x + 10, sec_bus_y + cb_gen_y_offset)

            # Draw generator
            is_connected = self.breaker_status[i + 4]
            dc.SetPen(wx.Pen(wx.GREEN if is_connected else wx.RED, 3))
            dc.SetBrush(wx.Brush(wx.GREEN if is_connected else wx.RED))
            dc.DrawCircle(x, y, 25)  # Size reduced from 30 to 25
            dc.SetTextForeground(wx.BLACK)
            dc.SetFont(wx.Font(9, wx.FONTFAMILY_SWISS, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))  # Font size matched to CB labels
            dc.DrawText(generator_labels[i], x - 5, y - 8)  # Y offset changed from -10 to -8

            # Add mode indicator (SYN or DROOP) to the left of the generator
            if i < len(self.generators):
                mode_text = "SYN" if self.generators[i].mode == "synchronous" else "DROOP"
                # Brighter, more contrasting colors for better visibility
                mode_color = wx.Colour(0, 255, 255) if self.generators[i].mode == "synchronous" else wx.Colour(255, 255, 0)

                # Draw a small dark background rectangle for the text to make it stand out
                text_width = 40  # Approximate width for the text
                text_height = 16  # Approximate height for the text
                dc.SetPen(wx.Pen(wx.BLACK, 1))
                dc.SetBrush(wx.Brush(wx.Colour(30, 30, 30)))  # Dark gray background
                dc.DrawRectangle(x - 85, y - 8, text_width, text_height)

                # Draw the text with increased font size
                dc.SetTextForeground(mode_color)
                dc.SetFont(wx.Font(10, wx.FONTFAMILY_SWISS, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))  # Increased font size
                dc.DrawText(mode_text, x - 80, y - 8)  # Positioned to the left of the generator

                # Add damping factor and inertia display for Turbine A only
                if i == 0:  # Generator A
                    # Get damping factor and inertia values
                    damping_factor = self.generators[i].damping_factor
                    inertia = self.generators[i].inertia

                    # Create a background panel for the values
                    panel_width = 60
                    panel_height = 40
                    panel_x = x - 120  # Position to the left of the generator, not overlapping with mode indicator
                    panel_y = y - 30   # Positioned slightly above the generator

                    # Draw background panel
                    dc.SetPen(wx.Pen(wx.WHITE, 1))
                    dc.SetBrush(wx.Brush(wx.Colour(40, 40, 40)))  # Dark gray background
                    dc.DrawRectangle(panel_x, panel_y, panel_width, panel_height)

                    # Draw damping factor on first line
                    dc.SetTextForeground(wx.Colour(255, 200, 0))  # Orange-yellow text
                    dc.SetFont(wx.Font(10, wx.FONTFAMILY_SWISS, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))
                    dc.DrawText(f"D: {damping_factor:.1f}", panel_x + 5, panel_y + 5)

                    # Draw inertia on second line
                    dc.SetTextForeground(wx.Colour(0, 255, 200))  # Cyan-green text
                    dc.DrawText(f"H: {inertia:.1f}s", panel_x + 5, panel_y + 22)

            dc.SetTextForeground(wx.WHITE)
            dc.SetFont(wx.Font(11, wx.FONTFAMILY_SWISS, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))  # Font size reduced
            dc.DrawText(f"Gerador {generator_labels[i]}", x - 50, y + 30)  # Y offset changed from +35 to +30

            # Draw data panel with dark background - positioned to avoid overlapping
            # Position the data panel centered below the generator
            panel_width = 80  # Width for data panel
            panel_height = 50  # Height for data panel
            panel_x = x - panel_width // 2  # Center the panel horizontally under the generator
            panel_y = y + 50  # Position below the generator

            # Draw panel background with border
            dc.SetPen(wx.Pen(wx.WHITE, 2))
            dc.SetBrush(wx.Brush(wx.Colour(40, 40, 40)))  # Dark gray background
            dc.DrawRectangle(panel_x, panel_y, panel_width, panel_height)

            # Display generator data
            if i < len(self.generators) and is_connected:
                gen = self.generators[i]
                # Use the previous time index to avoid displaying uninitialized values
                # This ensures consistency with the graph display
                display_idx = max(0, self.time_index - 1)  # Ensure we don't go below 0
                idx = min(display_idx, len(gen.V_t) - 1) if len(gen.V_t) > 0 else 0

                if idx >= 0 and idx < len(gen.V_t):
                    # Calculate values directly from generator data
                    voltage_pu = gen.V_t[idx]
                    power_pu = gen.P_elec[idx]

                    # Convert voltage to kV
                    voltage = voltage_pu * self.simulation.config.v_base / 1000.0  # kV

                    # Calculate current directly
                    if voltage_pu > 0:
                        current = (power_pu * self.simulation.config.s_base) / (voltage_pu * self.simulation.config.v_base)
                    else:
                        current = 0.0

                    # Calculate frequency in Hz
                    frequency = gen.omega[idx] * self.simulation.config.f_base  # Hz

                    # Debug information removed to reduce console output

                    # Set font for all text (clear, readable font)
                    dc.SetFont(wx.Font(9, wx.FONTFAMILY_SWISS, FONT_STYLE_NORMAL, wx.FONTWEIGHT_BOLD))

                    # Calculate text positions for better alignment
                    text_margin = 5
                    line_height = 15

                    # Draw voltage with cyan color
                    dc.SetTextForeground(wx.CYAN)
                    dc.DrawText(f"V: {voltage:.2f} kV", panel_x + text_margin, panel_y + text_margin)

                    # Use yellow for current to make it more visible
                    dc.SetTextForeground(wx.YELLOW)
                    dc.DrawText(f"I: {current:.1f} A", panel_x + text_margin, panel_y + text_margin + line_height)

                    # Use light green for frequency
                    dc.SetTextForeground(wx.Colour(0, 255, 0))  # Light green
                    dc.DrawText(f"f: {frequency:.1f} Hz", panel_x + text_margin, panel_y + text_margin + 2*line_height)

                    # Removed redundant refresh call

    def on_bus_select(self, event):
        """Handle bus selection events."""
        selection = self.bus_selector.GetSelection()
        if selection != self.current_bus_idx:
            self.current_bus_idx = selection
            print(f"Barramento selecionado: {chr(65 + self.current_bus_idx)}")

            # Update graph panel's bus index
            self.graph_panel.set_bus_index(self.current_bus_idx)

            # Update bus parameters panel with the selected bus
            self.bus_params_panel.bus = self.secondary_buses[self.current_bus_idx]
            self.bus_params_panel.update_values()
            print(f"Parâmetros do barramento atualizados para Barramento {chr(65 + self.current_bus_idx)}")

    def on_config(self, event):
        """Handle configuration button events."""
        dlg = ConfigDialog(self, self.generators, self.main_bus)
        if dlg.ShowModal() == wx.ID_OK:
            print("Configuração atualizada.")
            # Refresh UI to reflect changes
            self.schematic_panel.Refresh()
            # Force graph update
            if hasattr(self.graph_panel, "last_update_time"):
                self.graph_panel.last_update_time = -1
            self.graph_panel.update_plots(self.time_index)

    def on_generator_config(self, event):
        """Handle generator configuration button events."""
        dlg = GeneratorConfigDialog(self, self.generators)
        if dlg.ShowModal() == wx.ID_OK:
            # Get modified parameters
            modified_params = dlg.get_modified_parameters()
            if modified_params:
                print("Configuração do gerador atualizada.")
                # Apply changes to generators
                for gen_name, params in modified_params.items():
                    # Find the generator
                    gen = next((g for g in self.generators if g.name == gen_name), None)
                    if gen:
                        # Update generator parameters
                        for param_name, value in params.items():
                            setattr(gen, param_name, value)
                        print(f"Parâmetros do gerador {gen_name} atualizados: {params}")

                # Refresh UI to reflect changes
                self.schematic_panel.Refresh()
                # Force graph update
                if hasattr(self.graph_panel, "last_update_time"):
                    self.graph_panel.last_update_time = -1
                self.graph_panel.update_plots(self.time_index)
                # Update generator info panel
                self.generator_info_panel.update(self.time_index)

    def on_breaker_config(self, event):
        """Handle breaker configuration button events."""
        dlg = BreakerConfigDialog(self, self.breaker_status)
        if dlg.ShowModal() == wx.ID_OK:
            print("Configuração dos disjuntores atualizada.")
            # Copy updated breaker status
            self.breaker_status = dlg.breaker_status.copy()
            # Update simulation with new breaker status
            self._update_simulation_breakers()
            # Refresh UI to reflect changes
            self.schematic_panel.Refresh()
            # Force graph update
            if hasattr(self.graph_panel, "last_update_time"):
                self.graph_panel.last_update_time = -1
            self.graph_panel.update_plots(self.time_index)

    def on_timer(self, event):
        """Handle timer events for simulation stepping."""
        # Check if simulation has finished
        if self.simulation.is_finished():
            # Update the elapsed time one last time with the final time value
            final_time_str = self._format_elapsed_time(self.simulation.current_time_index)
            self.time_counter.SetLabel(final_time_str)
            print(f"Simulação finalizada no tempo {final_time_str}, parando temporizador.")

            # Only perform a final UI update if we haven't already done so
            if not hasattr(self, 'final_update_done'):
                print("Realizando atualização final da interface na conclusão da simulação")

                # Make sure we don't have any stale flags
                if hasattr(self.graph_panel, 'final_render_done'):
                    print("Removendo flag final_render_done obsoleta")
                    delattr(self.graph_panel, 'final_render_done')

                # Update all UI components with final data
                # The force_redraw=True parameter will ensure a single complete redraw
                # This will update the data and perform a single redraw
                self.update_all_ui_components(force_redraw=True)

                # Set flag to prevent multiple final updates
                self.final_update_done = True
                print("Atualização final concluída e marcada como feita")

            self.timer.Stop()
            return

        # Run multiple simulation steps per timer event for better performance
        # Increased steps per update for RDP optimization
        steps_per_update = 10  # Run 10 simulation steps per timer event for better RDP performance
        for step in range(steps_per_update):
            # Skip if simulation has finished
            if self.simulation.is_finished():
                break

            # Advance simulation by one step
            success = self.simulation.run_step()
            if not success:
                print(f"Passo de simulação falhou no passo {step+1} de {steps_per_update}")
                break

        # Update time index with the simulation's current time index
        self.time_index = self.simulation.current_time_index

        # Only print debug info occasionally
        if self.time_index % 100 == 0:  # Further reduced frequency of debug output
            print(f"Timer event: time_index = {self.time_index}")

        # Always update the elapsed time counter (lightweight operation)
        elapsed_time_str = self._format_elapsed_time(self.time_index)
        self.time_counter.SetLabel(elapsed_time_str)

        # Check if UI update is needed based on simulation state
        if self.simulation.is_ui_update_needed():
            self.update_all_ui_components()
            # Mark that UI has been updated
            self.simulation.mark_ui_updated()

    def update_all_ui_components(self, force_redraw=False):
        """Atualiza todos os componentes da interface com os dados atuais da simulação.

        Args:
            force_redraw: Se True, força o redesenho de todos os componentes
        """
        print(f"[UI UPDATE] update_all_ui_components called with force_redraw={force_redraw}, time_index={self.time_index}")

        # Check if this is the final update
        is_final_update = self.simulation.is_finished()
        print(f"[UI UPDATE] is_final_update={is_final_update}")

        # Check if we've already done a final render
        already_rendered = hasattr(self.graph_panel, 'final_render_done')
        print(f"[UI UPDATE] already_rendered={already_rendered}")

        # Get the current bus being displayed
        current_bus = chr(65 + self.graph_panel.bus_idx)
        print(f"[UI UPDATE] Current bus being displayed: Bus {current_bus}")

        # Only update the graph data if we haven't already done a final render
        # or if this is not a final update
        if not (is_final_update and already_rendered):
            print(f"[UI UPDATE] Updating graph data for Bus {current_bus}")
            # Update graph panel - data is updated but not necessarily redrawn
            # This is optimized for RDP clients to reduce screen updates
            self.graph_panel.update_plots(self.time_index)

            # For final update or forced redraw, ensure graphs are properly rendered
            # But only if we haven't already done a final render
            if (is_final_update or force_redraw) and not already_rendered:
                print(f"[UI UPDATE] Forcing complete redraw of Bus {current_bus} graphs (final={is_final_update}, forced={force_redraw})")

                try:
                    # Close any existing figures except our current one
                    if hasattr(plt, 'get_fignums') and plt.get_fignums():
                        print(f"[UI UPDATE] Cleaning up {len(plt.get_fignums())} existing figures")
                        current_figure = self.graph_panel.figure
                        # Verify that we have a valid figure before cleanup
                        if current_figure is not None:
                            for fig_num in plt.get_fignums():
                                fig = plt.figure(fig_num)
                                if fig != current_figure:
                                    plt.close(fig)
                except Exception as e:
                    print(f"[UI UPDATE] Error cleaning up figures: {e}")

                # Force the graph panel to redraw completely
                # Use the proper draw method for the WXAgg canvas
                # This ensures we only render the graphs that are actually visible
                self.graph_panel.canvas.draw()

                # Set flag to prevent multiple redraws
                self.graph_panel.final_render_done = True
                print(f"[UI UPDATE] Final render completed and marked as done for Bus {current_bus}")
        else:
            print(f"[UI UPDATE] Skipping graph update for Bus {current_bus} - already rendered final state")

        # Update information panels
        self.generator_info_panel.update(self.time_index)
        self.turbine_params_panel.update(self.time_index)
        self.avr_control_panel.update(self.time_index)
        self.loads_panel.update(self.time_index)

        # Refresh the schematic panel
        wx.CallAfter(self.schematic_panel.Refresh)

        # Print debug info
        if self.time_index % 100 == 0 or is_final_update:
            print(f"Updated all UI components at time_index = {self.time_index}")
            if is_final_update:
                print("This is the final UI update")

    def _format_elapsed_time(self, time_index):
        """Formata o tempo decorrido para exibição.

        Args:
            time_index: Índice de tempo atual

        Returns:
            String de tempo formatada em segundos com 2 casas decimais
        """
        try:
            # Obter o valor real de tempo do array de tempo da simulação
            if hasattr(self.simulation, 't') and self.simulation.t is not None:
                # Verificar se t é um array ou um escalar
                if isinstance(self.simulation.t, np.ndarray):
                    if time_index < len(self.simulation.t):
                        elapsed_time = float(self.simulation.t[time_index])
                    elif len(self.simulation.t) > 0:
                        # Se o índice estiver fora dos limites, mas o array não estiver vazio, use o último valor
                        elapsed_time = float(self.simulation.t[-1])
                        print(f"Time index {time_index} out of bounds, using last time value: {elapsed_time:.2f}s")
                    else:
                        elapsed_time = 0.0
                        print("Time array is empty, using 0.0s")
                else:
                    # Se t não for um array, use o valor diretamente
                    elapsed_time = float(self.simulation.t) * time_index
                    print(f"Using calculated time: {elapsed_time:.2f}s (t={self.simulation.t}, index={time_index})")
            else:
                # Se não houver atributo t, use o índice multiplicado pelo passo de tempo padrão
                elapsed_time = time_index * 0.01  # Passo de tempo padrão de 0.01s
                print(f"No time array found, using default time step: {elapsed_time:.2f}s")
        except Exception as e:
            # Em caso de erro, use um valor seguro
            elapsed_time = time_index * 0.01  # Passo de tempo padrão de 0.01s
            print(f"Error formatting time: {e}. Using default: {elapsed_time:.2f}s")

        # Formatar como segundos com 2 casas decimais
        return f"{elapsed_time:.2f} s"

    def _init_ui(self):
        """Initialize UI components."""
        log_message("Criando componentes da interface...", level="info")

        # Criar painel principal sem rolagem para garantir que o topo fique visível
        panel = wx.Panel(self)
        panel.SetBackgroundColour(wx.BLACK)  # Definir fundo preto
        main_vbox = wx.BoxSizer(wx.VERTICAL)

        # Criar sizer horizontal para a área de conteúdo principal
        content_sizer = wx.BoxSizer(wx.HORIZONTAL)

        # Criar sizer vertical para o lado esquerdo (esquemático e gráfico)
        left_sizer = wx.BoxSizer(wx.VERTICAL)

        # 1. Criar painel do esquemático (metade superior do lado esquerdo) com buffer duplo
        # Usar um tamanho fixo mais largo para o painel do esquemático para melhor ajuste em clientes RDP e evitar sobreposição
        self.schematic_panel = wx.Panel(panel, size=wx.Size(700, 350), style=wx.FULL_REPAINT_ON_RESIZE)
        self.schematic_panel.SetBackgroundColour(wx.BLACK)
        self.schematic_panel.SetMinSize(wx.Size(700, 300))  # Aumentar largura e altura mínimas para evitar sobreposição
        self.schematic_panel.Bind(wx.EVT_PAINT, self.on_paint_schematic)
        self.schematic_panel.Bind(wx.EVT_SIZE, self.on_schematic_size)

        # Criar buffer otimizado para clientes RDP
        # Usar um fator de escala mais conservador para melhor desempenho em RDP
        display_scale = min(wx.GetDisplayPPI()[0] / 96.0, 1.5)  # Limitar fator de escala a 1.5
        buffer_width = int(700 * display_scale)  # Aumentar largura para corresponder ao tamanho do painel
        buffer_height = int(350 * display_scale)  # Aumentar altura para corresponder ao tamanho do painel
        self.schematic_buffer = wx.Bitmap(buffer_width, buffer_height)
        self.display_scale = display_scale

        # Inicializar buffer com fundo preto
        try:
            dc = wx.MemoryDC(self.schematic_buffer)
            # Definir cor de fundo
            dc.SetBrush(wx.Brush(wx.BLACK))
            dc.SetPen(wx.Pen(wx.BLACK))
            dc.DrawRectangle(0, 0, buffer_width, buffer_height)
            # Escalar o DC para telas de alta resolução (DPI)
            dc.SetUserScale(display_scale, display_scale)
            del dc
            print(f"Buffer do esquemático de alta resolução inicializado com sucesso ({buffer_width}x{buffer_height}) com fator de escala {display_scale:.2f}")
        except Exception as e:
            log_message(f"Erro ao inicializar buffer do esquemático: {e}", level="error")

        # Adicionar o painel do esquemático com uma proporção fixa (0 significa tamanho fixo)
        # Usar wx.EXPAND para garantir que o painel preencha o espaço disponível
        left_sizer.Add(self.schematic_panel, 0, wx.EXPAND | wx.ALL, 0)

        # Adicionar uma linha separadora entre os painéis do esquemático e do gráfico
        separator = wx.StaticLine(panel, style=wx.LI_HORIZONTAL)
        separator.SetBackgroundColour(wx.BLACK)
        separator.SetForegroundColour(wx.Colour(100, 100, 100))  # Linha cinza escuro
        left_sizer.Add(separator, 0, wx.EXPAND | wx.ALL, 2)

        # 2. Criar painel do gráfico (metade inferior do lado esquerdo)
        self.graph_panel = GraphPanel(panel, self.simulation, self.current_bus_idx)
        # Adicionar o painel do gráfico com proporção=1 para que ocupe todo o espaço restante
        left_sizer.Add(self.graph_panel, 1, wx.EXPAND | wx.ALL, 0)

        # Adicionar lado esquerdo ao sizer de conteúdo
        content_sizer.Add(left_sizer, 3, wx.EXPAND | wx.ALL, 0)

        # Criar painel direito para abas e controles
        right_panel = wx.Panel(panel)
        right_panel.SetBackgroundColour(wx.BLACK)
        right_sizer = wx.BoxSizer(wx.VERTICAL)

        # Criar notebook (abas) para o lado direito
        notebook = wx.Notebook(right_panel)
        notebook.SetBackgroundColour(wx.Colour(40, 40, 40))

        # Painel de informações do gerador (primeira aba)
        self.generator_info_panel = GeneratorInfoPanel(notebook, self.generators, self.simulation)
        notebook.AddPage(self.generator_info_panel, "Informações Gerador")

        # Painel de parâmetros da turbina (segunda aba)
        self.turbine_params_panel = TurbineParamsPanel(notebook, self.generators, self.simulation)
        notebook.AddPage(self.turbine_params_panel, "Configuração Turbina")

        # Substituir os painéis antigos pelo novo painel unificado
        self.generator_control_panel = GeneratorControlPanel(notebook, self.generators, self.simulation)
        notebook.AddPage(self.generator_control_panel, "Controle do Gerador")

        # Adicionar o novo painel de controle do AVR
        self.avr_control_panel = AVRControlPanel(notebook, self.generators, self.simulation)
        notebook.AddPage(self.avr_control_panel, "Controle de Tensão")

        # Adicionar o novo painel de cargas
        self.loads_panel = LoadsPanel(notebook, self.simulation)
        notebook.AddPage(self.loads_panel, "Cargas")

        # Adicionar notebook ao sizer direito com proporção reduzida para clientes RDP
        right_sizer.Add(notebook, 2, wx.EXPAND | wx.ALL, 5)

        # Painel de parâmetros do barramento (parte inferior do lado direito) com proporção aumentada
        self.bus_params_panel = BusParametersPanel(right_panel, self.secondary_buses[self.current_bus_idx])
        right_sizer.Add(self.bus_params_panel, 3, wx.EXPAND | wx.ALL, 5)

        # Vincular evento do botão de configuração da turbina
        self.turbine_params_panel.Bind(wx.EVT_BUTTON, self.on_config, id=wx.ID_EDIT)

        right_panel.SetSizer(right_sizer)
        content_sizer.Add(right_panel, 1, wx.EXPAND | wx.ALL, 5)

        # Adicionar sizer de conteúdo ao sizer vertical principal
        main_vbox.Add(content_sizer, 1, wx.EXPAND | wx.ALL, 0)

        # 3. Criar painel de controle com seletor de barramento e botões
        control_panel = wx.Panel(panel)
        control_panel.SetBackgroundColour(wx.BLACK)  # Definir fundo preto
        control_hbox = wx.BoxSizer(wx.HORIZONTAL)

        # Seletor de barramento - alinhado pelo topo
        bus_label = wx.StaticText(control_panel, label="Barramento:")
        bus_label.SetForegroundColour(wx.WHITE)  # Texto branco para visibilidade em fundo preto
        control_hbox.Add(
            bus_label,
            0, wx.ALL | wx.ALIGN_TOP, 5  # Alterado para ALIGN_TOP
        )

        bus_choices = ['Barramento A', 'Barramento B', 'Barramento C', 'Barramento D']
        self.bus_selector = wx.ComboBox(control_panel, choices=bus_choices, style=wx.CB_READONLY)
        self.bus_selector.SetBackgroundColour(wx.Colour(40, 40, 40))  # Dark gray background
        self.bus_selector.SetForegroundColour(wx.WHITE)  # White text
        self.bus_selector.SetSelection(0)
        self.bus_selector.Bind(wx.EVT_COMBOBOX, self.on_bus_select)
        control_hbox.Add(self.bus_selector, 0, wx.ALL | wx.ALIGN_TOP, 5)  # Changed to ALIGN_TOP

        # Add spacer
        control_hbox.AddStretchSpacer(1)

        # Configuration buttons - aligned by bottom
        config_btn = wx.Button(control_panel, label="Governador/Turbina")
        config_btn.Bind(wx.EVT_BUTTON, self.on_config)
        control_hbox.Add(config_btn, 0, wx.ALL | wx.ALIGN_BOTTOM, 5)

        gen_config_btn = wx.Button(control_panel, label="Gerador")
        gen_config_btn.Bind(wx.EVT_BUTTON, self.on_generator_config)
        control_hbox.Add(gen_config_btn, 0, wx.ALL | wx.ALIGN_BOTTOM, 5)

        breaker_btn = wx.Button(control_panel, label="Disjuntores")
        breaker_btn.Bind(wx.EVT_BUTTON, self.on_breaker_config)
        control_hbox.Add(breaker_btn, 0, wx.ALL | wx.ALIGN_BOTTOM, 5)

        # Add another spacer
        control_hbox.AddStretchSpacer(1)

        # Add elapsed time counter with large font
        time_label = wx.StaticText(control_panel, label="Tempo:")
        time_label.SetFont(wx.Font(12, wx.FONTFAMILY_SWISS, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        time_label.SetForegroundColour(wx.WHITE)  # White text for visibility on black background
        control_hbox.Add(time_label, 0, wx.ALL | wx.ALIGN_BOTTOM, 5)

        # Create the time counter with a large, bold font
        self.time_counter = wx.StaticText(control_panel, label="0.00 s")
        self.time_counter.SetFont(wx.Font(24, wx.FONTFAMILY_SWISS, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        self.time_counter.SetForegroundColour(wx.Colour(0, 0, 200))  # Blue color for emphasis
        control_hbox.Add(self.time_counter, 0, wx.ALL | wx.ALIGN_BOTTOM, 5)

        # Add UI update frequency control
        update_freq_label = wx.StaticText(control_panel, label="Atualização:")
        update_freq_label.SetForegroundColour(wx.WHITE)
        control_hbox.Add(update_freq_label, 0, wx.ALL | wx.ALIGN_CENTER_VERTICAL, 5)

        # Create a choice control for update frequency
        update_choices = ['Apenas Final', 'Cada 50 passos', 'Cada 100 passos', 'Cada 200 passos']
        self.update_freq_choice = wx.Choice(control_panel, choices=update_choices)
        self.update_freq_choice.SetSelection(0)  # Default to 'Apenas Final'
        self.update_freq_choice.Bind(wx.EVT_CHOICE, self.on_update_freq_change)
        control_hbox.Add(self.update_freq_choice, 0, wx.ALL | wx.ALIGN_CENTER_VERTICAL, 5)

        # Add Refresh UI button
        refresh_ui_btn = wx.Button(control_panel, label="Atualizar")
        refresh_ui_btn.Bind(wx.EVT_BUTTON, self.on_refresh_ui)
        refresh_ui_btn.SetBackgroundColour(wx.Colour(0, 100, 200))  # Blue background
        refresh_ui_btn.SetForegroundColour(wx.WHITE)  # White text
        control_hbox.Add(refresh_ui_btn, 0, wx.ALL | wx.ALIGN_BOTTOM, 5)

        # Add Current Values button
        current_values_btn = wx.Button(control_panel, label="Valores")
        current_values_btn.Bind(wx.EVT_BUTTON, self.on_show_current_values)
        control_hbox.Add(current_values_btn, 0, wx.ALL | wx.ALIGN_BOTTOM, 5)

        # Add Save Data button
        save_data_btn = wx.Button(control_panel, label="Salvar")
        save_data_btn.Bind(wx.EVT_BUTTON, self.on_save_data)
        control_hbox.Add(save_data_btn, 0, wx.ALL | wx.ALIGN_BOTTOM, 5)

        # Add Test PLC button
        test_plc_btn = wx.Button(control_panel, label="Testar PLC")
        test_plc_btn.Bind(wx.EVT_BUTTON, self.on_test_plc)
        test_plc_btn.SetBackgroundColour(wx.Colour(0, 120, 0))  # Green background
        test_plc_btn.SetForegroundColour(wx.WHITE)  # White text
        control_hbox.Add(test_plc_btn, 0, wx.ALL | wx.ALIGN_BOTTOM, 5)

        control_panel.SetSizer(control_hbox)
        main_vbox.Add(control_panel, 0, wx.EXPAND | wx.ALL, 5)

        panel.SetSizer(main_vbox)
        self.Centre()
    def on_update_freq_change(self, event):
        """Trata mudança de seleção da frequência de atualização."""
        selection = self.update_freq_choice.GetSelection()
        if selection == 0:  # Apenas Final
            self.simulation.ui_update_frequency = 0  # Atualizar apenas no final
        elif selection == 1:  # Cada 50 passos
            self.simulation.ui_update_frequency = 50
        elif selection == 2:  # Cada 100 passos
            self.simulation.ui_update_frequency = 100
        elif selection == 3:  # Cada 200 passos
            self.simulation.ui_update_frequency = 200

        print(f"Frequência de atualização da interface alterada para: {self.simulation.ui_update_frequency} passos")



    def on_refresh_ui(self, event):
        """Trata eventos do botão de atualização da interface."""
        log_message("Atualização manual da interface solicitada", level="info")

        # Reset all flags that might prevent updates
        if hasattr(self.graph_panel, 'final_render_done'):
            delattr(self.graph_panel, 'final_render_done')

        # Reset the final update flag
        if hasattr(self, 'final_update_done'):
            delattr(self, 'final_update_done')

        # Force UI update with explicit force_redraw=True
        # This will handle the redraw in a single call
        self.update_all_ui_components(force_redraw=True)

        # Mark that UI has been updated
        self.simulation.mark_ui_updated()

    def on_show_current_values(self, event):
        """Trata eventos do botão de exibição de valores atuais."""
        try:
            # Get the selected generator
            gen_idx = self.current_bus_idx
            if gen_idx < 0 or gen_idx >= len(self.generators):
                gen_idx = 0

            # Show the current values dialog
            dlg = CurrentValuesDialog(self, self.generators[gen_idx])
            dlg.ShowModal()

        except Exception as e:
            # Show error message
            wx.MessageBox(
                f"Error showing current values: {e}",
                "Error",
                wx.OK | wx.ICON_ERROR
            )

    def on_save_data(self, event):
        """Trata eventos do botão de salvamento de dados."""
        try:
            # Save simulation data to CSV file
            filepath = self.simulation.save_data_to_csv()

            # Show success message
            wx.MessageBox(
                f"Dados da simulação salvos em:\n{filepath}",
                "Dados Salvos",
                wx.OK | wx.ICON_INFORMATION
            )
        except Exception as e:
            # Show error message
            wx.MessageBox(
                f"Erro ao salvar dados da simulação: {e}",
                "Erro ao Salvar",
                wx.OK | wx.ICON_ERROR
            )

    def on_test_plc(self, event):
        """Trata eventos do botão de teste do PLC."""
        try:
            # Obter o IP padrão do arquivo de configuração
            import json
            import os

            # Caminho para o arquivo de configuração
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config", "config.json")

            # Valor padrão caso não consiga ler o arquivo
            default_ip = "*************"

            try:
                # Ler o arquivo de configuração
                with open(config_path, 'r') as f:
                    config = json.load(f)

                # Obter o IP do PLC da configuração
                default_ip = config.get("plc", {}).get("ip", default_ip)
            except Exception as e:
                print(f"Erro ao ler IP do PLC do arquivo de configuração: {e}")

            # Criar diálogo para entrada do IP do PLC
            dlg = wx.TextEntryDialog(
                self,
                "Digite o endereço IP do PLC:",
                "Teste de Comunicação com PLC",
                default_ip  # Valor padrão do arquivo de configuração
            )

            # Se o usuário cancelar, retornar
            if dlg.ShowModal() != wx.ID_OK:
                dlg.Destroy()
                return

            # Obter o IP digitado
            plc_ip = dlg.GetValue().strip()
            dlg.Destroy()

            # Verificar se o IP é válido
            if not plc_ip:
                wx.MessageBox(
                    "Endereço IP inválido.",
                    "Erro",
                    wx.OK | wx.ICON_ERROR
                )
                return

            # Mostrar diálogo de progresso
            progress_dlg = wx.ProgressDialog(
                "Teste de Comunicação com PLC",
                "Testando conectividade com o PLC...",
                maximum=100,
                parent=self,
                style=wx.PD_APP_MODAL | wx.PD_AUTO_HIDE
            )
            progress_dlg.Update(10)

            # Importar a função de teste do PLC
            import sys
            import os

            # Adicionar o diretório pai ao path para importar a função
            parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            if parent_dir not in sys.path:
                sys.path.append(parent_dir)

            # Importar a função de teste
            from main import test_plc_communication

            # Atualizar progresso
            progress_dlg.Update(30, "Executando teste de comunicação...")

            # Executar o teste
            results = test_plc_communication(plc_ip=plc_ip, timeout=2)

            # Atualizar progresso
            progress_dlg.Update(90, "Teste concluído. Gerando relatório...")

            # Fechar diálogo de progresso
            progress_dlg.Destroy()

            # Criar relatório detalhado
            report = "RESULTADOS DO TESTE DE COMUNICAÇÃO COM O PLC\n"
            report += "="*50 + "\n"
            report += f"Status geral: {results['overall_status']}\n"
            report += f"Ping: {'Sucesso' if results['ping_success'] else 'Falha'}\n"
            report += f"Conexão: {'Sucesso' if results['connection_success'] else 'Falha'}\n"
            report += f"Leitura: {'Sucesso' if results['read_success'] else 'Falha'}\n"
            report += "\nDetalhes:\n"

            for key, value in results["details"].items():
                report += f"  {key}: {value}\n"

            # Determinar o ícone com base no status geral
            icon = wx.ICON_INFORMATION if results["overall_status"] == "Sucesso" else wx.ICON_WARNING

            # Mostrar resultados
            result_dlg = wx.MessageDialog(
                self,
                report,
                f"Teste de Comunicação com PLC ({plc_ip})",
                wx.OK | icon
            )
            result_dlg.ShowModal()
            result_dlg.Destroy()

        except ImportError as e:
            wx.MessageBox(
                f"Erro ao importar módulo necessário: {e}\n\nVerifique se a biblioteca pylogix está instalada.",
                "Erro de Importação",
                wx.OK | wx.ICON_ERROR
            )
        except Exception as e:
            wx.MessageBox(
                f"Erro ao testar comunicação com o PLC: {e}",
                "Erro",
                wx.OK | wx.ICON_ERROR
            )

    def on_schematic_size(self, event):
        """Trata eventos de redimensionamento do painel do esquemático."""
        # Get the new size
        size = event.GetSize()

        # Create a new high-resolution buffer with the new size
        buffer_width = int(size.width * self.display_scale)
        buffer_height = int(size.height * self.display_scale)

        # Only recreate the buffer if the size has actually changed
        if (buffer_width != self.schematic_buffer.GetWidth() or
            buffer_height != self.schematic_buffer.GetHeight()):
            self.schematic_buffer = wx.Bitmap(buffer_width, buffer_height)
            print(f"Buffer do esquemático redimensionado para {buffer_width}x{buffer_height}")

        # Refresh the panel to redraw with the new size
        self.schematic_panel.Refresh()
        event.Skip()

    def update_time_index(self, new_index):
        """
        Atualiza o índice de tempo para a simulação.

        Este método é chamado de fora para atualizar o índice de tempo,
        que é usado para exibir dados no passo de tempo correto.

        Args:
            new_index: O novo índice de tempo
        """
        self.time_index = new_index

        # Skip update if simulation is finished and we've already done a final update
        if self.simulation.is_finished() and hasattr(self, 'final_update_done'):
            print("Pulando chamada redundante de update_time_index - simulação já finalizada")
            return

        # Use our centralized UI update method
        self.update_all_ui_components()
        # Mark that UI has been updated
        self.simulation.mark_ui_updated()

    def _create_generator_panel(self, parent, generator, idx):
        """Cria um painel para um gerador com controles e indicadores."""
        panel = wx.Panel(parent)
        sizer = wx.BoxSizer(wx.VERTICAL)

        # Generator name and mode
        name_sizer = wx.BoxSizer(wx.HORIZONTAL)
        name_label = wx.StaticText(panel, label=f"Gerador {generator.name}")
        name_label.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        name_sizer.Add(name_label, 0, wx.ALL, 5)

        # Add mode indicator
        mode_str = "SÍNCRONO" if generator.mode == "synchronous" else "DROOP"
        mode_color = wx.Colour(0, 0, 255) if generator.mode == "synchronous" else wx.Colour(0, 128, 0)
        mode_label = wx.StaticText(panel, label=f"Modo: {mode_str}")
        mode_label.SetForegroundColour(mode_color)
        mode_label.SetFont(wx.Font(10, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        name_sizer.Add(mode_label, 0, wx.ALL | wx.ALIGN_CENTER_VERTICAL, 5)

        # Add connection status
        connected = self.breaker_status[idx + 4]  # Assuming breakers 4-7 correspond to generators A-D
        status_str = "CONNECTED" if connected else "DISCONNECTED"
        status_color = wx.Colour(0, 128, 0) if connected else wx.Colour(255, 0, 0)
        status_label = wx.StaticText(panel, label=f"Status: {status_str}")
        status_label.SetForegroundColour(status_color)
        status_label.SetFont(wx.Font(10, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        name_sizer.Add(status_label, 0, wx.ALL | wx.ALIGN_CENTER_VERTICAL, 5)

        sizer.Add(name_sizer, 0, wx.EXPAND)

        # Rest of the generator panel code...

        panel.SetSizer(sizer)
        return panel
