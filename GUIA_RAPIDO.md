# 🚀 Guia Rápido - Sistema de Simulação Elétrica

## ⚡ <PERSON><PERSON><PERSON> (5 minutos)

### 1. Instal<PERSON><PERSON>
```bash
pip install numpy matplotlib wxPython pandas
```

### 2. Primeira Execução
```bash
python main.py
```

**Pronto!** O sistema irá:
- ✅ Criar automaticamente as configurações padrão
- ✅ Migrar configurações antigas (se existirem)
- ✅ Abrir a interface gráfica
- ✅ Iniciar a simulação

## 🎮 Controles Básicos

### Interface Principal
- **Seletor "Tipo de Gráfico"**: Escolha entre Potência, Frequência, Tensão, Corrente, Balanço Potência
- **Disjuntores CB1-CB8**: Clique para abrir/fechar
- **Botão "Salvar Dados"**: Salva a simulação atual
- **Informações dos Geradores**: Mostra parâmetros em tempo real

### Tipos de Gráfico
| Tipo | Descrição | Cor |
|------|-----------|-----|
| **Potência** | Potência elétrica dos geradores | Ciano |
| **Frequência** | Frequência do sistema | Laranja |
| **Tensão** | Tensão dos barramentos | Verde |
| **Corrente** | Corrente dos geradores | Vermelho |
| **Balanço Potência** | Diferença [Geração - Carga] | Amarelo |

## ⚙️ Configurações Rápidas

### Alterar Duração da Simulação
Edite `config/system_config.json`:
```json
{
  "system": {
    "duration": 30.0,   // ← Mude para 30 segundos (ou qualquer valor!)
    "dt": 0.01          // ← Passo de tempo (mantenha 0.01s)
  }
}
```

**Durações testadas:**
- ✅ **15s** (padrão) - Rápido e eficiente
- ✅ **30s** - Ideal para estudos detalhados
- ✅ **60s** - Para análise de transitórios longos
- ✅ **120s+** - Possível, mas monitore a memória

### Criar Perturbação no Gerador A
Edite `config/generators_config.json`:
```json
{
  "generators": [
    {
      "name": "A",
      "disturb_start": 5.0,     // Inicia aos 5s
      "disturb_end": 7.0,       // Termina aos 7s
      "disturb_value": -0.1     // Reduz 3 MW
    }
  ]
}
```

### Personalizar Interface
Edite `config/user_preferences.json`:
```json
{
  "default_graph_type": "Frequência",  // Gráfico padrão
  "window_maximized": true,            // Janela maximizada
  "legend_position": "below"           // Legenda abaixo
}
```

## 💾 Dados de Simulação

### Onde São Salvos
```
simulation_data/
├── 2024-01-15/
│   ├── simulation_2024-01-15_14-30-25_config.json  ← Configuração completa
│   ├── simulation_2024-01-15_14-30-25_data.csv     ← Dados da simulação
│   └── simulation_2024-01-15_14-30-25_summary.txt  ← Resumo legível
```

### Como Salvar
1. Execute a simulação
2. Clique em **"Salvar Dados"**
3. Digite uma descrição (opcional)
4. Os arquivos são salvos automaticamente

## 🔧 Solução de Problemas

### Erro de Configuração
```bash
python test_config.py  # Testa o sistema de configuração
```

### Resetar Configurações
1. Feche o programa
2. Delete a pasta `config/`
3. Execute `python main.py`
4. Configurações padrão serão recriadas

### Verificar Armazenamento
```bash
python main.py --test-config  # Mostra informações de armazenamento
```

### Modo Legacy (Compatibilidade)
```bash
python main.py --legacy  # Usa sistema antigo se houver problemas
```

**Quando usar Legacy:**
- ⚠️ Se o novo sistema apresentar problemas
- 🏭 Em ambiente de produção crítico
- 🔄 Durante transição gradual de configurações complexas

**Limitações do Legacy:**
- ❌ Sem recursos da versão 2.0
- ❌ Sem organização por data
- ❌ Sem preferências personalizáveis

## 📚 Cenários Comuns

### 🎓 Para Ensino
- Use **"Potência"** como gráfico padrão
- Configure `window_maximized: true` para projetor
- Desabilite limpeza automática (`auto_cleanup_enabled: false`)

### 🔬 Para Pesquisa
- Use **"Frequência"** para estudos de estabilidade
- Configure perturbações específicas nos geradores
- Salve dados regularmente para análise posterior

### 🏭 Para Análise Industrial
- Use **"Balanço Potência"** para estudos de carga
- Configure cargas realistas nos barramentos
- Ajuste parâmetros de inércia conforme o sistema real

## 🆘 Ajuda Rápida

| Problema | Solução |
|----------|---------|
| Interface não abre | Verifique se wxPython está instalado |
| Gráficos não atualizam | Reinicie a simulação |
| Configuração perdida | Execute `python test_config.py` |
| Dados não salvam | Verifique permissões da pasta `simulation_data/` |
| Performance lenta | Reduza `update_frequency` nas preferências |

## 📖 Documentação Completa

- **[README.md](README.md)** - Visão geral completa do sistema
- **[config_instructions.md](config_instructions.md)** - Guia detalhado de configuração
- **Teste do sistema**: `python test_config.py`

---

**💡 Dica**: Para usuários avançados, todos os parâmetros podem ser ajustados nos arquivos JSON da pasta `config/`. O sistema valida automaticamente as configurações e reporta erros de forma clara.
