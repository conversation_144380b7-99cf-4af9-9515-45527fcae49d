#!/usr/bin/env python3
"""
Matplotlib configuration utilities to prevent tick overflow warnings.
"""

import matplotlib
import matplotlib.pyplot as plt
import matplotlib.axes
from matplotlib.ticker import MaxNLocator, NullLocator, FixedLocator
import warnings

def configure_matplotlib_safely():
    """Configure matplotlib to prevent tick overflow warnings globally."""
    
    # Set global matplotlib parameters to prevent tick overflow
    matplotlib.rcParams.update({
        'axes.autolimit_mode': 'data',
        'axes.xmargin': 0.05,
        'axes.ymargin': 0.05,
        'figure.max_open_warning': 0,  # Disable figure warnings
    })
    
    # Filter matplotlib warnings about ticks
    warnings.filterwarnings('ignore', 
                          message='.*Locator attempting to generate.*ticks.*exceeds.*MAXTICKS.*',
                          category=UserWarning,
                          module='matplotlib')
    
    print("✅ Matplotlib configured safely to prevent tick overflow warnings")

def apply_safe_locators(ax, data_type='REAL', x_data_count=None):
    """Apply safe locators to an axis to prevent tick overflow.
    
    Args:
        ax: Matplotlib axis object
        data_type: 'REAL' or 'BOOL' 
        x_data_count: Number of data points for X-axis optimization
    """
    
    # Y-axis configuration based on data type
    if data_type == 'BOOL':
        # For boolean data, use fixed locators
        ax.yaxis.set_major_locator(FixedLocator([0, 1]))
        ax.yaxis.set_minor_locator(NullLocator())
        ax.set_ylim(-0.1, 1.1)
    else:
        # For real data, use conservative MaxNLocator
        ax.yaxis.set_major_locator(MaxNLocator(nbins=5, prune='both'))
        ax.yaxis.set_minor_locator(NullLocator())
    
    # X-axis configuration
    if x_data_count and x_data_count > 100:
        # For many data points, use fewer ticks
        ax.xaxis.set_major_locator(MaxNLocator(nbins=6, prune='both'))
    else:
        # For fewer data points, can use more ticks
        ax.xaxis.set_major_locator(MaxNLocator(nbins=8, prune='both'))
    
    ax.xaxis.set_minor_locator(NullLocator())

def create_safe_figure(figsize=(8, 4)):
    """Create a figure with safe default settings.
    
    Returns:
        fig, ax: Figure and axis with safe locator configuration
    """
    fig, ax = plt.subplots(figsize=figsize)
    
    # Apply safe default locators
    apply_safe_locators(ax)
    
    return fig, ax

def force_safe_locators_on_all_axes(fig):
    """Force safe locators on all axes in a figure.
    
    Args:
        fig: Matplotlib figure object
    """
    for ax in fig.get_axes():
        # Apply conservative locators to all axes
        ax.yaxis.set_major_locator(MaxNLocator(nbins=5, prune='both'))
        ax.yaxis.set_minor_locator(NullLocator())
        ax.xaxis.set_major_locator(MaxNLocator(nbins=6, prune='both'))
        ax.xaxis.set_minor_locator(NullLocator())

def patch_matplotlib_autoscale():
    """Patch matplotlib's autoscale to use safe locators."""
    
    # Store original autoscale method
    original_autoscale = matplotlib.axes.Axes.autoscale
    
    def safe_autoscale(self, enable=True, axis='both', tight=None):
        """Safe autoscale that applies conservative locators."""
        # Call original autoscale
        result = original_autoscale(self, enable, axis, tight)
        
        # Apply safe locators after autoscaling
        if axis in ['both', 'y']:
            self.yaxis.set_major_locator(MaxNLocator(nbins=5, prune='both'))
            self.yaxis.set_minor_locator(NullLocator())
        
        if axis in ['both', 'x']:
            self.xaxis.set_major_locator(MaxNLocator(nbins=6, prune='both'))
            self.xaxis.set_minor_locator(NullLocator())
        
        return result
    
    # Monkey patch the autoscale method
    matplotlib.axes.Axes.autoscale = safe_autoscale
    print("✅ Matplotlib autoscale patched for safety")

def setup_matplotlib_for_plc():
    """Complete matplotlib setup for PLC application."""
    print("🔧 Setting up matplotlib for PLC application...")

    # SOLUÇÃO RADICAL: Suprimir TODAS as warnings relacionadas a ticks
    warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
    warnings.filterwarnings('ignore', message='.*tick.*')
    warnings.filterwarnings('ignore', message='.*Locator.*')
    warnings.filterwarnings('ignore', message='.*MAXTICKS.*')
    warnings.filterwarnings('ignore', message='.*attempting to generate.*')
    warnings.filterwarnings('ignore', message='.*exceeds.*')

    # Configure matplotlib safely
    configure_matplotlib_safely()

    # Patch autoscale for safety
    patch_matplotlib_autoscale()

    # Patch matplotlib's warning system directly
    try:
        import matplotlib._api
        original_warn = matplotlib._api.warn_external

        def silent_warn(message, category=UserWarning):
            """Silent warning function that suppresses tick-related warnings."""
            if any(keyword in str(message).lower() for keyword in ['tick', 'locator', 'maxticks', 'attempting', 'exceeds']):
                return  # Suppress the warning
            return original_warn(message, category)

        matplotlib._api.warn_external = silent_warn
    except Exception:
        pass  # If patching fails, continue with other methods

    print("✅ Matplotlib setup complete - ALL tick warnings suppressed")

if __name__ == "__main__":
    # Test the configuration
    setup_matplotlib_for_plc()
    
    # Test with problematic data
    fig, ax = create_safe_figure()
    
    # Test boolean-like data that would cause overflow
    x_data = list(range(1000))
    y_data = [i % 2 for i in x_data]
    
    ax.plot(x_data, y_data)
    ax.set_ylim(-0.1, 1.1)
    
    # Apply safe locators
    apply_safe_locators(ax, data_type='BOOL', x_data_count=len(x_data))
    
    plt.close(fig)
    print("✅ Test completed without warnings")
