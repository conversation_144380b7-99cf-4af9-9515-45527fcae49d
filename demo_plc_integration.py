#!/usr/bin/env python3
"""
Demonstration of Enhanced PLC Integration for Power System Simulation.

This script shows how to use the new PLC features:
- Multiple variables monitoring
- Real-time graphing
- Flexible configuration
- Data export capabilities
"""

import sys
import time
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from core.configuration import ConfigurationManager
from core.plc_manager import EnhancedPLCManager
from main import create_power_system_from_config
from core.simulation import Simulation


def demo_plc_basic_functionality():
    """Demonstrate basic PLC functionality."""
    print("🔬 DEMO: Enhanced PLC Integration")
    print("=" * 60)
    
    try:
        # Initialize configuration
        print("📋 Loading configuration...")
        config_manager = ConfigurationManager()
        
        # Create power system
        print("⚡ Creating power system...")
        system = create_power_system_from_config(config_manager)
        simulation = Simulation(system)
        
        # Initialize PLC Manager
        print("🔌 Initializing Enhanced PLC Manager...")
        plc_manager = EnhancedPLCManager(simulation, "config/plc_config.json")
        
        # Show available variables
        print(f"\n📊 Available PLC Variables: {len(plc_manager.variables)}")
        
        # Show variables by category
        categories = ["generator", "bus", "system", "alarm", "control"]
        for category in categories:
            vars_in_category = plc_manager.get_variables_by_category(category)
            print(f"  {category.title()}: {len(vars_in_category)} variables")
        
        # Show plottable variables
        plottable_vars = plc_manager.get_plottable_variables()
        print(f"  Plottable (REAL): {len(plottable_vars)} variables")
        
        # Start PLC communication
        print("\n🚀 Starting PLC communication...")
        plc_manager.start()
        
        # Run simulation for a few seconds to collect data
        print("⏱️  Running simulation to collect PLC data...")
        for i in range(100):  # 10 seconds at 0.1s intervals
            simulation.run_step()
            time.sleep(0.01)  # Small delay to see real-time updates
            
            if i % 20 == 0:  # Print progress every 2 seconds
                stats = plc_manager.get_statistics()
                print(f"  Step {i}: {stats['successful_reads']} successful reads")
        
        # Show some collected data
        print("\n📈 Sample of collected data:")
        sample_tags = ["TGCPA_GSP.A54GNMW", "TGCPA_GSP.A54GNF", "BUS_MAIN.A59BUSMW"]
        
        for tag in sample_tags:
            if tag in plc_manager.variables:
                recent_data = plc_manager.data_buffer.get_recent_data(tag, 5)
                if recent_data:
                    latest = recent_data[-1]
                    print(f"  {tag}: {latest.value} ({latest.quality})")
                else:
                    print(f"  {tag}: No data")
        
        # Show statistics
        stats = plc_manager.get_statistics()
        print(f"\n📊 Communication Statistics:")
        print(f"  Total reads: {stats['total_reads']}")
        print(f"  Successful reads: {stats['successful_reads']}")
        print(f"  Success rate: {(stats['successful_reads']/max(stats['total_reads'], 1))*100:.1f}%")
        print(f"  Connection errors: {stats['connection_errors']}")
        
        # Stop PLC communication
        print("\n🛑 Stopping PLC communication...")
        plc_manager.stop()
        
        print("✅ Demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


def demo_plc_data_export():
    """Demonstrate PLC data export functionality."""
    print("\n🔬 DEMO: PLC Data Export")
    print("=" * 40)
    
    try:
        # This would demonstrate data export features
        print("📤 Data export functionality:")
        print("  • CSV export with timestamps")
        print("  • JSON export with metadata")
        print("  • Configurable time ranges")
        print("  • Quality indicators included")
        print("  (Implementation in progress...)")
        
    except Exception as e:
        print(f"❌ Export demo failed: {e}")


def demo_plc_configuration():
    """Demonstrate PLC configuration features."""
    print("\n🔬 DEMO: PLC Configuration")
    print("=" * 40)
    
    try:
        config_file = Path("config/plc_config.json")
        
        if config_file.exists():
            print("✅ PLC configuration file found")
            print(f"📁 Location: {config_file}")
            
            # Show configuration structure
            import json
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            print("🔧 Configuration sections:")
            for section in config.keys():
                if isinstance(config[section], dict):
                    print(f"  • {section}: {len(config[section])} settings")
                else:
                    print(f"  • {section}: {type(config[section]).__name__}")
        else:
            print("⚠️ PLC configuration file not found")
            print("   Run the main application first to create default config")
        
        # Show extended tags file
        tags_file = Path("tags_plc_extended.csv")
        if tags_file.exists():
            print(f"\n📋 Extended tags file found: {tags_file}")
            
            # Count lines
            with open(tags_file, 'r') as f:
                lines = f.readlines()
            
            # Count non-comment lines
            data_lines = [line for line in lines if not line.strip().startswith('#') and line.strip()]
            print(f"📊 Total variables defined: {len(data_lines) - 1}")  # -1 for header
            
            # Count by category
            categories = {}
            for line in data_lines[1:]:  # Skip header
                parts = line.split(',')
                if len(parts) > 7:
                    category = parts[7].strip()
                    categories[category] = categories.get(category, 0) + 1
            
            print("📈 Variables by category:")
            for category, count in categories.items():
                print(f"  • {category}: {count} variables")
        else:
            print("⚠️ Extended tags file not found")
        
    except Exception as e:
        print(f"❌ Configuration demo failed: {e}")


def show_plc_features():
    """Show overview of new PLC features."""
    print("\n🚀 NEW PLC FEATURES OVERVIEW")
    print("=" * 60)
    
    features = [
        "📊 Multiple Variable Types",
        "   • Generator data (power, frequency, voltage, temperature)",
        "   • Bus system variables (loads, voltages, frequencies)",
        "   • Environmental monitoring (temperature, humidity, pressure)",
        "   • Alarm systems (fire, gas, power, network)",
        "   • Control commands (breakers, setpoints)",
        "   • Maintenance data (operating hours, start counts)",
        "   • Performance metrics (efficiency, load factors)",
        "",
        "📈 Real-time Graphing",
        "   • Individual variable plots with time history",
        "   • Configurable time windows (5 min default)",
        "   • Auto-scaling and grid display",
        "   • Category-based organization",
        "   • Live value display with quality indicators",
        "",
        "⚙️ Flexible Configuration",
        "   • JSON-based configuration system",
        "   • Category-based variable grouping",
        "   • Configurable update frequencies",
        "   • Scaling factors and limits",
        "   • Enable/disable individual variables",
        "",
        "💾 Advanced Data Management",
        "   • Circular buffer for efficient storage",
        "   • Configurable buffer sizes",
        "   • Quality indicators (Good/Bad/Simulated)",
        "   • Timestamp precision",
        "   • Export capabilities (CSV, JSON)",
        "",
        "🔧 Enhanced Integration",
        "   • Seamless GUI integration",
        "   • Dashboard and configuration panels",
        "   • Statistics and monitoring",
        "   • Simulation mode for testing",
        "   • Error handling and recovery"
    ]
    
    for feature in features:
        print(feature)


if __name__ == "__main__":
    print("🔌 ENHANCED PLC INTEGRATION DEMONSTRATION")
    print("=" * 80)
    
    # Show features overview
    show_plc_features()
    
    # Run configuration demo
    demo_plc_configuration()
    
    # Run basic functionality demo
    demo_plc_basic_functionality()
    
    # Run data export demo
    demo_plc_data_export()
    
    print("\n" + "=" * 80)
    print("🎉 All demos completed!")
    print("\n💡 Next steps:")
    print("   1. Run 'python main.py' to see PLC panels in GUI")
    print("   2. Configure real PLC IP in config/plc_config.json")
    print("   3. Customize variables in tags_plc_extended.csv")
    print("   4. Use PLC Dashboard for real-time monitoring")
