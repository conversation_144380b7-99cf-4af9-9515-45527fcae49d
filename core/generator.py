"""
Classes de geradores para a simulação do sistema elétrico de potência.

<PERSON>ste módulo define a classe Generator e constantes relacionadas para os
diferentes modos de controle do gerador.
"""

import numpy as np
from typing import Dict, Any, Optional, TYPE_CHECKING

from core.constants import SystemConfig

if TYPE_CHECKING:
    from core.bus import SecondaryBus

# Modos de controle do gerador
MODE_SYNCHRONOUS = "synchronous"
MODE_DROOP = "droop"

# Estados do gerador
STATE_DISCONNECTED = "disconnected"      # Gerador desligado
STATE_CONNECTED = "connected"            # Gerador ligado mas não sincronizado
STATE_SYNCHRONIZED = "synchronized"      # Gerador sincronizado em vazio
STATE_LOADED = "loaded"                  # Gerador compartilhando carga

# SecondaryBus é importado em outras partes do código
# para evitar importações circulares


class Generator:
    """
    Gerador no sistema elétrico de potência.

    Esta classe representa um gerador com vários modos de controle e dinâmicas.

    Atributos:
        name (str): Identificador do gerador
        config (SystemConfig): Configuração do sistema
        mode (str): Modo de controle (síncrono, droop, isócrono)
        damping_factor (float): Fator de amortecimento para dinâmica de frequência
        droop_coefficient (float): Coeficiente de droop para controle droop
        inertia (float): Constante de inércia do gerador
        P_setpoint (float): Setpoint de potência ativa em por unidade
        V_setpoint (float): Setpoint de tensão em por unidade
        is_connected (bool): Se o gerador está conectado à rede
        bus (Optional[SecondaryBus]): Barramento ao qual o gerador está conectado
        omega (np.ndarray): Frequência angular em por unidade
        P_mech (np.ndarray): Potência mecânica em por unidade
        P_elec (np.ndarray): Potência elétrica em por unidade
        V_t (np.ndarray): Tensão terminal em por unidade
    """

    def __init__(self, name: str, mode: str, config: SystemConfig, params: Optional[Dict[str, Any]] = None):
        """
        Inicializa o gerador.

        Args:
            name: Identificador do gerador
            mode: Modo de controle (padrão: síncrono)
            config: Configuração do sistema
            params: Dicionário de parâmetros do gerador
        """
        # Define parâmetros padrão
        if params is None:
            params = {}

        # Parâmetros padrão do gerador, turbina, governador e AVR
        STANDARD_PARAMS = {
            # Parâmetros do conjunto turbina-gerador
            'H': 20.0,                # Constante de inércia do conjunto turbina-gerador (em segundos)
            'damping_factor': 30.0,    # Fator de amortecimento do conjunto

            # Parâmetros do controle de frequência
            'droop_coefficient': 0.04, # Coeficiente de droop padrão (4%)

            # Parâmetros de perturbação
            'disturb_start': 0.0,     # Tempo de início da perturbação (s)
            'disturb_end': 0.0,       # Tempo de fim da perturbação (s)
            'disturb_value': 0.0,     # Valor da perturbação (pu)

            # Parâmetros do governador (controle de frequência)
            'K_pgov': 12.0,           # Ganho proporcional do governador
            'K_igov': 2.0,            # Ganho integral do governador
            'K_dgov': 0.8,            # Ganho derivativo do governador

            # Parâmetros da turbina
            'K_turb': 1.2,            # Ganho da turbina
            'T_turb': 0.15,           # Constante de tempo da turbina (s)
            'W_fnl': 0.15,            # Vazão de combustível em vazio

            # Parâmetros de limitação do governador
            'deadband': 0.001,        # Banda morta do governador (região onde não há resposta)
            'saturation_threshold': 0.05, # Limiar de saturação (onde a resposta começa a saturar)
            'saturation_gain': 0.5,   # Ganho de saturação (redução de ganho na região saturada)
            'emergency_threshold': 1.05, # Limiar de emergência para frequência (% acima do nominal)
            'emergency_action': 0.9,  # Fator de redução de potência em caso de emergência

            # Parâmetros do AVR (controle de tensão)
            'K_pavr': 10.0,           # Ganho proporcional do AVR
            'K_iavr': 1.0,            # Ganho integral do AVR
            'K_davr': 0.1,            # Ganho derivativo do AVR
            'T_avr': 0.05,            # Constante de tempo do AVR
            'V_max': 1.1,             # Limite máximo de tensão (pu)
            'V_min': 0.9              # Limite mínimo de tensão (pu)
        }

        # Extrai apenas os setpoints do dicionário
        P_setpoint = params.get('P_setpoint', 1.0)
        V_setpoint = params.get('V_setpoint', 1.0)

        # Extrai parâmetros do gerador
        damping_factor = STANDARD_PARAMS['damping_factor']
        inertia = STANDARD_PARAMS['H']  # H é a constante de inércia

        # Extrai parâmetros de controle de frequência
        droop_coefficient = STANDARD_PARAMS['droop_coefficient']

        # Extrai parâmetros da turbina usando valores padrão
        disturb_start = STANDARD_PARAMS['disturb_start']
        disturb_end = STANDARD_PARAMS['disturb_end']
        disturb_value = STANDARD_PARAMS['disturb_value']

        # Extrai parâmetros do governador
        K_pgov = STANDARD_PARAMS['K_pgov']
        K_igov = STANDARD_PARAMS['K_igov']
        K_dgov = STANDARD_PARAMS['K_dgov']

        # Extrai parâmetros da turbina
        K_turb = STANDARD_PARAMS['K_turb']
        T_turb = STANDARD_PARAMS['T_turb']
        W_fnl = STANDARD_PARAMS['W_fnl']

        # Extrai parâmetros de saturação e banda morta
        deadband = STANDARD_PARAMS['deadband']
        saturation_threshold = STANDARD_PARAMS['saturation_threshold']
        saturation_gain = STANDARD_PARAMS['saturation_gain']
        emergency_threshold = STANDARD_PARAMS['emergency_threshold']
        emergency_action = STANDARD_PARAMS['emergency_action']

        # Extrai parâmetros do AVR
        K_pavr = STANDARD_PARAMS['K_pavr']
        K_iavr = STANDARD_PARAMS['K_iavr']
        K_davr = STANDARD_PARAMS['K_davr']
        T_avr = STANDARD_PARAMS['T_avr']
        V_max = STANDARD_PARAMS['V_max']
        V_min = STANDARD_PARAMS['V_min']

        # Registra os parâmetros padronizados
        print(f"Generator {name}: Using standardized turbine parameters (H={inertia}, damping={damping_factor})")

        self.name = name
        self.config = config
        self.mode = mode.lower()
        self.damping_factor = damping_factor
        self.droop_coefficient = droop_coefficient
        self.droop = droop_coefficient  # Adiciona droop como um alias para droop_coefficient
        self.inertia = inertia
        self.H = inertia  # Adiciona H como um alias para inércia por consistência
        self.P_setpoint = P_setpoint
        self.V_setpoint = V_setpoint
        self.v_ref = V_setpoint  # Adiciona v_ref como um alias para V_setpoint
        self.freq_ref = 1.0  # Referência de frequência em pu (padrão: 1.0 = frequência nominal)

        # Define parâmetros da turbina
        self.disturb_start = disturb_start
        self.disturb_end = disturb_end
        self.disturb_value = disturb_value

        # Define parâmetros do governador (controle de frequência)
        self.K_pgov = K_pgov
        self.K_igov = K_igov
        self.K_dgov = K_dgov

        # Define parâmetros da turbina
        self.K_turb = K_turb
        self.T_turb = T_turb
        self.W_fnl = W_fnl

        # Define parâmetros de saturação e banda morta
        self.deadband = deadband
        self.saturation_threshold = saturation_threshold
        self.saturation_gain = saturation_gain
        self.emergency_threshold = emergency_threshold
        self.emergency_action = emergency_action

        # Define parâmetros do AVR (controle de tensão)
        self.K_pavr = K_pavr
        self.K_iavr = K_iavr
        self.K_davr = K_davr
        self.T_avr = T_avr
        self.V_max = V_max
        self.V_min = V_min

        self.is_connected = True
        self.state = STATE_DISCONNECTED  # Estado inicial: desconectado
        self.bus: Optional['SecondaryBus'] = None

        # Inicializa arrays para simulação no domínio do tempo
        # Estes serão redimensionados na simulação
        # Garante que todos os arrays são inicializados como arrays numpy, não como escalares
        self.omega = np.ones(1, dtype=np.float64)
        self.P_mech = np.ones(1, dtype=np.float64) * P_setpoint
        self.P_elec = np.ones(1, dtype=np.float64) * P_setpoint
        self.Q_elec = np.ones(1, dtype=np.float64) * 0.0  # Potência reativa
        self.V_t = np.ones(1, dtype=np.float64) * V_setpoint
        self.V_setpoint_history = np.ones(1, dtype=np.float64) * V_setpoint  # Histórico de setpoints de tensão

        # Atributos adicionais necessários para a simulação
        self.power_error = np.zeros(1, dtype=np.float64)
        self.frequency_error = np.zeros(1, dtype=np.float64)
        self.voltage_error = np.zeros(1, dtype=np.float64)

    def initialize_arrays(self, length: int) -> None:
        """
        Inicializa arrays para simulação no domínio do tempo.

        Args:
            length: Comprimento dos arrays
        """
        print(f"Generator {self.name}: Initializing arrays with length {length}")
        # Garante que todos os arrays são inicializados como arrays numpy, não como escalares
        self.omega = np.ones(length, dtype=np.float64)
        self.P_mech = np.ones(length, dtype=np.float64) * self.P_setpoint
        self.P_elec = np.ones(length, dtype=np.float64) * self.P_setpoint
        self.Q_elec = np.ones(length, dtype=np.float64) * 0.0  # Inicializa potência reativa
        self.V_t = np.ones(length, dtype=np.float64) * self.V_setpoint
        self.V_setpoint_history = np.ones(length, dtype=np.float64) * self.V_setpoint  # Inicializa histórico de setpoints
        self.power_error = np.zeros(length, dtype=np.float64)
        self.frequency_error = np.zeros(length, dtype=np.float64)
        self.voltage_error = np.zeros(length, dtype=np.float64)

        # Saída de depuração
        print(f"  Omega array initialized with {len(self.omega)} elements")
        print(f"  First value: {self.omega[0]}, Last value: {self.omega[-1]}")
        print(f"  Type of omega array: {type(self.omega)}, dtype: {self.omega.dtype}")

    def update_frequency(self, frequency: float) -> None:
        """
        Atualiza a frequência do gerador.

        Isto é chamado quando a frequência do barramento muda.

        Args:
            frequency: Nova frequência em por unidade
        """
        if self.is_connected:
            # Atualiza apenas se estiver conectado à rede
            self.omega[-1] = frequency

    def update_power(self, power: float) -> None:
        """
        Atualiza a potência elétrica do gerador.

        Isto é chamado quando a potência elétrica muda devido a mudanças de carga.

        Args:
            power: Nova potência elétrica em por unidade
        """
        if self.is_connected:
            # Atualiza apenas se estiver conectado à rede
            self.P_elec[-1] = power

    def update_voltage(self, voltage: float) -> None:
        """
        Atualiza a tensão terminal do gerador.

        Isto é chamado quando a tensão do barramento muda.

        Args:
            voltage: Nova tensão terminal em por unidade
        """
        if self.is_connected:
            # Atualiza apenas se estiver conectado à rede
            self.V_t[-1] = voltage

    def calculate_mechanical_power(self, time_index: int) -> float:
        """
        Calcula a potência mecânica com base no modo de controle.

        Args:
            time_index: Índice de tempo atual na simulação

        Returns:
            Potência mecânica em por unidade
        """
        if not self.is_connected:
            # Se não estiver conectado, a potência mecânica é zero
            return 0.0

        if self.mode == MODE_SYNCHRONOUS:
            # Modo síncrono: ajusta a potência para manter a frequência constante (isócrono)
            # Quando a frequência cai abaixo da nominal, aumenta a potência mecânica
            # Quando a frequência sobe acima da nominal, diminui a potência mecânica
            frequency_error = self.omega[time_index] - 1.0
            return self.P_elec[time_index] - self.damping_factor * frequency_error

        elif self.mode == MODE_DROOP:
            # Controle droop: P = P_setpoint - (1/R) * (f - f_nominal)
            frequency_error = self.omega[time_index] - 1.0
            return self.P_setpoint - (1.0 / self.droop_coefficient) * frequency_error

        else:
            # Padrão para potência constante
            return self.P_setpoint

    def calculate_frequency(self, time_index: int, dt: float) -> float:
        """
        Calcula a nova frequência com base na equação de oscilação.

        Args:
            time_index: Índice de tempo atual na simulação
            dt: Passo de tempo em segundos

        Returns:
            Nova frequência em por unidade
        """
        if not self.is_connected:
            # Se não estiver conectado, a frequência não muda
            return self.omega[time_index]

        # Equação de oscilação: dω/dt = (P_mech - P_elec) / (2H)
        power_imbalance = self.P_mech[time_index] - self.P_elec[time_index]
        acceleration = power_imbalance / (2.0 * self.inertia)

        # Integração de Euler
        new_omega = self.omega[time_index] + acceleration * dt

        return new_omega

    def step(self, time_index: int, dt: float) -> None:
        """
        Executa um passo de tempo na simulação.

        Args:
            time_index: Índice de tempo atual na simulação
            dt: Passo de tempo em segundos
        """
        if time_index + 1 >= len(self.omega):
            # Redimensiona arrays se necessário
            # Usa np.append com reshape para garantir que o resultado seja um array, não um escalar
            self.omega = np.append(self.omega, self.omega[-1]).reshape(-1)
            self.P_mech = np.append(self.P_mech, self.P_mech[-1]).reshape(-1)
            self.P_elec = np.append(self.P_elec, self.P_elec[-1]).reshape(-1)
            self.Q_elec = np.append(self.Q_elec, self.Q_elec[-1]).reshape(-1)
            self.V_t = np.append(self.V_t, self.V_t[-1]).reshape(-1)
            self.V_setpoint_history = np.append(self.V_setpoint_history, self.V_setpoint).reshape(-1)

            # Também redimensiona os arrays de erro
            self.power_error = np.append(self.power_error, 0.0).reshape(-1)
            self.frequency_error = np.append(self.frequency_error, 0.0).reshape(-1)
            self.voltage_error = np.append(self.voltage_error, 0.0).reshape(-1)

        # Calcula potência mecânica
        self.P_mech[time_index + 1] = self.calculate_mechanical_power(time_index)

        # Calcula nova frequência
        self.omega[time_index + 1] = self.calculate_frequency(time_index, dt)

        # Potência elétrica é atualizada pelo sistema elétrico de potência
        self.P_elec[time_index + 1] = self.P_elec[time_index]
        self.Q_elec[time_index + 1] = self.Q_elec[time_index]

        # Aplica controle de tensão (AVR) se o gerador estiver conectado
        if self.is_connected and self.state != STATE_DISCONNECTED:
            self.V_t[time_index + 1] = self._apply_avr_control(time_index, dt)
        else:
            self.V_t[time_index + 1] = self.V_t[time_index]

        # Atualiza erros - garante que estamos atualizando elementos de arrays, não escalares
        self.power_error[time_index + 1] = self.P_setpoint - self.P_elec[time_index + 1]
        self.frequency_error[time_index + 1] = 1.0 - self.omega[time_index + 1]
        self.voltage_error[time_index + 1] = self.V_setpoint - self.V_t[time_index + 1]

    def get_frequency_hz(self, time_index: int) -> float:
        """
        Obtém a frequência do gerador em Hz.

        Args:
            time_index: Índice de tempo na simulação

        Returns:
            Frequência do gerador em Hz
        """
        return self.omega[time_index] * self.config.f_base

    def get_power_mw(self, time_index: int) -> float:
        """
        Obtém a potência elétrica do gerador em MW.

        Args:
            time_index: Índice de tempo na simulação

        Returns:
            Potência elétrica do gerador em MW
        """
        return self.P_elec[time_index] * self.config.s_base / 1e6

    def get_voltage_kv(self, time_index: int) -> float:
        """
        Obtém a tensão terminal do gerador em kV.

        Args:
            time_index: Índice de tempo na simulação

        Returns:
            Tensão terminal do gerador em kV
        """
        return self.V_t[time_index] * self.config.v_base / 1000.0

    def update_generator_state(self, time_index: int, is_breaker_closed: bool) -> None:
        """
        Atualiza o estado do gerador com base no estado do disjuntor e condições do sistema.

        Args:
            time_index: Índice de tempo atual na simulação
            is_breaker_closed: Se o disjuntor do gerador está fechado
        """
        old_state = self.state

        # Atualiza o estado de conexão
        self.is_connected = is_breaker_closed

        # Determina o novo estado do gerador
        if not is_breaker_closed:
            self.state = STATE_DISCONNECTED
        else:
            # Gerador está conectado, determinar o estado específico
            if self.P_elec[time_index] < 0.01:  # Potência muito baixa
                if abs(self.omega[time_index] - 1.0) < 0.001:  # Frequência próxima da nominal
                    self.state = STATE_SYNCHRONIZED  # Sincronizado em vazio
                else:
                    self.state = STATE_CONNECTED  # Conectado mas não sincronizado
            else:
                self.state = STATE_LOADED  # Compartilhando carga

        # Se o estado mudou, registrar a mudança
        if old_state != self.state:
            print(f"Gerador {self.name}: Estado alterado de {old_state} para {self.state}")

        # Aplicar ações específicas com base no estado
        self._apply_state_actions(time_index)

    def _apply_state_actions(self, time_index: int) -> None:
        """
        Aplica ações específicas com base no estado atual do gerador.

        Args:
            time_index: Índice de tempo atual na simulação
        """
        if self.state == STATE_DISCONNECTED:
            # Redefine a potência elétrica para zero quando desconectado
            self.P_elec[time_index] = 0.0
            self.Q_elec[time_index] = 0.0

            # Quando desconectado, a potência mecânica deve começar a diminuir
            if time_index > 0:
                # Reduz gradualmente a potência mecânica (simulando resposta do governador)
                ramp_factor = 0.95  # Redução de 5% por passo de tempo
                self.P_mech[time_index] = self.P_mech[time_index - 1] * ramp_factor

                # A frequência mudará com base no desequilíbrio de potência
                power_imbalance = self.P_mech[time_index] - self.P_elec[time_index]
                acceleration = power_imbalance / (2.0 * self.inertia)
                dt = 0.01  # Passo de tempo em segundos
                self.omega[time_index] = self.omega[time_index - 1] + acceleration * dt
            else:
                self.P_mech[time_index] = self.P_setpoint
                self.omega[time_index] = 1.0

        elif self.state == STATE_CONNECTED:
            # Gerador conectado mas não sincronizado
            # Ajustar frequência para se aproximar da nominal
            if time_index > 0:
                freq_error = 1.0 - self.omega[time_index]
                # Aplicar controle para sincronizar
                correction = 0.1 * freq_error  # Fator de correção
                self.P_mech[time_index] = self.P_mech[time_index - 1] + correction

        elif self.state == STATE_SYNCHRONIZED:
            # Gerador sincronizado em vazio
            # Manter frequência nominal e potência baixa
            self.omega[time_index] = 1.0
            self.P_mech[time_index] = 0.05  # Potência mecânica mínima

        elif self.state == STATE_LOADED:
            # Gerador compartilhando carga
            # Controle normal já é aplicado em outras funções
            pass

    def reset_state(self, time_index: int) -> None:
        """
        Redefine o estado do gerador quando desconectado da rede.

        Args:
            time_index: Índice de tempo atual na simulação
        """
        # Atualiza para o estado desconectado
        self.update_generator_state(time_index, False)

        # Imprime mensagem de status
        print(f"Gerador {self.name} desconectado em t={time_index*0.01:.2f}s:")
        print(f"  P_elec definido como {self.P_elec[time_index]:.4f} pu")
        print(f"  P_mech definido como {self.P_mech[time_index]:.4f} pu")
        print(f"  Frequência definida como {self.omega[time_index]*60:.2f} Hz")

    def _apply_governor_control(self, time_index: int) -> None:
        """
        Aplica controle do governador para ajustar a potência mecânica.

        Args:
            time_index: Índice de tempo atual na simulação
        """
        # Calcula erro de frequência
        frequency_error = self.omega[time_index] - 1.0

        # Calcula erro de potência (diferença entre setpoint e potência elétrica real)
        power_error = self.P_setpoint - self.P_elec[time_index]

        # Inicializa erros anteriores e termos integrais se for o primeiro passo de tempo
        if not hasattr(self, 'prev_freq_error'):
            self.prev_freq_error = 0.0
            self.freq_integral_term = 0.0
            self.prev_power_error = 0.0
            self.power_integral_term = 0.0

        # Passo de tempo em segundos
        dt = 0.01

        if self.mode == MODE_DROOP:
            # Controle droop com controlador PID para frequência
            # Termo proporcional
            p_term = self.K_pgov * frequency_error

            # Termo integral
            self.freq_integral_term += self.K_igov * frequency_error * dt

            # Termo derivativo
            if time_index > 0:
                d_term = self.K_dgov * (frequency_error - self.prev_freq_error) / dt
            else:
                d_term = 0.0

            # Calcula saída do governador com base na característica droop
            # P = P_setpoint - (1/R) * (f - f_nominal)
            freq_correction = (1.0 / self.droop_coefficient) * (p_term + self.freq_integral_term + d_term)
            governor_output = self.P_setpoint - freq_correction

            # Atualiza potência mecânica
            self.P_mech[time_index] = governor_output

        elif self.mode == MODE_SYNCHRONOUS:
            # Modo síncrono (isócrono) foca em manter a frequência constante
            # Termo proporcional para frequência - ganho alto para controle preciso de frequência
            p_term_freq = 8.0 * self.K_pgov * frequency_error  # Aumentado de 5.0 para 8.0

            # Termo integral para frequência - integração muito mais forte para frequência
            self.freq_integral_term += 4.0 * self.K_igov * frequency_error * dt  # Aumentado de 3.0 para 4.0

            # Anti-windup para termo integral para evitar acúmulo excessivo - limites mais restritos
            self.freq_integral_term = max(-0.5, min(0.5, self.freq_integral_term))  # Limites reduzidos para evitar sobressinal

            # Termo derivativo para frequência - ação derivativa mais forte
            if time_index > 0:
                d_term_freq = 5.0 * self.K_dgov * (frequency_error - self.prev_freq_error) / dt  # Aumentado de 3.0 para 5.0
            else:
                d_term_freq = 0.0

            # Calcula saída do governador baseada principalmente no erro de frequência
            # Para geradores síncronos, ajustamos a potência mecânica para manter a frequência
            # em vez de corresponder a um setpoint de potência
            freq_correction = p_term_freq + self.freq_integral_term + d_term_freq

            # Aplica banda morta - não responde a erros muito pequenos
            if abs(frequency_error) <= self.deadband:
                # Dentro da banda morta, não aplica correção
                pass
            elif abs(frequency_error) > self.saturation_threshold:
                # Região de saturação - resposta não-linear com ganho reduzido
                # Calcula a direção do erro (positivo ou negativo)
                error_sign = 1.0 if frequency_error > 0 else -1.0

                # Aplica saturação: parte linear + parte saturada com ganho reduzido
                saturated_error = self.saturation_threshold + (abs(frequency_error) - self.saturation_threshold) * self.saturation_gain

                # Aplica a correção com a direção correta
                extra_correction = error_sign * saturated_error * saturated_error * self.K_pgov

                # Adiciona à correção de frequência
                if frequency_error > 0:
                    # Para frequência acima do nominal, subtrai para reduzir potência
                    freq_correction -= extra_correction
                else:
                    # Para frequência abaixo do nominal, adiciona para aumentar potência
                    freq_correction += extra_correction
            else:
                # Região linear normal - resposta quadrática proporcional ao erro
                extra_correction = frequency_error * abs(frequency_error) * self.K_pgov
                freq_correction += extra_correction

            # Ajusta potência mecânica com base na potência elétrica atual e correção de frequência
            # Usa potência elétrica como base e aplica correção de frequência
            governor_output = self.P_elec[time_index] + freq_correction

            # Limite adicional para geradores síncronos para evitar sobressinal de frequência
            # Se a frequência estiver acima da nominal, limita potência mecânica à potência elétrica
            if frequency_error > 0 and governor_output > self.P_elec[time_index]:
                governor_output = self.P_elec[time_index]

            # Saída de depuração para controle de gerador síncrono
            if time_index % 100 == 0:  # Registra a cada 100 passos
                print(f"Gerador Síncrono {self.name}: freq={self.omega[time_index]*60:.2f}Hz, erro_freq={frequency_error:.6f}, "
                      f"P_elec={self.P_elec[time_index]:.4f}, P_mech={governor_output:.4f}, corr_freq={freq_correction:.4f}")

            # Atualiza potência mecânica
            self.P_mech[time_index] = governor_output

        # Armazena erros atuais para a próxima iteração
        self.prev_freq_error = frequency_error
        self.prev_power_error = power_error

        # Limita potência mecânica a um intervalo válido
        self.P_mech[time_index] = max(0.0, min(1.0, self.P_mech[time_index]))

        # Saída de depuração para mudanças significativas
        if abs(self.P_mech[time_index] - self.P_elec[time_index]) > 0.05:
            print(f"Gerador {self.name} em t={time_index*0.01:.2f}s: P_mech={self.P_mech[time_index]:.4f}, P_elec={self.P_elec[time_index]:.4f}, P_setpoint={self.P_setpoint:.4f}, erro_freq={frequency_error:.6f}")

    def _apply_avr_control(self, time_index: int, dt: float) -> float:
        """
        Aplica controle de tensão (AVR) usando controlador PID.

        Args:
            time_index: Índice de tempo atual na simulação
            dt: Passo de tempo em segundos

        Returns:
            float: Nova tensão terminal em por unidade
        """
        # Calcula erro de tensão
        voltage_error = self.V_setpoint - self.V_t[time_index]

        # Inicializa variáveis de controle se for o primeiro passo
        if not hasattr(self, 'prev_voltage_error'):
            self.prev_voltage_error = 0.0
            self.voltage_integral_term = 0.0

        # Termo proporcional
        p_term = self.K_pavr * voltage_error

        # Termo integral
        self.voltage_integral_term += self.K_iavr * voltage_error * dt

        # Termo derivativo
        if time_index > 0:
            d_term = self.K_davr * (voltage_error - self.prev_voltage_error) / dt
        else:
            d_term = 0.0

        # Calcula saída do AVR
        avr_output = p_term + self.voltage_integral_term + d_term

        # Aplica dinâmica do AVR (modelo de primeira ordem)
        if time_index > 0:
            # Mudança na tensão: ΔV = (avr_output - V_prev) * (dt / T_avr)
            delta_V = (avr_output - self.V_t[time_index]) * (dt / self.T_avr)
            new_voltage = self.V_t[time_index] + delta_V
        else:
            new_voltage = self.V_t[time_index] + avr_output * dt

        # Limita tensão a um intervalo válido
        new_voltage = max(self.V_min, min(self.V_max, new_voltage))

        # Armazena erro atual para a próxima iteração
        self.prev_voltage_error = voltage_error

        # Registra mudanças significativas de tensão
        if abs(new_voltage - self.V_t[time_index]) > 0.01:
            print(f"Gerador {self.name}: Tensão alterada de {self.V_t[time_index]:.4f} pu para {new_voltage:.4f} pu")
            print(f"  Erro de tensão: {voltage_error:.4f} pu, Saída do AVR: {avr_output:.4f}")

        return new_voltage

    def _apply_turbine_dynamics(self, time_index: int) -> None:
        """
        Aplica dinâmica da turbina à potência mecânica.

        Args:
            time_index: Índice de tempo atual na simulação
        """
        # Verifica se estamos em um período de perturbação
        t = time_index * 0.01  # Converte índice de tempo para segundos

        # Aplica perturbação se estiver dentro do intervalo de tempo especificado
        if self.disturb_start <= t <= self.disturb_end and self.disturb_value != 0.0:
            # Aplica a perturbação à potência mecânica
            self.P_mech[time_index] += self.disturb_value
            print(f"Aplicando perturbação de {self.disturb_value:.4f} pu ao gerador {self.name} em t={t:.2f}s")

        # Aplica dinâmica da turbina usando modelo de primeira ordem
        # Mudança na potência mecânica: ΔP_mech = (K_turb * governor_out - P_mech_prev) * (dt / T_turb)
        if time_index > 0:
            dt = 0.01  # Passo de tempo em segundos
            governor_out = self.P_mech[time_index]  # Usa o valor definido pelo controle do governador

            # Aplica dinâmica da turbina
            delta_P_mech = (self.K_turb * (governor_out - self.W_fnl) - self.P_mech[time_index - 1]) * (dt / self.T_turb)
            self.P_mech[time_index] = self.P_mech[time_index - 1] + delta_P_mech

            # Limita potência mecânica a um intervalo válido
            self.P_mech[time_index] = max(0.0, min(1.0, self.P_mech[time_index]))

    def _calculate_frequency_dynamics(self, time_index: int) -> None:
        """
        Calcula a dinâmica de frequência com base na equação de oscilação.

        Args:
            time_index: Índice de tempo atual na simulação
        """
        # Equação de oscilação: dω/dt = (P_mech - P_elec) / (2H)
        if not self.is_connected:
            # Se não estiver conectado, o gerador está isolado e responderá ao seu próprio desequilíbrio de potência
            if time_index > 0:
                # Calcula desequilíbrio de potência (P_elec é 0 quando desconectado)
                power_imbalance = self.P_mech[time_index] - self.P_elec[time_index]

                # Calcula aceleração - será positiva se P_mech > 0, causando aumento de frequência
                acceleration = power_imbalance / (2.0 * self.inertia)

                # Integração de Euler
                dt = 0.01  # Passo de tempo em segundos
                self.omega[time_index] = self.omega[time_index - 1] + acceleration * dt

                # Adiciona algum amortecimento para evitar aumento irreal de frequência
                # Isso simula a resposta do governador de velocidade
                if self.omega[time_index] > self.emergency_threshold:  # Acima do limiar de emergência
                    # Aplica ação de emergência do governador
                    self.P_mech[time_index] *= self.emergency_action  # Reduz potência mecânica conforme configurado

                    # Registra a ação de emergência
                    print(f"Ação de emergência: Frequência do gerador {self.name} de {self.omega[time_index]*60:.2f} Hz excede o limite. Reduzindo potência para {self.P_mech[time_index]:.4f} pu")

                    # Recalcula frequência com nova potência mecânica
                    power_imbalance = self.P_mech[time_index] - self.P_elec[time_index]
                    acceleration = power_imbalance / (2.0 * self.inertia)
                    self.omega[time_index] = self.omega[time_index - 1] + acceleration * dt
            return

        # Para geradores conectados, aplica-se a equação de oscilação normal
        # Verifica se o gerador está conectado a um barramento com carga
        if hasattr(self, 'bus') and self.bus is not None and hasattr(self.bus, 'load_p'):
            # Converte carga de watts para por unidade
            load_pu = self.bus.load_p / self.config.s_base

            # Para geradores síncronos, rastreamos a carga para fins informativos
            # mas não ajustamos o setpoint, pois eles regulam frequência, não potência
            if self.mode == MODE_SYNCHRONOUS and abs(self.P_elec[time_index] - load_pu) > 0.05:
                print(f"Gerador {self.name} (Síncrono): Carga alterada para {load_pu:.4f} pu, P_elec é {self.P_elec[time_index]:.4f} pu")

            # Registra mudanças significativas de carga
            if abs(self.P_elec[time_index] - load_pu) > 0.05:
                print(f"Gerador {self.name}: Carga é {load_pu:.4f} pu, P_elec é {self.P_elec[time_index]:.4f} pu")

        # Calcula o desequilíbrio de potência
        power_imbalance = self.P_mech[time_index] - self.P_elec[time_index]

        # Termo de amortecimento padrão - proporcional ao desvio de frequência
        # Este é o modelo de amortecimento padrão para este tipo de turbina
        standard_damping = self.damping_factor * (self.omega[time_index] - 1.0)

        # O desequilíbrio líquido inclui tanto o desequilíbrio de potência quanto o amortecimento
        net_imbalance = power_imbalance - standard_damping

        # Calcula a aceleração usando a equação de oscilação padrão
        # Todas as turbinas têm a mesma constante de inércia, pois são do mesmo modelo
        acceleration = net_imbalance / (2.0 * self.inertia)

        # Integração de Euler
        if time_index > 0:
            dt = 0.01  # Passo de tempo em segundos

            # CORREÇÃO: Para geradores em modo droop, a frequência deve seguir o barramento
            # quando conectados ao sistema. Isso simula o comportamento real de geradores
            # síncronos conectados ao mesmo barramento.
            if self.mode == MODE_DROOP and self.is_connected and self.state == STATE_LOADED:
                # Encontra o gerador síncrono no sistema para usar sua frequência
                sync_gen_found = False

                # Tenta encontrar um gerador síncrono conectado ao mesmo barramento
                if hasattr(self, 'bus') and self.bus is not None:
                    for gen in self.bus.generators:
                        if gen.mode == MODE_SYNCHRONOUS and gen.is_connected and gen.state == STATE_LOADED:
                            # Usa a frequência do gerador síncrono
                            self.omega[time_index] = gen.omega[time_index]
                            sync_gen_found = True
                            break

                # Se não encontrou um gerador síncrono, aplica a equação de oscilação normal
                if not sync_gen_found:
                    self.omega[time_index] = self.omega[time_index - 1] + acceleration * dt
            else:
                # Para geradores síncronos ou desconectados, aplica a equação de oscilação normal
                self.omega[time_index] = self.omega[time_index - 1] + acceleration * dt

            # Registra mudanças significativas de frequência
            if abs(self.omega[time_index] - self.omega[time_index - 1]) > 0.001:
                print(f"Gerador {self.name}: Frequência alterada de {self.omega[time_index-1]*60:.2f} Hz para {self.omega[time_index]*60:.2f} Hz")
                print(f"  Desequilíbrio de potência: {power_imbalance:.4f} pu, Amortecimento: {standard_damping:.4f} pu, Líquido: {net_imbalance:.4f} pu")

    def __str__(self) -> str:
        """Retorna uma representação em string do gerador."""
        return (f"Gerador(nome={self.name}, "
                f"modo={self.mode}, "
                f"P_setpoint={self.P_setpoint:.4f} pu, "
                f"V_setpoint={self.V_setpoint:.4f} pu, "
                f"H={self.H:.2f}, "
                f"fator_amortecimento={self.damping_factor:.2f}, "
                f"K_pgov={self.K_pgov:.2f}, "
                f"K_igov={self.K_igov:.2f}, "
                f"K_dgov={self.K_dgov:.2f}, "
                f"K_turb={self.K_turb:.2f}, "
                f"T_turb={self.T_turb:.2f}, "
                f"W_fnl={self.W_fnl:.2f}, "
                f"banda_morta={self.deadband:.4f}, "
                f"limiar_saturacao={self.saturation_threshold:.4f}, "
                f"ganho_saturacao={self.saturation_gain:.2f}, "
                f"limiar_emergencia={self.emergency_threshold:.4f}, "
                f"acao_emergencia={self.emergency_action:.2f})")
