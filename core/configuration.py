"""
Unified configuration management system for the power system simulation.

This module provides a centralized way to manage all configuration aspects:
- System parameters (base values, simulation settings)
- Generator configurations
- User preferences
- Load configurations
- Migration from legacy config files
"""

import json
import os
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict, field
from datetime import datetime
import warnings

@dataclass
class SystemConfig:
    """System-wide configuration parameters."""
    # Base electrical parameters
    s_base: float = 30e6        # Base power (VA)
    v_base: float = 13800.0     # Base voltage (V)
    f_base: float = 60.0        # Base frequency (Hz)
    
    # Simulation parameters
    dt: float = 0.01            # Time step (s)
    duration: float = 15.0      # Simulation duration (s)
    
    # Network parameters
    r_eq: float = 0.01          # Equivalent resistance (pu)
    x_eq: float = 0.1           # Equivalent reactance (pu)
    
    # Derived properties
    @property
    def z_base(self) -> float:
        """Calculate base impedance."""
        return (self.v_base ** 2) / self.s_base
    
    @property
    def i_base(self) -> float:
        """Calculate base current."""
        return self.s_base / self.v_base
    
    @property
    def time_vector(self) -> np.ndarray:
        """Generate time vector for simulation."""
        num_points = int(self.duration / self.dt) + 1
        return np.linspace(0, self.duration, num_points)
    
    @property
    def t(self) -> np.ndarray:
        """Alias for time_vector for backward compatibility."""
        return self.time_vector

@dataclass
class GeneratorConfig:
    """Configuration for a single generator."""
    name: str
    mode: str                   # "synchronous" or "droop"
    
    # Setpoints
    P_setpoint: float = 0.3     # Power setpoint (pu)
    V_setpoint: float = 1.0     # Voltage setpoint (pu)
    
    # Mechanical parameters
    H: float = 20.0             # Inertia constant (s)
    damping_factor: float = 6.0 # Damping factor
    
    # Governor parameters
    K_pgov: float = 12.0        # Proportional gain
    K_igov: float = 2.0         # Integral gain
    K_dgov: float = 0.8         # Derivative gain
    K_turb: float = 1.2         # Turbine gain
    T_turb: float = 0.15        # Turbine time constant (s)
    W_fnl: float = 0.15         # No-load frequency
    
    # AVR parameters
    K_pavr: float = 12.0        # AVR proportional gain
    K_iavr: float = 1.5         # AVR integral gain
    K_davr: float = 0.15        # AVR derivative gain
    T_avr: float = 0.04         # AVR time constant (s)
    V_max: float = 1.1          # Maximum voltage limit (pu)
    V_min: float = 0.9          # Minimum voltage limit (pu)
    
    # Disturbance parameters
    disturb_start: float = 0.0  # Disturbance start time (s)
    disturb_end: float = 0.0    # Disturbance end time (s)
    disturb_value: float = 0.0  # Disturbance magnitude (pu)

@dataclass
class LoadConfig:
    """Configuration for bus loads."""
    A: Dict[str, float] = field(default_factory=lambda: {"load_p": 0.075, "load_q": 0.0363})
    B: Dict[str, float] = field(default_factory=lambda: {"load_p": 0.3, "load_q": 0.03})
    C: Dict[str, float] = field(default_factory=lambda: {"load_p": 0.3, "load_q": 0.03})
    D: Dict[str, float] = field(default_factory=lambda: {"load_p": 0.3, "load_q": 0.03})

@dataclass
class UserPreferences:
    """User interface and application preferences."""
    # Storage preferences
    use_date_folders: bool = True
    auto_cleanup_enabled: bool = False      # OFF by default for safety
    auto_cleanup_days: int = 30             # Only used if auto_cleanup_enabled = True
    max_storage_mb: int = 1000
    
    # UI preferences
    default_graph_type: str = "Potência"
    legend_position: str = "below"          # "below" or "inside"
    auto_save_enabled: bool = False
    update_frequency: int = 50              # UI update frequency (steps)
    
    # Window preferences
    window_maximized: bool = False
    window_width: int = 1200
    window_height: int = 800
    
    # Recent files
    recent_simulations: List[str] = field(default_factory=list)


class ConfigurationManager:
    """Unified configuration management system."""

    def __init__(self, project_root: Optional[Path] = None):
        if project_root is None:
            # Auto-detect project root (where main.py is located)
            current_file = Path(__file__).resolve()
            project_root = current_file.parent.parent

        self.project_root = project_root
        self.config_dir = project_root / "config"
        self.config_dir.mkdir(exist_ok=True)

        # Configuration file paths
        self.system_config_file = self.config_dir / "system_config.json"
        self.generators_config_file = self.config_dir / "generators_config.json"
        self.user_preferences_file = self.config_dir / "user_preferences.json"
        self.scenarios_file = self.config_dir / "default_scenarios.json"

        # Loaded configurations
        self.system_config: Optional[SystemConfig] = None
        self.generators_config: List[GeneratorConfig] = []
        self.load_config: Optional[LoadConfig] = None
        self.user_preferences: Optional[UserPreferences] = None
        self.breaker_status: List[bool] = [True] * 8

        # Load all configurations
        self.load_all_configurations()

    def load_all_configurations(self):
        """Load all configuration files."""
        try:
            self.load_system_config()
            self.load_generators_config()
            self.load_user_preferences()
            self.migrate_legacy_config()  # Handle old config.json files
            print("✅ All configurations loaded successfully")
        except Exception as e:
            print(f"❌ Error loading configurations: {e}")
            self.create_default_configurations()

    def load_system_config(self):
        """Load system configuration."""
        if self.system_config_file.exists():
            try:
                with open(self.system_config_file, 'r') as f:
                    data = json.load(f)
                self.system_config = SystemConfig(**data['system'])
                self.load_config = LoadConfig(**data['loads'])
                self.breaker_status = data.get('breaker_status', [True] * 8)
                print(f"✅ System config loaded from {self.system_config_file}")
            except Exception as e:
                print(f"⚠️ Error loading system config: {e}, using defaults")
                self._create_default_system_config()
        else:
            self._create_default_system_config()

    def _create_default_system_config(self):
        """Create default system configuration."""
        self.system_config = SystemConfig()
        self.load_config = LoadConfig()
        self.breaker_status = [True] * 8
        self.save_system_config()
        print("✅ Default system config created")

    def load_generators_config(self):
        """Load generators configuration."""
        if self.generators_config_file.exists():
            try:
                with open(self.generators_config_file, 'r') as f:
                    data = json.load(f)
                self.generators_config = [GeneratorConfig(**gen) for gen in data['generators']]
                print(f"✅ Generators config loaded from {self.generators_config_file}")
            except Exception as e:
                print(f"⚠️ Error loading generators config: {e}, using defaults")
                self._create_default_generators()
        else:
            self._create_default_generators()

    def _create_default_generators(self):
        """Create default generator configurations."""
        self.generators_config = [
            GeneratorConfig(name="A", mode="synchronous", P_setpoint=0.6, V_setpoint=1.02, damping_factor=16.0),
            GeneratorConfig(name="B", mode="droop", P_setpoint=0.3, V_setpoint=1.0, damping_factor=6.0),
            GeneratorConfig(name="C", mode="droop", P_setpoint=0.3, V_setpoint=1.0, damping_factor=6.0),
            GeneratorConfig(name="D", mode="droop", P_setpoint=0.3, V_setpoint=1.0, damping_factor=6.0)
        ]
        self.save_generators_config()
        print("✅ Default generators config created")

    def load_user_preferences(self):
        """Load user preferences."""
        if self.user_preferences_file.exists():
            try:
                with open(self.user_preferences_file, 'r') as f:
                    data = json.load(f)
                self.user_preferences = UserPreferences(**data)
                print(f"✅ User preferences loaded from {self.user_preferences_file}")
            except Exception as e:
                print(f"⚠️ Error loading user preferences: {e}, using defaults")
                self._create_default_preferences()
        else:
            self._create_default_preferences()

    def _create_default_preferences(self):
        """Create default user preferences."""
        self.user_preferences = UserPreferences()
        self.save_user_preferences()
        print("✅ Default user preferences created")

    def save_system_config(self):
        """Save system configuration."""
        if self.system_config is None or self.load_config is None:
            raise ValueError("System config or load config is not initialized")

        data = {
            "metadata": {
                "version": "2.0",
                "last_updated": datetime.now().isoformat(),
                "description": "System configuration for power system simulation"
            },
            "system": asdict(self.system_config),
            "loads": asdict(self.load_config),
            "breaker_status": self.breaker_status
        }

        with open(self.system_config_file, 'w') as f:
            json.dump(data, f, indent=2)
        print(f"✅ System config saved to {self.system_config_file}")

    def save_generators_config(self):
        """Save generators configuration."""
        data = {
            "metadata": {
                "version": "2.0",
                "last_updated": datetime.now().isoformat(),
                "description": "Generator configurations for power system simulation"
            },
            "generators": [asdict(gen) for gen in self.generators_config]
        }

        with open(self.generators_config_file, 'w') as f:
            json.dump(data, f, indent=2)
        print(f"✅ Generators config saved to {self.generators_config_file}")

    def save_user_preferences(self):
        """Save user preferences."""
        if self.user_preferences is None:
            raise ValueError("User preferences is not initialized")

        with open(self.user_preferences_file, 'w') as f:
            json.dump(asdict(self.user_preferences), f, indent=2)
        print(f"✅ User preferences saved to {self.user_preferences_file}")

    def create_default_configurations(self):
        """Create all default configurations."""
        self._create_default_system_config()
        self._create_default_generators()
        self._create_default_preferences()
        print("✅ All default configurations created")

    def migrate_legacy_config(self):
        """Migrate from old config.json format."""
        legacy_files = [
            self.project_root / "config.json",
            self.config_dir / "config.json"
        ]

        for legacy_file in legacy_files:
            if legacy_file.exists():
                print(f"🔄 Migrating legacy configuration from {legacy_file}")
                try:
                    with open(legacy_file, 'r') as f:
                        legacy_data = json.load(f)

                    self._migrate_legacy_data(legacy_data)

                    # Backup and remove legacy file
                    backup_file = legacy_file.with_suffix('.json.backup')
                    legacy_file.rename(backup_file)
                    print(f"✅ Legacy config migrated and backed up to {backup_file}")

                except Exception as e:
                    print(f"❌ Error migrating legacy config: {e}")

    def _migrate_legacy_data(self, legacy_data: Dict[str, Any]):
        """Convert legacy config format to new format."""
        # Migrate system config
        if 'bus' in legacy_data:
            bus_data = legacy_data['bus']
            self.system_config = SystemConfig(
                s_base=bus_data.get('s_base', 30e6),
                v_base=bus_data.get('v_base', 13800.0),
                f_base=bus_data.get('f_base', 60.0),
                r_eq=bus_data.get('r_eq', 0.01),
                x_eq=bus_data.get('x_eq', 0.1)
            )

            # Migrate loads
            if 'loads' in bus_data:
                loads = bus_data['loads']
                self.load_config = LoadConfig(
                    A=loads.get('A', {"load_p": 0.075, "load_q": 0.0363}),
                    B=loads.get('B', {"load_p": 0.3, "load_q": 0.03}),
                    C=loads.get('C', {"load_p": 0.3, "load_q": 0.03}),
                    D=loads.get('D', {"load_p": 0.3, "load_q": 0.03})
                )

        # Migrate generators
        if 'generators' in legacy_data:
            self.generators_config = [
                GeneratorConfig(**gen) for gen in legacy_data['generators']
            ]

        # Migrate breaker status
        self.breaker_status = legacy_data.get('breaker_status', [True] * 8)

        # Save migrated data
        self.save_system_config()
        self.save_generators_config()
        print("✅ Legacy data migration completed")

    def validate_configuration(self) -> List[str]:
        """Validate current configuration and return any errors."""
        errors = []

        # Validate system config
        if self.system_config is None:
            errors.append("System configuration is not loaded")
            return errors

        if self.system_config.s_base <= 0:
            errors.append("Base power must be positive")
        if self.system_config.dt <= 0:
            errors.append("Time step must be positive")
        if self.system_config.duration <= 0:
            errors.append("Simulation duration must be positive")

        # Validate generators
        if len(self.generators_config) != 4:
            errors.append("Must have exactly 4 generators")

        for gen in self.generators_config:
            if gen.H <= 0:
                errors.append(f"Generator {gen.name}: Inertia constant must be positive")
            if gen.P_setpoint < 0:
                errors.append(f"Generator {gen.name}: Power setpoint cannot be negative")
            if gen.mode not in ["synchronous", "droop"]:
                errors.append(f"Generator {gen.name}: Invalid mode '{gen.mode}'")

        # Validate breaker status
        if len(self.breaker_status) != 8:
            errors.append("Must have exactly 8 breaker status values")

        return errors

    def get_configuration_summary(self) -> str:
        """Get a human-readable summary of current configuration."""
        if self.system_config is None:
            return "Configuration not loaded"

        summary = "CURRENT CONFIGURATION SUMMARY\n"
        summary += "=" * 40 + "\n\n"

        summary += f"System Base Values:\n"
        summary += f"  Power: {self.system_config.s_base/1e6:.1f} MVA\n"
        summary += f"  Voltage: {self.system_config.v_base/1000:.1f} kV\n"
        summary += f"  Frequency: {self.system_config.f_base:.1f} Hz\n\n"

        summary += f"Simulation Settings:\n"
        summary += f"  Duration: {self.system_config.duration:.1f} s\n"
        summary += f"  Time Step: {self.system_config.dt:.3f} s\n\n"

        summary += f"Generators:\n"
        for gen in self.generators_config:
            summary += f"  {gen.name}: {gen.mode}, P={gen.P_setpoint:.2f} pu, H={gen.H:.1f} s\n"

        summary += f"\nBreaker Status: {self.breaker_status}\n"

        return summary

    def update_generator_config(self, generator_name: str, **kwargs):
        """Update a specific generator's configuration."""
        for gen in self.generators_config:
            if gen.name == generator_name:
                for key, value in kwargs.items():
                    if hasattr(gen, key):
                        setattr(gen, key, value)
                    else:
                        print(f"⚠️ Unknown parameter '{key}' for generator {generator_name}")
                self.save_generators_config()
                return True

        print(f"❌ Generator '{generator_name}' not found")
        return False

    def get_generator_config(self, generator_name: str) -> Optional[GeneratorConfig]:
        """Get configuration for a specific generator."""
        for gen in self.generators_config:
            if gen.name == generator_name:
                return gen
        return None
