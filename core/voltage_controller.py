"""
Controlador de tensão para o sistema elétrico de potência.

<PERSON><PERSON> módulo define a classe VoltageController que coordena o controle de tensão
entre os geradores e o barramento principal.
"""

import numpy as np
from typing import List, Dict, Any, Optional

from core.constants import SystemConfig
from core.generator import Generator
from core.bus import MainBus, SecondaryBus


class VoltageController:
    """
    Controlador de tensão para o sistema elétrico de potência.

    Esta classe coordena o controle de tensão entre os geradores e o barramento principal,
    implementando estratégias de controle como droop de tensão e compartilhamento de potência reativa.

    Atributos:
        config (SystemConfig): Configuração do sistema
        generators (List[Generator]): Lista de geradores no sistema
        main_bus (MainBus): Barramento principal
        secondary_buses (List[SecondaryBus]): Lista de barramentos secundários
        v_setpoint (float): Setpoint de tensão do barramento principal em pu
        v_droop (float): Coeficiente de droop de tensão
        q_sharing_enabled (bool): Se o compartilhamento de potência reativa está habilitado
    """

    def __init__(self, config: SystemConfig, generators: List[Generator], 
                 main_bus: MainBus, secondary_buses: List[SecondaryBus],
                 v_setpoint: float = 1.0, v_droop: float = 0.05,
                 q_sharing_enabled: bool = True):
        """
        Inicializa o controlador de tensão.

        Args:
            config: Configuração do sistema
            generators: Lista de geradores no sistema
            main_bus: Barramento principal
            secondary_buses: Lista de barramentos secundários
            v_setpoint: Setpoint de tensão do barramento principal em pu (padrão: 1.0)
            v_droop: Coeficiente de droop de tensão (padrão: 0.05)
            q_sharing_enabled: Se o compartilhamento de potência reativa está habilitado (padrão: True)
        """
        self.config = config
        self.generators = generators
        self.main_bus = main_bus
        self.secondary_buses = secondary_buses
        self.v_setpoint = v_setpoint
        self.v_droop = v_droop
        self.q_sharing_enabled = q_sharing_enabled

        # Parâmetros de controle
        self.K_p = 0.5  # Ganho proporcional para controle de tensão do barramento principal
        self.K_i = 0.1  # Ganho integral para controle de tensão do barramento principal
        self.K_d = 0.05  # Ganho derivativo para controle de tensão do barramento principal
        
        # Variáveis de estado
        self.v_error_integral = 0.0
        self.prev_v_error = 0.0
        self.last_update_time = 0
        
        # Limites
        self.v_max = 1.1  # Limite máximo de tensão em pu
        self.v_min = 0.9  # Limite mínimo de tensão em pu
        
        # Inicializa histórico
        self.v_history = []
        self.q_history = []
        
        print(f"Controlador de tensão inicializado com setpoint={v_setpoint:.4f} pu, droop={v_droop:.4f}")

    def update(self, time_index: int, dt: float) -> None:
        """
        Atualiza o controle de tensão.

        Esta função coordena o controle de tensão entre os geradores e o barramento principal,
        ajustando os setpoints de tensão dos geradores para manter a tensão do barramento principal
        próxima ao setpoint desejado.

        Args:
            time_index: Índice de tempo atual
            dt: Passo de tempo em segundos
        """
        # Registra o tempo da última atualização
        self.last_update_time = time_index
        
        # Obtém a tensão atual do barramento principal
        main_bus_voltage = self.main_bus.voltage
        
        # Calcula o erro de tensão
        v_error = self.v_setpoint - main_bus_voltage
        
        # Atualiza o termo integral
        self.v_error_integral += v_error * dt
        
        # Limita o termo integral para evitar wind-up
        self.v_error_integral = max(-1.0, min(1.0, self.v_error_integral))
        
        # Calcula o termo derivativo
        if time_index > 0:
            v_error_derivative = (v_error - self.prev_v_error) / dt
        else:
            v_error_derivative = 0.0
        
        # Armazena o erro atual para a próxima iteração
        self.prev_v_error = v_error
        
        # Calcula a saída do controlador PID
        pid_output = (self.K_p * v_error + 
                      self.K_i * self.v_error_integral + 
                      self.K_d * v_error_derivative)
        
        # Aplica o controle aos geradores conectados
        self._apply_voltage_control(time_index, pid_output)
        
        # Registra dados para análise
        self.v_history.append(main_bus_voltage)
        q_values = [gen.Q_elec[time_index] for gen in self.generators]
        self.q_history.append(q_values)
        
        # Registra informações de depuração
        if time_index % 100 == 0:  # Registra a cada 100 passos
            print(f"Controle de tensão em t={time_index*dt:.2f}s:")
            print(f"  Tensão do barramento principal: {main_bus_voltage:.4f} pu")
            print(f"  Erro de tensão: {v_error:.4f} pu")
            print(f"  Saída do controlador PID: {pid_output:.4f}")
            
            # Registra informações de cada gerador
            for i, gen in enumerate(self.generators):
                if gen.is_connected:
                    print(f"  Gerador {gen.name}: V_setpoint={gen.V_setpoint:.4f} pu, "
                          f"V_t={gen.V_t[time_index]:.4f} pu, Q={gen.Q_elec[time_index]:.4f} pu")

    def _apply_voltage_control(self, time_index: int, pid_output: float) -> None:
        """
        Aplica o controle de tensão aos geradores.

        Esta função ajusta os setpoints de tensão dos geradores com base na saída do controlador PID
        e na estratégia de controle selecionada (droop ou compartilhamento de potência reativa).

        Args:
            time_index: Índice de tempo atual
            pid_output: Saída do controlador PID
        """
        # Conta o número de geradores conectados
        connected_generators = [gen for gen in self.generators if gen.is_connected]
        num_connected = len(connected_generators)
        
        if num_connected == 0:
            return  # Nenhum gerador conectado, não há nada a fazer
        
        # Calcula a potência reativa total
        total_q = sum(gen.Q_elec[time_index] for gen in connected_generators)
        
        # Aplica o controle a cada gerador conectado
        for gen in connected_generators:
            # Calcula o ajuste de tensão com base na estratégia de controle
            if self.q_sharing_enabled and num_connected > 1:
                # Modo de compartilhamento de potência reativa
                # Geradores com maior Q recebem um setpoint de tensão menor
                q_share = gen.Q_elec[time_index] / total_q if total_q != 0 else 1.0 / num_connected
                v_adjustment = pid_output * (1.0 - q_share * num_connected)
            else:
                # Modo droop de tensão
                # Cada gerador recebe um ajuste proporcional à sua potência reativa
                v_adjustment = pid_output - self.v_droop * gen.Q_elec[time_index]
            
            # Calcula o novo setpoint de tensão
            new_v_setpoint = self.v_setpoint + v_adjustment
            
            # Limita o setpoint dentro dos limites permitidos
            new_v_setpoint = max(self.v_min, min(self.v_max, new_v_setpoint))
            
            # Atualiza o setpoint de tensão do gerador
            if abs(new_v_setpoint - gen.V_setpoint) > 0.001:  # Apenas se houver mudança significativa
                old_setpoint = gen.V_setpoint
                gen.V_setpoint = new_v_setpoint
                
                # Registra a mudança
                if time_index % 100 == 0:  # Registra a cada 100 passos
                    print(f"  Ajustando setpoint de tensão do gerador {gen.name}: "
                          f"{old_setpoint:.4f} pu -> {new_v_setpoint:.4f} pu")

    def get_status(self) -> Dict[str, Any]:
        """
        Obtém o status atual do controlador de tensão.

        Returns:
            Dicionário com informações de status
        """
        return {
            "v_setpoint": self.v_setpoint,
            "v_droop": self.v_droop,
            "q_sharing_enabled": self.q_sharing_enabled,
            "main_bus_voltage": self.main_bus.voltage,
            "v_error": self.v_setpoint - self.main_bus.voltage,
            "v_error_integral": self.v_error_integral,
            "generator_setpoints": {gen.name: gen.V_setpoint for gen in self.generators},
            "generator_voltages": {gen.name: gen.V_t[self.last_update_time] 
                                  if self.last_update_time < len(gen.V_t) else 0.0 
                                  for gen in self.generators},
            "generator_q": {gen.name: gen.Q_elec[self.last_update_time] 
                           if self.last_update_time < len(gen.Q_elec) else 0.0 
                           for gen in self.generators}
        }

    def set_main_bus_voltage_setpoint(self, v_setpoint: float) -> None:
        """
        Define o setpoint de tensão do barramento principal.

        Args:
            v_setpoint: Novo setpoint de tensão em pu
        """
        if v_setpoint < self.v_min or v_setpoint > self.v_max:
            print(f"Aviso: Setpoint de tensão {v_setpoint:.4f} pu está fora dos limites "
                  f"({self.v_min:.4f}-{self.v_max:.4f} pu)")
        
        self.v_setpoint = max(self.v_min, min(self.v_max, v_setpoint))
        print(f"Setpoint de tensão do barramento principal alterado para {self.v_setpoint:.4f} pu")
        
        # Reseta o termo integral para evitar overshoot
        self.v_error_integral = 0.0

    def set_voltage_droop(self, v_droop: float) -> None:
        """
        Define o coeficiente de droop de tensão.

        Args:
            v_droop: Novo coeficiente de droop de tensão
        """
        if v_droop < 0.0:
            print(f"Aviso: Coeficiente de droop de tensão não pode ser negativo. Usando 0.0.")
            v_droop = 0.0
        
        self.v_droop = v_droop
        print(f"Coeficiente de droop de tensão alterado para {self.v_droop:.4f}")

    def enable_q_sharing(self, enabled: bool) -> None:
        """
        Habilita ou desabilita o compartilhamento de potência reativa.

        Args:
            enabled: Se o compartilhamento de potência reativa deve ser habilitado
        """
        self.q_sharing_enabled = enabled
        status = "habilitado" if enabled else "desabilitado"
        print(f"Compartilhamento de potência reativa {status}")

    def __str__(self) -> str:
        """Retorna uma representação em string do controlador de tensão."""
        return (f"VoltageController(v_setpoint={self.v_setpoint:.4f} pu, "
                f"v_droop={self.v_droop:.4f}, "
                f"q_sharing_enabled={self.q_sharing_enabled})")
