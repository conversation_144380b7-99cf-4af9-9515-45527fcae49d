#!/usr/bin/env python3
"""
Enhanced PLC Manager for advanced integration with power system simulation.
Supports multiple variables, real-time graphing, and flexible configuration.
"""

import threading
import time
import csv
import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import logging

try:
    from pylogix import PLC
    PYLOGIX_AVAILABLE = True
except ImportError:
    PYLOGIX_AVAILABLE = False
    print("Warning: pylogix not available. PLC functionality will be simulated.")

# Local log function to avoid circular imports
def log_message(message: str, level: str = "info") -> None:
    """Log a message with the specified level."""
    print(message)
    if level == "debug":
        logging.debug(message)
    elif level == "info":
        logging.info(message)
    elif level == "warning":
        logging.warning(message)
    elif level == "error":
        logging.error(message)
    elif level == "critical":
        logging.critical(message)


@dataclass
class PLCVariable:
    """Represents a PLC variable with metadata."""
    tag: str
    description: str
    data_type: str  # BOOL, REAL, INT, etc.
    unit: str
    direction: str  # LEITURA (read from PLC) or ESCRITA (write to PLC)
    simulation_mapping: str
    category: str = "general"  # generator, bus, alarm, control, etc.
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    scaling_factor: float = 1.0
    enabled: bool = True


@dataclass
class PLCDataPoint:
    """Represents a single data point from PLC."""
    timestamp: datetime
    tag: str
    value: Any
    quality: str = "Good"


class PLCDataBuffer:
    """Circular buffer for storing PLC data history."""
    
    def __init__(self, max_size: int = 10000):
        self.max_size = max_size
        self.data: Dict[str, List[PLCDataPoint]] = {}
        self.lock = threading.Lock()
    
    def add_data_point(self, tag: str, value: Any, quality: str = "Good"):
        """Add a data point to the buffer."""
        with self.lock:
            if tag not in self.data:
                self.data[tag] = []
            
            data_point = PLCDataPoint(
                timestamp=datetime.now(),
                tag=tag,
                value=value,
                quality=quality
            )
            
            self.data[tag].append(data_point)
            
            # Keep buffer size under limit
            if len(self.data[tag]) > self.max_size:
                self.data[tag] = self.data[tag][-self.max_size:]
    
    def get_recent_data(self, tag: str, count: int = 100) -> List[PLCDataPoint]:
        """Get recent data points for a tag."""
        with self.lock:
            if tag not in self.data:
                return []
            return self.data[tag][-count:]
    
    def get_data_for_plotting(self, tag: str, count: int = 100) -> Tuple[List[datetime], List[float]]:
        """Get data formatted for plotting."""
        data_points = self.get_recent_data(tag, count)
        timestamps = [dp.timestamp for dp in data_points]
        values = [float(dp.value) if dp.value is not None else 0.0 for dp in data_points]
        return timestamps, values
    
    def clear_tag_data(self, tag: str):
        """Clear data for a specific tag."""
        with self.lock:
            if tag in self.data:
                self.data[tag].clear()
    
    def get_all_tags(self) -> List[str]:
        """Get list of all tags with data."""
        with self.lock:
            return list(self.data.keys())


class EnhancedPLCManager:
    """Enhanced PLC Manager with advanced features."""
    
    def __init__(self, simulation, config_file: str = "config/plc_config.json"):
        self.simulation = simulation
        self.config_file = Path(config_file)
        self.variables: Dict[str, PLCVariable] = {}
        self.data_buffer = PLCDataBuffer()
        self.plc = None
        self.running = False
        self.comm_thread = None
        self.update_interval = 0.1
        self.plc_ip = "*************"
        
        # Statistics
        self.stats = {
            'total_reads': 0,
            'total_writes': 0,
            'successful_reads': 0,
            'successful_writes': 0,
            'connection_errors': 0,
            'last_update': None
        }
        
        # Load configuration
        self._load_configuration()
        self._load_variables_from_csv()
    
    def _load_configuration(self):
        """Load PLC configuration from JSON file."""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                
                self.plc_ip = config.get('plc_ip', '*************')
                self.update_interval = config.get('update_interval', 0.1)
                
                log_message(f"PLC configuration loaded from {self.config_file}", level="info")
            else:
                # Create default configuration
                self._create_default_config()
                
        except Exception as e:
            log_message(f"Error loading PLC configuration: {e}", level="error")
            self._create_default_config()
    
    def _create_default_config(self):
        """Create default PLC configuration."""
        default_config = {
            "plc_ip": "*************",
            "update_interval": 0.1,
            "enabled": True,
            "timeout": 5.0,
            "retry_attempts": 3,
            "data_buffer_size": 10000,
            "categories": {
                "generator": {
                    "enabled": True,
                    "update_frequency": 1
                },
                "bus": {
                    "enabled": True,
                    "update_frequency": 1
                },
                "alarm": {
                    "enabled": True,
                    "update_frequency": 2
                },
                "control": {
                    "enabled": True,
                    "update_frequency": 1
                }
            }
        }
        
        # Create config directory if it doesn't exist
        self.config_file.parent.mkdir(exist_ok=True)
        
        with open(self.config_file, 'w') as f:
            json.dump(default_config, f, indent=2)
        
        log_message(f"Default PLC configuration created at {self.config_file}", level="info")
    
    def _load_variables_from_csv(self):
        """Load PLC variables from CSV file."""
        csv_file = Path("tags_plc_map.csv")
        
        if not csv_file.exists():
            log_message(f"PLC tags file not found: {csv_file}", level="warning")
            return
        
        try:
            with open(csv_file, 'r') as f:
                reader = csv.DictReader(f)
                
                for row in reader:
                    tag = row['TAG']
                    
                    # Determine category based on tag name
                    category = self._determine_category(tag)
                    
                    variable = PLCVariable(
                        tag=tag,
                        description=row['Descrição'],
                        data_type=row['Tipo'],
                        unit=row['Unidade'],
                        direction=row['Direção'],
                        simulation_mapping=row['Mapeamento'],
                        category=category,
                        enabled=row.get('Simulável', 'Sim').lower() == 'sim'
                    )
                    
                    self.variables[tag] = variable
            
            log_message(f"Loaded {len(self.variables)} PLC variables from {csv_file}", level="info")
            
        except Exception as e:
            log_message(f"Error loading PLC variables: {e}", level="error")
    
    def _determine_category(self, tag: str) -> str:
        """Determine variable category based on tag name."""
        tag_upper = tag.upper()
        
        if any(gen in tag_upper for gen in ['TGCPA', 'TGCPB', 'TGCPC', 'TGCPD']):
            return "generator"
        elif any(bus in tag_upper for bus in ['52BB', '52CS', 'TIE']):
            return "bus"
        elif any(alarm in tag_upper for alarm in ['FAULT', 'ALARM', 'TRIP', 'CO2', 'FIRE']):
            return "alarm"
        elif any(ctrl in tag_upper for ctrl in ['ASR', 'VR', 'ENABLE', 'DISABLE']):
            return "control"
        else:
            return "general"
    
    def get_variables_by_category(self, category: str) -> List[PLCVariable]:
        """Get all variables in a specific category."""
        return [var for var in self.variables.values() if var.category == category and var.enabled]
    
    def get_plottable_variables(self) -> List[PLCVariable]:
        """Get variables suitable for plotting (REAL type, LEITURA direction)."""
        return [
            var for var in self.variables.values()
            if var.data_type == 'REAL' and var.direction == 'LEITURA' and var.enabled
        ]
    
    def start(self):
        """Start PLC communication."""
        if not PYLOGIX_AVAILABLE:
            log_message("PyLogix not available. Starting in simulation mode.", level="warning")
            self._start_simulation_mode()
            return
        
        if self.running:
            log_message("PLC communication already running", level="warning")
            return
        
        try:
            self._connect()
            self.running = True
            self.comm_thread = threading.Thread(target=self._communication_loop)
            self.comm_thread.daemon = True
            self.comm_thread.start()
            
            log_message(f"PLC communication started with {self.plc_ip}", level="info")
            
        except Exception as e:
            log_message(f"Failed to start PLC communication: {e}", level="error")
            self._start_simulation_mode()
    
    def _start_simulation_mode(self):
        """Start in simulation mode (no real PLC)."""
        self.running = True
        self.comm_thread = threading.Thread(target=self._simulation_loop)
        self.comm_thread.daemon = True
        self.comm_thread.start()
        
        log_message("PLC communication started in SIMULATION mode", level="info")
    
    def _connect(self):
        """Connect to PLC."""
        if self.plc is None:
            self.plc = PLC()
            self.plc.IPAddress = self.plc_ip
            
            # Test connection
            ret = self.plc.GetPLCTime()
            if ret.Status != "Success":
                raise ConnectionError(f"Failed to connect to PLC: {ret.Status}")
    
    def _communication_loop(self):
        """Main communication loop."""
        log_message("PLC communication loop started", level="debug")
        
        while self.running:
            try:
                self._update_all_variables()
                self.stats['last_update'] = datetime.now()
                
            except Exception as e:
                log_message(f"Error in PLC communication loop: {e}", level="error")
                self.stats['connection_errors'] += 1
            
            time.sleep(self.update_interval)
    
    def _simulation_loop(self):
        """Simulation loop for testing without real PLC."""
        log_message("PLC simulation loop started", level="debug")
        
        while self.running:
            try:
                self._simulate_plc_data()
                self.stats['last_update'] = datetime.now()
                
            except Exception as e:
                log_message(f"Error in PLC simulation loop: {e}", level="error")
            
            time.sleep(self.update_interval)
    
    def _update_all_variables(self):
        """Update all enabled PLC variables."""
        for variable in self.variables.values():
            if not variable.enabled:
                continue
            
            try:
                if variable.direction == 'LEITURA':
                    self._read_variable(variable)
                elif variable.direction == 'ESCRITA':
                    self._write_variable(variable)
                    
            except Exception as e:
                log_message(f"Error updating variable {variable.tag}: {e}", level="error")
    
    def _read_variable(self, variable: PLCVariable):
        """Read a variable from PLC."""
        if self.plc is None:
            log_message("PLC connection not available", level="error")
            return

        self.stats['total_reads'] += 1

        try:
            ret = self.plc.Read(variable.tag)

            # Handle both single response and list of responses
            if isinstance(ret, list) and len(ret) > 0:
                ret = ret[0]  # Take first response

            status = getattr(ret, 'Status', 'Unknown')
            if status == "Success":
                self.stats['successful_reads'] += 1

                # Apply scaling if needed
                value = getattr(ret, 'Value', None)
                if value is not None and variable.data_type == 'REAL' and variable.scaling_factor != 1.0:
                    value *= variable.scaling_factor

                # Store in buffer
                self.data_buffer.add_data_point(variable.tag, value, "Good")

                # Update simulation if mapping exists
                self._update_simulation_from_plc(variable, value)

            else:
                log_message(f"Failed to read {variable.tag}: {status}", level="error")
                self.data_buffer.add_data_point(variable.tag, None, "Bad")

        except Exception as e:
            log_message(f"Exception reading {variable.tag}: {e}", level="error")
            self.data_buffer.add_data_point(variable.tag, None, "Bad")
    
    def _write_variable(self, variable: PLCVariable):
        """Write a variable to PLC."""
        if self.plc is None:
            log_message("PLC connection not available", level="error")
            return

        self.stats['total_writes'] += 1

        # Get value from simulation
        value = self._get_simulation_value(variable)

        if value is not None:
            try:
                # Apply scaling if needed
                if variable.data_type == 'REAL' and variable.scaling_factor != 1.0:
                    value /= variable.scaling_factor

                ret = self.plc.Write(variable.tag, value)

                # Handle both single response and list of responses
                if isinstance(ret, list) and len(ret) > 0:
                    ret = ret[0]  # Take first response

                status = getattr(ret, 'Status', 'Unknown')
                if status == "Success":
                    self.stats['successful_writes'] += 1
                    self.data_buffer.add_data_point(variable.tag, value, "Good")
                else:
                    log_message(f"Failed to write {variable.tag}: {status}", level="error")
                    self.data_buffer.add_data_point(variable.tag, None, "Bad")

            except Exception as e:
                log_message(f"Exception writing {variable.tag}: {e}", level="error")
                self.data_buffer.add_data_point(variable.tag, None, "Bad")
    
    def _simulate_plc_data(self):
        """Simulate PLC data for testing."""
        current_time = time.time()
        
        for variable in self.variables.values():
            if not variable.enabled or variable.direction != 'LEITURA':
                continue
            
            # Generate simulated data based on variable type
            if variable.data_type == 'BOOL':
                value = np.random.choice([True, False], p=[0.8, 0.2])
            elif variable.data_type == 'REAL':
                if 'MW' in variable.tag:
                    value = 10.0 + 5.0 * np.sin(current_time * 0.1) + np.random.normal(0, 0.5)
                elif 'Hz' in variable.tag:
                    value = 60.0 + 0.5 * np.sin(current_time * 0.2) + np.random.normal(0, 0.1)
                elif 'kV' in variable.tag:
                    value = 13.8 + 0.2 * np.sin(current_time * 0.15) + np.random.normal(0, 0.05)
                else:
                    value = np.random.normal(0, 1)
            else:
                value = 0
            
            self.data_buffer.add_data_point(variable.tag, value, "Simulated")
    
    def _get_simulation_value(self, variable: PLCVariable) -> Optional[float]:
        """Get value from simulation to write to PLC."""
        mapping = variable.simulation_mapping
        
        if not mapping or mapping == 'None':
            return None
        
        try:
            # Implement mapping logic based on simulation_mapping
            if mapping.startswith('power_'):
                gen_name = mapping.split('_')[1]
                gen = self._get_generator_by_name(gen_name)
                if gen:
                    return gen.P_elec[self.simulation.current_time_index] * 30.0  # Convert to MW
            
            elif mapping.startswith('frequency_'):
                gen_name = mapping.split('_')[1]
                gen = self._get_generator_by_name(gen_name)
                if gen:
                    return gen.omega[self.simulation.current_time_index] * 60.0  # Convert to Hz
            
            elif mapping.startswith('voltage_'):
                gen_name = mapping.split('_')[1]
                gen = self._get_generator_by_name(gen_name)
                if gen:
                    return gen.V_t[self.simulation.current_time_index] * 13.8  # Convert to kV
            
            elif mapping.startswith('breaker_status'):
                index = int(mapping.split('[')[1].split(']')[0])
                return float(self.simulation.system.breaker_status[index])
            
        except Exception as e:
            log_message(f"Error getting simulation value for {variable.tag}: {e}", level="error")
        
        return None
    
    def _get_generator_by_name(self, name: str):
        """Get generator by name."""
        for gen in self.simulation.system.generators:
            if gen.name == name:
                return gen
        return None
    
    def _update_simulation_from_plc(self, variable: PLCVariable, value: Any):
        """Update simulation with value from PLC."""
        # This would implement control from PLC to simulation
        # For now, we just log the received values
        pass
    
    def stop(self):
        """Stop PLC communication."""
        log_message("Stopping PLC communication...", level="info")
        self.running = False
        
        if self.comm_thread:
            self.comm_thread.join(timeout=2.0)
        
        if self.plc:
            self.plc.Close()
            self.plc = None
        
        log_message("PLC communication stopped", level="info")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get communication statistics."""
        return self.stats.copy()
    
    def get_variable_info(self, tag: str) -> Optional[PLCVariable]:
        """Get information about a specific variable."""
        return self.variables.get(tag)
    
    def export_data_to_csv(self, filename: str, tags: Optional[List[str]] = None):
        """Export PLC data to CSV file."""
        if tags is None:
            tags = self.data_buffer.get_all_tags()

        # Implementation for CSV export
        # TODO: Implement actual CSV export functionality
        log_message(f"CSV export requested for {len(tags)} tags to {filename}", level="info")
