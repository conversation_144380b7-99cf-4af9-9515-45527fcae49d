"""
Date-based simulation data management system.

This module provides functionality to save and load simulation data
in an organized, date-based folder structure with JSON + CSV format.
"""

import os
import json
import pandas as pd
import numpy as np
import re
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import asdict
import shutil

from core.configuration import ConfigurationManager


class DateBasedSimulationManager:
    """Manages simulation data storage with date-based organization."""
    
    def __init__(self, config_manager: Optional[ConfigurationManager] = None):
        if config_manager is None:
            config_manager = ConfigurationManager()
        
        self.config_manager = config_manager
        self.project_root = config_manager.project_root
        self.data_dir = self.project_root / "simulation_data"
        self.index_file = self.data_dir / "index.json"
        
        # Create base directory
        self.data_dir.mkdir(exist_ok=True)
        self._init_index()
    
    def _init_index(self):
        """Initialize the global index file."""
        if not self.index_file.exists():
            index_data = {
                "version": "1.0",
                "created": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "simulations": []
            }
            with open(self.index_file, 'w') as f:
                json.dump(index_data, f, indent=2)
    
    def get_today_folder(self) -> Path:
        """Get today's simulation folder, creating if needed."""
        today = datetime.now().strftime("%Y-%m-%d")
        today_folder = self.data_dir / today
        today_folder.mkdir(exist_ok=True)
        return today_folder
    
    def get_date_folder(self, date: datetime) -> Path:
        """Get simulation folder for specific date."""
        date_str = date.strftime("%Y-%m-%d")
        date_folder = self.data_dir / date_str
        date_folder.mkdir(exist_ok=True)
        return date_folder
    
    def save_simulation(self, simulation, description: str = "") -> Dict[str, Path]:
        """Save simulation with date-based organization."""
        timestamp = datetime.now()
        save_folder = self.get_today_folder()
        
        # Generate base filename
        time_str = timestamp.strftime("%Y-%m-%d_%H-%M-%S")
        base_name = f"simulation_{time_str}"
        
        # Define file paths
        files = {
            'config': save_folder / f"{base_name}_config.json",
            'data': save_folder / f"{base_name}_data.csv",
            'summary': save_folder / f"{base_name}_summary.txt"
        }
        
        # Save all files
        self._save_config_file(files['config'], simulation, description, timestamp)
        self._save_data_file(files['data'], simulation)
        self._save_summary_file(files['summary'], simulation, description, timestamp)
        
        # Update daily summary
        self._update_daily_summary(save_folder, description, timestamp, files)
        
        # Update global index
        self._update_global_index(files, description, timestamp)
        
        print(f"✅ Simulation saved successfully:")
        print(f"   Config: {files['config']}")
        print(f"   Data: {files['data']}")
        print(f"   Summary: {files['summary']}")
        
        return files
    
    def _save_config_file(self, filepath: Path, simulation, description: str, timestamp: datetime):
        """Save configuration file with all parameters needed for reproduction."""
        if self.config_manager.system_config is None or self.config_manager.load_config is None:
            raise ValueError("Configuration manager not properly initialized")

        config_data = {
            "metadata": {
                "timestamp": timestamp.isoformat(),
                "version": "2.0",
                "description": description,
                "duration_seconds": simulation.config.duration,
                "total_samples": len(simulation.t)
            },
            "system": asdict(self.config_manager.system_config),
            "generators": [asdict(gen) for gen in self.config_manager.generators_config],
            "loads": asdict(self.config_manager.load_config),
            "breaker_status": self.config_manager.breaker_status,
            "initial_conditions": self._extract_initial_conditions(simulation)
        }
        
        with open(filepath, 'w') as f:
            json.dump(config_data, f, indent=2)
    
    def _save_data_file(self, filepath: Path, simulation):
        """Save time series data to CSV file."""
        # Get the current time index
        current_idx = min(simulation.current_time_index, len(simulation.t))
        if current_idx <= 0:
            raise ValueError("No simulation data available to save")
        
        # Create data dictionary
        data = {
            "time": simulation.t[:current_idx]
        }
        
        # Add generator data
        for idx, gen in enumerate(simulation.system.generators):
            gen_name = gen.name
            # Ensure arrays are of the correct length
            p_elec_len = min(current_idx, len(gen.P_elec))
            p_mech_len = min(current_idx, len(gen.P_mech))
            omega_len = min(current_idx, len(gen.omega))
            v_t_len = min(current_idx, len(gen.V_t))
            
            data[f"Gen_{gen_name}_P_elec"] = gen.P_elec[:p_elec_len]
            data[f"Gen_{gen_name}_P_mech"] = gen.P_mech[:p_mech_len]
            data[f"Gen_{gen_name}_omega"] = gen.omega[:omega_len]
            data[f"Gen_{gen_name}_frequency"] = gen.omega[:omega_len] * simulation.config.f_base
            data[f"Gen_{gen_name}_V_t"] = gen.V_t[:v_t_len]
        
        # Add bus data
        for idx, bus in enumerate(simulation.system.secondary_buses):
            bus_name = chr(65 + idx)  # A, B, C, D
            # Ensure arrays are of the correct length
            power_len = min(current_idx, len(simulation.secondary_bus_powers[idx]))
            freq_len = min(current_idx, len(simulation.secondary_bus_frequencies[idx]))
            volt_len = min(current_idx, len(simulation.secondary_bus_voltages[idx]))
            budget_len = min(current_idx, len(simulation.secondary_bus_power_budgets[idx]))
            
            data[f"Bus_{bus_name}_power"] = simulation.secondary_bus_powers[idx][:power_len]
            data[f"Bus_{bus_name}_frequency"] = simulation.secondary_bus_frequencies[idx][:freq_len]
            data[f"Bus_{bus_name}_voltage"] = simulation.secondary_bus_voltages[idx][:volt_len]
            data[f"Bus_{bus_name}_power_budget"] = simulation.secondary_bus_power_budgets[idx][:budget_len]
        
        # Ensure all arrays are the same length by padding with NaN if necessary
        max_len = max(len(arr) for arr in data.values())
        for key in data:
            if len(data[key]) < max_len:
                # Pad with NaN
                pad_len = max_len - len(data[key])
                data[key] = np.append(data[key], [np.nan] * pad_len)
        
        # Convert to DataFrame and save to CSV
        df = pd.DataFrame(data)
        df.to_csv(filepath, index=False)
    
    def _save_summary_file(self, filepath: Path, simulation, description: str, timestamp: datetime):
        """Save human-readable summary file."""
        summary = "POWER SYSTEM SIMULATION SUMMARY\n"
        summary += "=" * 40 + "\n"
        summary += f"Timestamp: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n"
        summary += f"Description: {description}\n"
        summary += f"Duration: {simulation.config.duration:.1f} seconds\n"
        summary += f"Samples: {len(simulation.t)}\n"
        summary += f"Time Step: {simulation.config.dt:.3f} seconds\n\n"
        
        summary += "SYSTEM CONFIGURATION:\n"
        summary += f"- Base Power: {simulation.config.s_base/1e6:.1f} MVA\n"
        summary += f"- Base Voltage: {simulation.config.v_base/1000:.1f} kV\n"
        summary += f"- Base Frequency: {simulation.config.f_base:.1f} Hz\n\n"
        
        summary += "GENERATORS:\n"
        for gen_config in self.config_manager.generators_config:
            power_mw = gen_config.P_setpoint * (simulation.config.s_base / 1e6)
            summary += f"- Generator {gen_config.name}: {gen_config.mode.title()}, "
            summary += f"P={power_mw:.1f}MW"
            if gen_config.disturb_start > 0:
                disturb_mw = gen_config.disturb_value * (simulation.config.s_base / 1e6)
                summary += f", Disturbance at {gen_config.disturb_start:.1f}-{gen_config.disturb_end:.1f}s ({disturb_mw:+.1f}MW)"
            summary += "\n"
        
        summary += "\nLOADS:\n"
        load_config = self.config_manager.load_config
        for bus_id in ['A', 'B', 'C', 'D']:
            load_data = getattr(load_config, bus_id)
            load_p_mw = load_data['load_p'] * (simulation.config.s_base / 1e6)
            load_q_mvar = load_data['load_q'] * (simulation.config.s_base / 1e6)
            summary += f"- Bus {bus_id}: {load_p_mw:.2f} MW, {load_q_mvar:.2f} MVAR\n"
        
        summary += f"\nBREAKERS: "
        breaker_names = ["CB1", "CB2", "CB3", "CB4", "CB5", "CB6", "CB7", "CB8"]
        closed_breakers = [name for i, name in enumerate(breaker_names) if self.config_manager.breaker_status[i]]
        if len(closed_breakers) == 8:
            summary += "All closed\n"
        else:
            summary += f"Closed: {', '.join(closed_breakers)}\n"
        
        # Add basic results analysis if simulation is complete
        if simulation.current_time_index > 100:  # Only if we have enough data
            summary += "\nRESULTS SUMMARY:\n"
            try:
                # Analyze frequency data from first generator
                gen_freq = simulation.system.generators[0].omega * simulation.config.f_base
                min_freq = np.min(gen_freq[:simulation.current_time_index])
                max_freq = np.max(gen_freq[:simulation.current_time_index])
                summary += f"- Frequency Range: {min_freq:.2f} - {max_freq:.2f} Hz\n"
                summary += f"- Frequency Deviation: ±{max(abs(min_freq-60), abs(max_freq-60)):.2f} Hz\n"
            except:
                summary += "- Results analysis not available\n"
        
        with open(filepath, 'w') as f:
            f.write(summary)
    
    def _extract_initial_conditions(self, simulation) -> Dict[str, Any]:
        """Extract initial conditions from simulation."""
        initial_conditions = {
            "generators": {},
            "buses": {}
        }
        
        # Generator initial conditions
        for gen in simulation.system.generators:
            initial_conditions["generators"][gen.name] = {
                "omega": float(gen.omega[0]) if len(gen.omega) > 0 else 1.0,
                "P_mech": float(gen.P_mech[0]) if len(gen.P_mech) > 0 else gen.P_setpoint,
                "P_elec": float(gen.P_elec[0]) if len(gen.P_elec) > 0 else gen.P_setpoint,
                "V_t": float(gen.V_t[0]) if len(gen.V_t) > 0 else gen.V_setpoint
            }
        
        # Bus initial conditions
        initial_conditions["buses"]["main"] = {
            "voltage": simulation.system.main_bus.voltage,
            "frequency": simulation.system.main_bus.frequency
        }
        
        for i, bus in enumerate(simulation.system.secondary_buses):
            bus_id = chr(65 + i)
            initial_conditions["buses"][bus_id] = {
                "voltage": bus.voltage,
                "frequency": bus.frequency
            }
        
        return initial_conditions

    def _update_daily_summary(self, date_folder: Path, description: str,
                             timestamp: datetime, files: Dict[str, Path]):
        """Update daily summary file."""
        daily_summary_file = date_folder / "daily_summary.txt"

        # Read existing summary or create new
        if daily_summary_file.exists():
            with open(daily_summary_file, 'r') as f:
                content = f.read()
        else:
            content = f"DAILY SIMULATION SUMMARY - {date_folder.name}\n"
            content += "=" * 50 + "\n\n"

        # Add new simulation entry
        time_str = timestamp.strftime("%H:%M:%S")
        content += f"{time_str} - {description or 'Untitled simulation'}\n"
        content += f"         Files: {files['config'].name}\n"
        content += f"                {files['data'].name}\n"
        content += f"                {files['summary'].name}\n\n"

        # Write updated summary
        with open(daily_summary_file, 'w') as f:
            f.write(content)

    def _update_global_index(self, files: Dict[str, Path], description: str, timestamp: datetime):
        """Update global index with new simulation."""
        # Load existing index
        with open(self.index_file, 'r') as f:
            index_data = json.load(f)

        # Calculate file sizes
        total_size = sum(f.stat().st_size for f in files.values() if f.exists())

        # Create new simulation entry
        sim_entry = {
            "id": f"sim_{len(index_data['simulations']) + 1:03d}",
            "timestamp": timestamp.isoformat(),
            "description": description,
            "duration": self.config_manager.system_config.duration if self.config_manager.system_config else 15.0,
            "files": {
                "config": str(files['config'].relative_to(self.data_dir)),
                "data": str(files['data'].relative_to(self.data_dir)),
                "summary": str(files['summary'].relative_to(self.data_dir))
            },
            "size_mb": total_size / (1024 * 1024)
        }

        # Add to index
        index_data['simulations'].append(sim_entry)
        index_data['last_updated'] = timestamp.isoformat()

        # Save updated index
        with open(self.index_file, 'w') as f:
            json.dump(index_data, f, indent=2)

    def list_simulations_by_date(self, days_back: int = 30) -> Dict[str, List[Dict]]:
        """List simulations organized by date."""
        simulations_by_date = {}

        # Get date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)

        # Scan date folders
        for date_folder in self.data_dir.iterdir():
            if not date_folder.is_dir() or not re.match(r'\d{4}-\d{2}-\d{2}', date_folder.name):
                continue

            try:
                folder_date = datetime.strptime(date_folder.name, "%Y-%m-%d")
                if start_date <= folder_date <= end_date:
                    simulations_by_date[date_folder.name] = self._scan_date_folder(date_folder)
            except ValueError:
                continue

        return simulations_by_date

    def _scan_date_folder(self, date_folder: Path) -> List[Dict]:
        """Scan a date folder for simulations."""
        simulations = []

        # Look for config files
        for config_file in date_folder.glob("*_config.json"):
            try:
                with open(config_file, 'r') as f:
                    config_data = json.load(f)

                # Extract simulation info
                metadata = config_data.get('metadata', {})
                sim_info = {
                    'config_file': config_file,
                    'timestamp': datetime.fromisoformat(metadata.get('timestamp', '1970-01-01T00:00:00')),
                    'description': metadata.get('description', 'No description'),
                    'duration': metadata.get('duration_seconds', 0),
                    'samples': metadata.get('total_samples', 0)
                }
                simulations.append(sim_info)

            except Exception as e:
                print(f"⚠️ Error reading {config_file}: {e}")

        # Sort by timestamp
        simulations.sort(key=lambda x: x['timestamp'], reverse=True)
        return simulations

    def _analyze_cleanup(self, days_to_keep: int) -> Dict[str, Any]:
        """Analyze what would be cleaned up."""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)

        analysis = {
            'total_folders': 0,
            'total_files': 0,
            'total_mb': 0,
            'folders_to_remove': 0,
            'files_to_remove': 0,
            'mb_to_free': 0,
            'folders_list': []
        }

        for date_folder in self.data_dir.iterdir():
            if not date_folder.is_dir() or not re.match(r'\d{4}-\d{2}-\d{2}', date_folder.name):
                continue

            try:
                folder_date = datetime.strptime(date_folder.name, "%Y-%m-%d")
                analysis['total_folders'] += 1

                # Calculate folder size and file count
                folder_size = 0
                file_count = 0
                for file in date_folder.rglob('*'):
                    if file.is_file():
                        folder_size += file.stat().st_size
                        file_count += 1

                analysis['total_files'] += file_count
                analysis['total_mb'] += folder_size / (1024 * 1024)

                # Check if folder should be removed
                if folder_date < cutoff_date:
                    analysis['folders_to_remove'] += 1
                    analysis['files_to_remove'] += file_count
                    analysis['mb_to_free'] += folder_size / (1024 * 1024)
                    analysis['folders_list'].append(date_folder.name)

            except ValueError:
                continue

        return analysis

    def cleanup_old_simulations(self, days_to_keep: int = 30,
                               confirm_deletion: bool = True) -> Dict[str, int]:
        """
        Clean up simulations older than specified days.

        Args:
            days_to_keep: Number of days to keep
            confirm_deletion: Require user confirmation (default: True)
        """
        # Check if auto-cleanup is enabled
        if (self.config_manager.user_preferences is None or
            not self.config_manager.user_preferences.auto_cleanup_enabled):
            if confirm_deletion:
                # This should be handled by the GUI
                raise ValueError("Auto-cleanup is disabled. Use manual cleanup dialog.")
            else:
                # Allow programmatic cleanup without confirmation
                pass

        return self._perform_cleanup(days_to_keep)

    def _perform_cleanup(self, days_to_keep: int) -> Dict[str, int]:
        """Perform the actual cleanup operation."""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        cleanup_stats = {'folders_removed': 0, 'files_removed': 0, 'mb_freed': 0.0}

        for date_folder in self.data_dir.iterdir():
            if not date_folder.is_dir() or not re.match(r'\d{4}-\d{2}-\d{2}', date_folder.name):
                continue

            try:
                folder_date = datetime.strptime(date_folder.name, "%Y-%m-%d")
                if folder_date < cutoff_date:
                    # Calculate size before deletion
                    folder_size = sum(f.stat().st_size for f in date_folder.rglob('*') if f.is_file())
                    cleanup_stats['mb_freed'] += folder_size / (1024 * 1024)

                    # Count files
                    cleanup_stats['files_removed'] += len(list(date_folder.rglob('*')))

                    # Remove folder
                    shutil.rmtree(date_folder)
                    cleanup_stats['folders_removed'] += 1
                    print(f"🗑️ Removed folder: {date_folder.name}")

            except ValueError:
                continue

        # Log cleanup
        self._log_cleanup(cleanup_stats)
        return cleanup_stats

    def _log_cleanup(self, cleanup_stats: Dict[str, int]):
        """Log cleanup operation."""
        log_file = self.data_dir / "cleanup_log.txt"

        log_entry = f"{datetime.now().isoformat()} - Cleanup performed\n"
        log_entry += f"  Folders removed: {cleanup_stats['folders_removed']}\n"
        log_entry += f"  Files removed: {cleanup_stats['files_removed']}\n"
        log_entry += f"  Space freed: {cleanup_stats['mb_freed']:.2f} MB\n\n"

        # Append to log file
        with open(log_file, 'a') as f:
            f.write(log_entry)

    def get_storage_info(self) -> Dict[str, Any]:
        """Get current storage information."""
        total_size = 0
        folder_count = 0
        file_count = 0

        for date_folder in self.data_dir.iterdir():
            if date_folder.is_dir() and re.match(r'\d{4}-\d{2}-\d{2}', date_folder.name):
                folder_count += 1
                for file in date_folder.rglob('*'):
                    if file.is_file():
                        total_size += file.stat().st_size
                        file_count += 1

        return {
            'total_mb': total_size / (1024 * 1024),
            'folder_count': folder_count,
            'file_count': file_count,
            'data_dir': str(self.data_dir)
        }
