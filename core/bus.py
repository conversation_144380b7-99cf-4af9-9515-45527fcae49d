"""
Classes de barramento para a simulação do sistema elétrico de potência.

Este módulo define as classes MainBus e SecondaryBus que representam
os barramentos no sistema elétrico de potência.
"""

import numpy as np
from typing import List, Optional, Dict, Any

from core.constants import SystemConfig


class MainBus:
    """
    Barramento principal no sistema elétrico de potência.

    O barramento principal é o ponto de conexão central no sistema elétrico de potência, conectando
    a todos os barramentos secundários.

    Atributos:
        config (SystemConfig): Configuração do sistema
        voltage (float): Tensão do barramento em por unidade
        frequency (float): Frequência do barramento em por unidade
        power (float): Potência do barramento em por unidade
    """

    def __init__(self, config: SystemConfig, voltage: float = 1.0, frequency: float = 1.0):
        """
        Inicializa o barramento principal.

        Args:
            config: Configuração do sistema
            voltage: Tensão inicial em por unidade (padrão: 1.0)
            frequency: Frequência inicial em por unidade (padrão: 1.0)
        """
        self.config = config
        self.voltage = voltage
        self.frequency = frequency
        self.power = 0.0

    def update(self, voltage: float, frequency: float, power: float) -> None:
        """
        Atualiza o estado do barramento.

        Args:
            voltage: Nova tensão em por unidade
            frequency: Nova frequência em por unidade
            power: Nova potência em por unidade
        """
        self.voltage = voltage
        self.frequency = frequency
        self.power = power

    def get_voltage_kv(self) -> float:
        """
        Obtém a tensão do barramento em kV.

        Returns:
            Tensão do barramento em kV
        """
        return self.voltage * self.config.v_base / 1000.0

    def get_frequency_hz(self) -> float:
        """
        Obtém a frequência do barramento em Hz.

        Returns:
            Frequência do barramento em Hz
        """
        return self.frequency * self.config.f_base

    def get_power_mw(self) -> float:
        """
        Obtém a potência do barramento em MW.

        Returns:
            Potência do barramento em MW
        """
        return self.power * self.config.s_base / 1e6

    def __str__(self) -> str:
        """Retorna uma representação em string do barramento."""
        return (f"MainBus(voltage={self.voltage:.4f} pu, "
                f"frequency={self.frequency:.4f} pu, "
                f"power={self.power:.4f} pu)")


class SecondaryBus:
    """
    Barramento secundário no sistema elétrico de potência.

    Os barramentos secundários estão conectados ao barramento principal e podem ter geradores
    conectados a eles.

    Atributos:
        config (SystemConfig): Configuração do sistema
        bus_id (str): Identificador do barramento
        voltage (float): Tensão do barramento em por unidade
        frequency (float): Frequência do barramento em por unidade
        power (float): Potência do barramento em por unidade
        generator (Optional[Generator]): Gerador principal conectado ao barramento (para compatibilidade)
        generators (List[Generator]): Lista de todos os geradores conectados ao barramento
    """

    def __init__(self, config: SystemConfig, bus_id: str, voltage: float = 1.0, frequency: float = 1.0):
        """
        Inicializa o barramento secundário.

        Args:
            config: Configuração do sistema
            bus_id: Identificador do barramento
            voltage: Tensão inicial em por unidade (padrão: 1.0)
            frequency: Frequência inicial em por unidade (padrão: 1.0)
        """
        self.config = config
        self.bus_id = bus_id
        self.voltage = voltage
        self.frequency = frequency
        self.power = 0.0
        self.generator = None  # Mantido para compatibilidade com código existente
        self.generators = []   # Nova lista para armazenar todos os geradores

        # Parâmetros de carga
        self.load_p = 0.0  # Carga ativa em watts
        self.load_q = 0.0  # Carga reativa em volt-amperes reativos

        # Parâmetros elétricos
        self.r_eq = 0.01  # Resistência equivalente em por unidade
        self.x_eq = 0.1   # Reatância equivalente em por unidade
        self.y_eq = 1 / (self.r_eq + 1j * self.x_eq)  # Admitância equivalente

    def add_generator(self, generator) -> None:
        """
        Conecta um gerador ao barramento.

        Args:
            generator: Gerador a ser conectado
        """
        self.generator = generator  # Mantido para compatibilidade
        generator.bus = self

        # Adiciona à lista de geradores se ainda não estiver lá
        if generator not in self.generators:
            self.generators.append(generator)

    def update(self, voltage: float, frequency: float, power: float) -> None:
        """
        Atualiza o estado do barramento.

        Args:
            voltage: Nova tensão em por unidade
            frequency: Nova frequência em por unidade
            power: Nova potência em por unidade
        """
        self.voltage = voltage
        self.frequency = frequency
        self.power = power

        # Atualiza a frequência de todos os geradores conectados
        for gen in self.generators:
            if gen.is_connected:
                gen.update_frequency(frequency)

        # Mantém compatibilidade com código existente
        if self.generator and self.generator.is_connected:
            self.generator.update_frequency(frequency)

    def get_voltage_kv(self) -> float:
        """
        Obtém a tensão do barramento em kV.

        Returns:
            Tensão do barramento em kV
        """
        return self.voltage * self.config.v_base / 1000.0

    def get_frequency_hz(self) -> float:
        """
        Obtém a frequência do barramento em Hz.

        Returns:
            Frequência do barramento em Hz
        """
        return self.frequency * self.config.f_base

    def get_power_mw(self) -> float:
        """
        Obtém a potência do barramento em MW.

        Returns:
            Potência do barramento em MW
        """
        return self.power * self.config.s_base / 1e6

    def __str__(self) -> str:
        """Retorna uma representação em string do barramento."""
        return (f"SecondaryBus(id={self.bus_id}, "
                f"voltage={self.voltage:.4f} pu, "
                f"frequency={self.frequency:.4f} pu, "
                f"power={self.power:.4f} pu)")
