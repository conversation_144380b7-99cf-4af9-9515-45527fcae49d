# test_plc.py
# Script simples para testar a comunicação com o PLC

from pylogix import PLC
import time

def test_plc_connection(plc_ip="*************"):
    """Testa a conexão com o PLC."""
    print(f"Testando conexão com o PLC em {plc_ip}...")
    plc = None
    
    try:
        plc = PLC()
        plc.IPAddress = plc_ip
        
        # Test connection with GetPLCTime
        ret = plc.GetPLCTime()
        
        if ret.Status == "Success":
            print(f"Conexão bem-sucedida! Hora do PLC: {ret.Value}")
            return True
        else:
            print(f"Falha na conexão: {ret.Status}")
            return False
            
    except Exception as e:
        print(f"Erro ao conectar ao PLC: {e}")
        return False
        
    finally:
        if plc:
            plc.Close()

if __name__ == "__main__":
    # Teste com o IP padrão
    test_plc_connection()
    
    # Aguarde um momento
    time.sleep(1)
    
    # Teste com um IP alternativo
    test_plc_connection("************")
