<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,Unidade,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,Mapeamento
TGCPA_GSP.I86MCBC,Gen Breaker Closed And GT Not Tripped,BOOL,-,Sim,LEITURA,breaker_status[4]
TGCPA_GSP.I4GNLSA,Available For Loadshare And Not In Droop,BO<PERSON>,-,Sim,<PERSON><PERSON><PERSON><PERSON>,True
TGCPA_GSP.I4GNEXC,Excitation Is On,BOOL,-,Sim,LEITURA,True
TGCPA_GSP.I45FP1A,CO2 FAULT,BOOL,-,Sim,LEITURA,False
TGCPA_GSP.I45FPD1,CO2 DISCHARGE,BOOL,-,Sim,LEITURA,False
TGCPA_GSP.I45FPHH1,Fire Confirmed,<PERSON>OOL,-,Sim,LEITURA,False
TGCPA_GSP.I71GPHH1,Gas Confirmed,BOOL,-,Sim,LEITURA,False
TGCPA_GSP.I45FPCRFA,Control Room CO2 Fault,<PERSON><PERSON><PERSON>,-,<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>GCPA_GSP.I45FPCRDA,Control Room CO2 Discharge,<PERSON><PERSON><PERSON>,-,<PERSON><PERSON>,<PERSON>EITURA,False
TGCPA_GSP.I45FPCRHHA,Control Room Fire Confirmed,BOOL,-,Sim,LEITURA,False
TGCPA_GSP.I71GPCRHHA,Control Room Gas Confirmed,BOOL,-,Sim,LEITURA,False
TGCPA_GSP.ITGCP_GSP12,Spare Boolean,BOOL,-,Nao,LEITURA,0.0
TGCPA_GSP.ITGCP_GSP13,Spare Boolean,BOOL,-,Nao,LEITURA,0.0
TGCPA_GSP.ITGCP_GSP14,Spare Boolean,BOOL,-,Nao,LEITURA,0.0
TGCPA_GSP.ITGCP_GSP15,Spare Boolean,BOOL,-,Nao,LEITURA,0.0
TGCPA_GSP.ITGCP_GSP16,Spare Boolean,BOOL,-,Nao,LEITURA,0.0
TGCPA_GSP.A54GNMWBUD,Generator Power Budget,REAL,kW,Sim,LEITURA,power_budget_A
TGCPA_GSP.A54GNMW,Generator Power MW,REAL,MW,Sim,LEITURA,power_A
TGCPA_GSP.A54GNMVAR,Generator Reactive Power MVAr,REAL,MVAr,Sim,LEITURA,reactive_power_A
TGCPA_GSP.A54GNKV,Generator Voltage kV,REAL,kV,Sim,LEITURA,voltage_A
TGCPA_GSP.A54GNF,Generator Frequency Hz,REAL,Hz,Sim,LEITURA,frequency_A
TGCPA_GSP.ATGCP_GSP6,Spare Analog,REAL,-,Sim,LEITURA,False
TGCPA_GSP.ATGCP_GSP7,Spare Analog,REAL,-,Sim,LEITURA,False
TGCPA_GSP.ATGCP_GSP8,Spare Analog,REAL,-,Sim,LEITURA,False
TGCPA_GSP.ATGCP_GSP9,Spare Analog,REAL,-,Sim,LEITURA,False
TGCPA_GSP.ATGCP_GSP10,Spare Analog,REAL,-,Sim,LEITURA,False
TGCPB_GSP.I86MCBC,Gen Breaker Closed And GT Not Tripped,BOOL,-,Sim,LEITURA,breaker_status[5]
TGCPB_GSP.I4GNLSA,Available For Loadshare And Not In Droop,BOOL,-,Sim,LEITURA,False
TGCPB_GSP.I4GNEXC,Excitation Is On,BOOL,-,Sim,LEITURA,True
TGCPB_GSP.I45FP1A,CO2 FAULT,BOOL,-,Sim,LEITURA,False
TGCPB_GSP.I45FPD1,CO2 DISCHARGE,BOOL,-,Sim,LEITURA,False
TGCPB_GSP.I45FPHH1,Fire Confirmed,BOOL,-,Sim,LEITURA,False
TGCPB_GSP.ITGCP_GSP12,Spare Boolean,BOOL,-,Nao,LEITURA,False
TGCPB_GSP.ITGCP_GSP13,Spare Boolean,BOOL,-,Nao,LEITURA,False
TGCPB_GSP.ITGCP_GSP14,Spare Boolean,BOOL,-,Nao,LEITURA,False
TGCPB_GSP.ITGCP_GSP15,Spare Boolean,BOOL,-,Nao,LEITURA,False
TGCPB_GSP.ITGCP_GSP16,Spare Boolean,BOOL,-,Nao,LEITURA,False
TGCPB_GSP.A54GNMWBUD,Generator Power Budget,REAL,kW,Sim,LEITURA,power_budget_B
TGCPB_GSP.A54GNMW,Generator Power MW,REAL,MW,Sim,LEITURA,power_B
TGCPB_GSP.A54GNMVAR,Generator Reactive Power MVAr,REAL,MVAr,Sim,LEITURA,reactive_power_B
TGCPB_GSP.A54GNKV,Generator Voltage kV,REAL,kV,Sim,LEITURA,voltage_B
TGCPB_GSP.A54GNF,Generator Frequency Hz,REAL,Hz,Sim,LEITURA,frequency_B
TGCPB_GSP.ATGCP_GSP6,Spare Analog,REAL,-,Nao,LEITURA,0.0
TGCPB_GSP.ATGCP_GSP7,Spare Analog,REAL,-,Nao,LEITURA,0.0
TGCPB_GSP.ATGCP_GSP8,Spare Analog,REAL,-,Nao,LEITURA,0.0
TGCPB_GSP.ATGCP_GSP9,Spare Analog,REAL,-,Nao,LEITURA,0.0
TGCPB_GSP.ATGCP_GSP10,Spare Analog,REAL,-,Nao,LEITURA,0.0
TGCPC_GSP.I86MCBC,Gen Breaker Closed And GT Not Tripped,BOOL,-,Sim,LEITURA,breaker_status[6]
TGCPC_GSP.I4GNLSA,Available For Loadshare And Not In Droop,BOOL,-,Sim,LEITURA,False
TGCPC_GSP.I4GNEXC,Excitation Is On,BOOL,-,Sim,LEITURA,True
TGCPC_GSP.I45FPFX,CO2 FAULT,BOOL,-,Sim,LEITURA,False
TGCPC_GSP.I45FPDX,CO2 DISCHARGE,BOOL,-,Sim,LEITURA,False
TGCPC_GSP.I45FPHHX,Fire Confirmed,BOOL,-,Sim,LEITURA,False
TGCPC_GSP.ITGCP_GSP12,Spare Boolean,BOOL,-,Nao,LEITURA,False
TGCPC_GSP.ITGCP_GSP13,Spare Boolean,BOOL,-,Nao,LEITURA,False
TGCPC_GSP.ITGCP_GSP14,Spare Boolean,BOOL,-,Nao,LEITURA,False
TGCPC_GSP.ITGCP_GSP15,Spare Boolean,BOOL,-,Nao,LEITURA,False
TGCPC_GSP.ITGCP_GSP16,Spare Boolean,BOOL,-,Nao,LEITURA,False
TGCPC_GSP.A54GNMWBUD,Generator Power Budget,REAL,kW,Sim,LEITURA,power_budget_C
TGCPC_GSP.A54GNMW,Generator Power MW,REAL,MW,Sim,LEITURA,power_C
TGCPC_GSP.A54GNMVAR,Generator Reactive Power MVAr,REAL,MVAr,Sim,LEITURA,reactive_power_C
TGCPC_GSP.A54GNKV,Generator Voltage kV,REAL,kV,Sim,LEITURA,voltage_C
TGCPC_GSP.A54GNF,Generator Frequency Hz,REAL,Hz,Sim,LEITURA,frequency_C
TGCPC_GSP.ATGCP_GSP6,Spare Analog,REAL,-,Nao,LEITURA,0.0
TGCPC_GSP.ATGCP_GSP7,Spare Analog,REAL,-,Nao,LEITURA,0.0
TGCPC_GSP.ATGCP_GSP8,Spare Analog,REAL,-,Nao,LEITURA,0.0
TGCPC_GSP.ATGCP_GSP9,Spare Analog,REAL,-,Nao,LEITURA,0.0
TGCPC_GSP.ATGCP_GSP10,Spare Analog,REAL,-,Nao,LEITURA,0.0
TGCPD_GSP.I86MCBC,Gen Breaker Closed And GT Not Tripped,BOOL,-,Sim,LEITURA,breaker_status[7]
TGCPD_GSP.I4GNLSA,Available For Loadshare And Not In Droop,BOOL,-,Sim,LEITURA,False
TGCPD_GSP.I4GNEXC,Excitation Is On,BOOL,-,Sim,LEITURA,True
TGCPD_GSP.I45FP1A,CO2 FAULT,BOOL,-,Sim,LEITURA,False
TGCPD_GSP.I45FPD1,CO2 DISCHARGE,BOOL,-,Sim,LEITURA,False
TGCPD_GSP.I45FPHH1,Fire Confirmed,BOOL,-,Sim,LEITURA,False
TGCPD_GSP.ITGCP_GSP12,Spare Boolean,BOOL,-,Nao,LEITURA,False
TGCPD_GSP.ITGCP_GSP13,Spare Boolean,BOOL,-,Nao,LEITURA,False
TGCPD_GSP.ITGCP_GSP14,Spare Boolean,BOOL,-,Nao,LEITURA,False
TGCPD_GSP.ITGCP_GSP15,Spare Boolean,BOOL,-,Nao,LEITURA,False
TGCPD_GSP.ITGCP_GSP16,Spare Boolean,BOOL,-,Nao,LEITURA,False
TGCPD_GSP.A54GNMWBUD,Generator Power Budget,REAL,kW,Sim,LEITURA,power_budget_D
TGCPD_GSP.A54GNMW,Generator Power MW,REAL,MW,Sim,LEITURA,power_D
TGCPD_GSP.A54GNMVAR,Generator Reactive Power MVAr,REAL,MVAr,Sim,LEITURA,reactive_power_D
TGCPD_GSP.A54GNKV,Generator Voltage kV,REAL,kV,Sim,LEITURA,voltage_D
TGCPD_GSP.A54GNF,Generator Frequency Hz,REAL,Hz,Sim,LEITURA,frequency_D
TGCPD_GSP.ATGCP_GSP6,Spare Analog,REAL,-,Nao,LEITURA,0.0
TGCPD_GSP.ATGCP_GSP7,Spare Analog,REAL,-,Nao,LEITURA,0.0
TGCPD_GSP.ATGCP_GSP8,Spare Analog,REAL,-,Nao,LEITURA,0.0
TGCPD_GSP.ATGCP_GSP9,Spare Analog,REAL,-,Nao,LEITURA,0.0
TGCPD_GSP.ATGCP_GSP10,Spare Analog,REAL,-,Nao,LEITURA,0.0
I27DC1,MAIN POWER 24VDC LOW ALARM,BOOL,-,Sim,LEITURA,False
I27DC2,MAIN POWER 24VDC LOW ALARM,BOOL,-,Sim,LEITURA,False
I74ENET1,ETHERNET SWITCH 1 FAULT,BOOL,-,Sim,LEITURA,False
I52CS1,CLOSE CIRCUIT BREAKER COMMAND SWITCH 52-2A,BOOL,-,Sim,LEITURA,None
I52CS2,CLOSE CIRCUIT BREAKER COMMAND SWITCH 52-2B,BOOL,-,Sim,LEITURA,None
I52CS3,CLOSE CIRCUIT BREAKER COMMAND SWITCH 52-2C,BOOL,-,Sim,LEITURA,None
I52CS4,CLOSE CIRCUIT BREAKER COMMAND SWITCH 52-2D,BOOL,-,Sim,LEITURA,None
I52BB2AO,TIE BREAKER 52-2A OPEN,BOOL,-,Sim,LEITURA,False
I52BB2AC,TIE BREAKER 52-2A CLOSE,BOOL,-,Sim,LEITURA,False
I52BB2BO,TIE BREAKER 52-2B OPEN,BOOL,-,Sim,LEITURA,False
I52BB2BC,TIE BREAKER 52-2B CLOSE,BOOL,-,Sim,LEITURA,False
I52BB2CO,TIE BREAKER 52-2C OPEN,BOOL,-,Sim,LEITURA,False
I52BB2CC,TIE BREAKER 52-2C CLOSE,BOOL,-,Sim,LEITURA,False
I52BB2DO,TIE BREAKER 52-2D OPEN,BOOL,-,Sim,LEITURA,False
I52BB2DC,TIE BREAKER 52-2D CLOSE,BOOL,-,Sim,LEITURA,False
I04TB2A,TIE BREAKER 52-2A AVAILABLE,BOOL,-,Sim,LEITURA,False
I04TB2B,TIE BREAKER 52-2B AVAILABLE,BOOL,-,Sim,LEITURA,False
I04TB2C,TIE BREAKER 52-2C AVAILABLE,BOOL,-,Sim,LEITURA,False
I04TB2D,TIE BREAKER 52-2D AVAILABLE,BOOL,-,Sim,LEITURA,False
I18ASL1,AUTOMATIC SPEED LOWER FROM 25ASR1,BOOL,-,Sim,LEITURA,False
I18ASR1,AUTOMATIC SPEED RAISE FROM 25ASR1,BOOL,-,Sim,LEITURA,False
I90VRL1,AUTOMATIC VOLTAGE LOWER FROM 25ASR1,BOOL,-,Sim,LEITURA,False
I90VRR1,AUTOMATIC VOLTAGE RAISE FROM 25ASR1,BOOL,-,Sim,LEITURA,False
I18ASL2,AUTOMATIC SPEED LOWER FROM 25ASR2,BOOL,-,Sim,LEITURA,False
I18ASR2,AUTOMATIC SPEED RAISE FROM 25ASR2,BOOL,-,Sim,LEITURA,False
I90VRL2,AUTOMATIC VOLTAGE LOWER FROM 25ASR2,BOOL,-,Sim,LEITURA,False
I90VRR2,AUTOMATIC VOLTAGE RAISE FROM 25ASR2,BOOL,-,Sim,LEITURA,False
I18ASL3,AUTOMATIC SPEED LOWER FROM 25ASR3,BOOL,-,Sim,LEITURA,False
I18ASR3,AUTOMATIC SPEED RAISE FROM 25ASR3,BOOL,-,Sim,LEITURA,False
I90VRL3,AUTOMATIC VOLTAGE LOWER FROM 25ASR3,BOOL,-,Sim,LEITURA,False
I90VRR3,AUTOMATIC VOLTAGE RAISE FROM 25ASR3,BOOL,-,Sim,LEITURA,False
I18ASL4,AUTOMATIC SPEED LOWER FROM 25ASR4,BOOL,-,Sim,LEITURA,False
I18ASR4,AUTOMATIC SPEED RAISE FROM 25ASR4,BOOL,-,Sim,LEITURA,False
I90VRL4,AUTOMATIC VOLTAGE LOWER FROM 25ASR4,BOOL,-,Sim,LEITURA,False
I90VRR4,AUTOMATIC VOLTAGE RAISE FROM 25ASR4,BOOL,-,Sim,LEITURA,False
O86BA101A,Bus A Breaker 101A Gas Compr MCB Trip Command,BOOL,-,Sim,ESCRITA,None
O86BA101B,Bus B Breaker 101B Gas Compr MCB Trip Command,BOOL,-,Sim,ESCRITA,None
O86BA101C,Bus A Breaker 101C Gas Compr MCB Trip Command,BOOL,-,Sim,ESCRITA,None
O86BA101D,Bus B Breaker 101D Gas Compr MCB Trip Command,BOOL,-,Sim,ESCRITA,None
O86BA101E,Bus C Breaker 101E Gas Compr MCB Trip Command,BOOL,-,Sim,ESCRITA,None
O86BA102A,Bus A Breaker 102A Propane Cooling Compr MCB Trip Command,BOOL,-,Sim,ESCRITA,None
O86BA102B,Bus B Breaker 102B Propane Cooling Compr MCB Trip Command,BOOL,-,Sim,ESCRITA,None
O86BA102C,Bus C Breaker 102C Propane Cooling Compr MCB Trip Command,BOOL,-,Sim,ESCRITA,None
O86BA202A,Bus A Breaker 202A Recycle Gas Compr MCB Breaker Trip Command,BOOL,-,Sim,ESCRITA,None
O86BA202B,Bus B Breaker 202B Recycle Gas Compr MCB Breaker Trip Command,BOOL,-,Sim,ESCRITA,None
O86BC001A,Bus D Breaker 001A TF-123001A Trip Command,BOOL,-,Sim,ESCRITA,None
O86BD001B,Bus D Breaker 001A TF-123001B Trip Command,BOOL,-,Sim,ESCRITA,None
O86BD3102A,Bus D Breaker M-C-3123102A Trip Command,BOOL,-,Sim,ESCRITA,None
O86BD3102B,Bus D Breaker M-C-3123102B Trip Command,BOOL,-,Sim,ESCRITA,None
O03BB01A,Bus Tie Breaker Open Command 52-2A,BOOL,-,Sim,ESCRITA,None
O03BB01B,Bus Tie Breaker Open Command 52-2B,BOOL,-,Sim,ESCRITA,None
O03BB01C,Bus Tie Breaker Open Command 52-2C,BOOL,-,Sim,ESCRITA,None
O03BB01D,Bus Tie Breaker Open Command 52-2D,BOOL,-,Sim,ESCRITA,None
O25ASRX1,Enable/Disable Auto Synchronizer 25ASR1,BOOL,-,Sim,ESCRITA,None
O25ASRX2,Enable/Disable Auto Synchronizer 25ASR2,BOOL,-,Sim,ESCRITA,None
O25ASRX3,Enable/Disable Auto Synchronizer 25ASR3,BOOL,-,Sim,ESCRITA,None
O25ASRX4,Enable/Disable Auto Synchronizer 25ASR4,BOOL,-,Sim,ESCRITA,None
O25CSRX1,Enable/Disable Check Synchronizer 25ASR1,BOOL,-,Sim,ESCRITA,None
O25CSRX2,Enable/Disable Check Synchronizer 25ASR2,BOOL,-,Sim,ESCRITA,None
O25CSRX3,Enable/Disable Check Synchronizer 25ASR3,BOOL,-,Sim,ESCRITA,None
O25CSRX4,Enable/Disable Check Synchronizer 25ASR4,BOOL,-,Sim,ESCRITA,None
TGCPA_GSP.A54GNMWBUD,Generator Power Budget,REAL,kW,Sim,LEITURA,power_budget_A