# Simulação de Sistema Elétrico de Potência

Este projeto implementa uma simulação avançada de sistema elétrico de potência com múltiplos geradores e barramentos, usando implementações simplificadas para cálculos de fluxo de potência e dinâmica de geradores.

## ✨ Características Principais

### 🔌 Sistema Elétrico
- Simulação de sistema elétrico de potência com 4 geradores e 5 barramentos (1 principal e 4 secundários)
- Simulação de dinâmica de geradores com controle síncrono e droop
- Sistema de controle automático de tensão (AVR)
- Controle de disjuntores para alterar a topologia do sistema
- Simulação de perturbações e resposta transitória do sistema

### 🖥️ Interface e Visualização
- Interface gráfica moderna para visualização e controle da simulação
- Visualização de gráficos de potência, frequência, tensão e corrente
- Esquemático interativo do sistema elétrico
- Controles em tempo real para parâmetros do sistema

### ⚙️ Sistema de Configuração Unificado (Novo!)
- **Configuração modular** separada em arquivos especializados
- **Migração automática** de configurações antigas
- **Validação de configuração** com relatórios de erro
- **Preferências de usuário** personalizáveis
- **Gerenciamento de dados** com organização por data

### 💾 Gerenciamento de Dados Avançado (Novo!)
- **Salvamento automático** de dados de simulação
- **Organização por data** em pastas estruturadas
- **Formato JSON + CSV** para máxima compatibilidade
- **Carregamento de dados** para análise posterior
- **Limpeza automática** opcional de dados antigos

## 📋 Requisitos

- Python 3.10 ou superior
- Bibliotecas: numpy, matplotlib, wxPython, pandas

## 🚀 Instalação

```bash
pip install numpy matplotlib wxPython pandas
```

## ▶️ Execução

### Execução Normal
```bash
python main.py
```

### Testar Sistema de Configuração
```bash
python test_config.py
```

### Opções de Linha de Comando
```bash
# Usar sistema de configuração legado
python main.py --legacy

# Testar novo sistema de configuração
python main.py --test-config
```

## ⚙️ Sistema de Configuração

O sistema agora utiliza um **sistema de configuração unificado** com arquivos separados:

- **`config/system_config.json`**: Parâmetros do sistema elétrico
- **`config/generators_config.json`**: Configurações dos geradores
- **`config/user_preferences.json`**: Preferências do usuário
- **`config/default_scenarios.json`**: Cenários pré-definidos

Para instruções detalhadas, consulte [config_instructions.md](config_instructions.md).

## 📁 Estrutura do Projeto

```
projeto/
├── main.py                     # Ponto de entrada principal
├── test_config.py              # Script de teste do sistema de configuração
├── README.md                   # Este arquivo
├── config_instructions.md      # Instruções de configuração
├── core/                       # Módulos principais da simulação
│   ├── generator.py            # Modelos de geradores
│   ├── simulation.py           # Simulação do sistema elétrico
│   ├── constants.py            # Constantes (legado)
│   ├── voltage_controller.py   # Controle automático de tensão
│   ├── configuration.py        # Sistema de configuração unificado (NOVO)
│   └── data_manager.py         # Gerenciamento de dados (NOVO)
├── gui/                        # Interface gráfica
│   ├── gui_fixed.py            # Interface principal
│   └── panels.py               # Painéis de informação
├── config/                     # Arquivos de configuração (NOVO)
│   ├── system_config.json      # Configuração do sistema
│   ├── generators_config.json  # Configuração dos geradores
│   ├── user_preferences.json   # Preferências do usuário
│   └── default_scenarios.json  # Cenários pré-definidos
└── simulation_data/            # Dados de simulação (NOVO)
    ├── index.json              # Índice global de simulações
    ├── 2024-01-15/             # Dados organizados por data
    │   ├── simulation_*_config.json
    │   ├── simulation_*_data.csv
    │   └── simulation_*_summary.txt
    └── cleanup_log.txt          # Log de limpeza automática
```

## 🎮 Como Usar

### 1. Primeira Execução
```bash
python main.py
```

### 2. Interface Gráfica
A interface será aberta mostrando:
- **Esquemático do sistema elétrico** com disjuntores interativos
- **Gráficos em tempo real** de potência, frequência, tensão e corrente
- **Controles dos disjuntores** para alterar topologia
- **Informações dos geradores** com parâmetros atuais
- **Seletor de tipo de gráfico** unificado

### 3. Controles Disponíveis
- **Disjuntores**: Clique para abrir/fechar (CB1-CB8)
- **Tipo de Gráfico**: Selecione entre Potência, Frequência, Tensão, Corrente, Balanço Potência
- **Salvar Dados**: Botão para salvar simulação atual
- **Carregar Dados**: Carregue simulações anteriores (em desenvolvimento)

### 4. Gerenciamento de Dados
- **Salvamento automático**: Dados organizados por data em `simulation_data/`
- **Formato completo**: JSON (configuração) + CSV (dados) + TXT (resumo)
- **Reprodutibilidade**: Todos os parâmetros salvos para reprodução exata

## ⚙️ Configuração Avançada

### Arquivos de Configuração

#### `config/system_config.json`
```json
{
  "system": {
    "s_base": 30000000.0,    // Potência base (VA)
    "v_base": 13800.0,       // Tensão base (V)
    "f_base": 60.0,          // Frequência base (Hz)
    "dt": 0.01,              // Passo de tempo (s)
    "duration": 15.0         // Duração da simulação (s)
  }
}
```

#### `config/generators_config.json`
- Configurações individuais dos 4 geradores (A, B, C, D)
- Parâmetros de controle (governador, AVR)
- Configurações de perturbação

#### `config/user_preferences.json`
- Preferências da interface (tamanho da janela, tipo de gráfico padrão)
- Configurações de armazenamento (pastas por data, limpeza automática)
- Configurações de desempenho (frequência de atualização)

## 🏗️ Arquitetura

O sistema utiliza uma **arquitetura modular avançada** com separação clara entre:

### Camadas Principais
- **🔧 Modelo Físico**: Geradores, barramentos, cargas (rede simplificada)
- **⚡ Simulação**: Dinâmica temporal e cálculos simplificados de fluxo de potência
- **🖥️ Interface**: Visualização e controle em tempo real
- **⚙️ Configuração**: Sistema unificado de gerenciamento de parâmetros
- **💾 Dados**: Gerenciamento avançado com organização temporal

### Padrões de Design
- **Separação de responsabilidades**: Cada módulo tem função específica
- **Configuração externa**: Parâmetros separados do código
- **Extensibilidade**: Fácil adição de novos recursos
- **Manutenibilidade**: Código organizado e documentado

## 🔬 Características Técnicas

### Sistema Elétrico
- **Simulação simplificada**: Não requer bibliotecas externas complexas
- **Dinâmica de geradores**: Modelos de turbina, governador e AVR
- **Controle de frequência**: Modo síncrono e droop
- **Controle de tensão**: Sistema AVR com compartilhamento de potência reativa
- **Perturbações**: Simulação de distúrbios configuráveis no sistema

### Interface e Dados
- **Visualização em tempo real**: Gráficos atualizados durante a simulação
- **Interface responsiva**: Controles intuitivos e feedback visual
- **Persistência de dados**: Salvamento completo para reprodutibilidade
- **Compatibilidade**: Formatos padrão (JSON, CSV) para interoperabilidade

### Configuração e Preferências
- **Migração automática**: Atualização transparente de configurações antigas
- **Validação robusta**: Verificação de integridade dos parâmetros
- **Personalização**: Preferências individuais por usuário
- **Cenários**: Configurações pré-definidas para casos de estudo

## 🆕 Novidades da Versão 2.0

### Sistema de Configuração Unificado
- ✅ **Migração automática** de configurações antigas
- ✅ **Arquivos especializados** para diferentes aspectos do sistema
- ✅ **Validação robusta** com relatórios de erro detalhados
- ✅ **Preferências de usuário** personalizáveis

### Gerenciamento de Dados Avançado
- ✅ **Organização por data** em estrutura hierárquica
- ✅ **Formato JSON + CSV** para máxima compatibilidade
- ✅ **Reprodutibilidade completa** com todos os parâmetros salvos
- ✅ **Limpeza automática** opcional (desabilitada por padrão)

### Interface Melhorada
- ✅ **Seletor unificado** de tipo de gráfico
- ✅ **Legenda posicionada** abaixo dos gráficos
- ✅ **Controles simplificados** para melhor usabilidade

## 🚀 Próximos Passos

- 🔄 **Carregamento de dados**: Interface para carregar simulações anteriores
- 📊 **Análise comparativa**: Comparação entre múltiplas simulações
- 🎛️ **Configuração via GUI**: Interface gráfica para editar parâmetros
- 📈 **Relatórios avançados**: Geração automática de relatórios técnicos

## 📚 Documentação Adicional

- [Instruções de Configuração](config_instructions.md) - Guia detalhado do sistema de configuração
- [Manual do Usuário](user_manual.md) - Guia completo de uso (em desenvolvimento)
- [Guia do Desenvolvedor](developer_guide.md) - Documentação técnica (em desenvolvimento)

## 📄 Licença

### Licença do Projeto
Este projeto é distribuído sob a **Licença MIT**.

### Análise de Compatibilidade de Licenças

Este software utiliza as seguintes bibliotecas de terceiros:

| Biblioteca | Licença | Compatibilidade |
|------------|---------|-----------------|
| **NumPy** | BSD-3-Clause | ✅ Totalmente compatível |
| **Matplotlib** | PSF-based (BSD-compatible) | ✅ Totalmente compatível |
| **pandas** | BSD-3-Clause | ✅ Totalmente compatível |
| **wxPython** | wxWindows Library License | ✅ Compatível (LGPL-like com exceção) |
| **Python** | PSF License | ✅ Totalmente compatível |

### Sobre a Licença MIT

A **Licença MIT** é uma licença de software livre permissiva que:

- ✅ **Permite uso comercial** - Você pode usar este software em projetos comerciais
- ✅ **Permite modificação** - Você pode modificar o código conforme necessário
- ✅ **Permite distribuição** - Você pode distribuir o software original ou modificado
- ✅ **Permite uso privado** - Você pode usar o software para fins privados
- ✅ **Compatível com GPL** - Pode ser combinada com código GPL se necessário

### Responsabilidades

Ao usar este software, você deve:

- 📋 **Incluir a licença** - Manter o aviso de copyright e licença em todas as cópias
- 📋 **Incluir o aviso de copyright** - Creditar os autores originais

### Isenção de Responsabilidade

Este software é fornecido "como está", sem garantias de qualquer tipo. Os autores não se responsabilizam por danos decorrentes do uso deste software.

### Texto Completo da Licença MIT

```
MIT License

Copyright (c) 2024 Sistema de Simulação Elétrica de Potência

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

### Licenças das Dependências

Para informações detalhadas sobre as licenças das bibliotecas utilizadas:

- **NumPy**: [BSD-3-Clause License](https://github.com/numpy/numpy/blob/main/LICENSE.txt)
- **Matplotlib**: [PSF-based License](https://matplotlib.org/stable/project/license.html)
- **pandas**: [BSD-3-Clause License](https://github.com/pandas-dev/pandas/blob/main/LICENSE)
- **wxPython**: [wxWindows Library License](https://wxpython.org/pages/license/index.html)
- **Python**: [PSF License](https://docs.python.org/3/license.html)
