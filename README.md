# Simulação de Sistema Elétrico de Potência

Este projeto implementa uma simulação avançada de sistema elétrico de potência com múltiplos geradores e barramentos, usando implementações simplificadas para cálculos de fluxo de potência e dinâmica de geradores.

## ✨ Características Principais

### 🔌 Sistema Elétrico
- Simulação de sistema elétrico de potência com 4 geradores e 5 barramentos (1 principal e 4 secundários)
- Simulação de dinâmica de geradores com controle síncrono e droop
- Sistema de controle automático de tensão (AVR)
- Controle de disjuntores para alterar a topologia do sistema
- Simulação de perturbações e resposta transitória do sistema

### 🖥️ Interface e Visualização
- Interface gráfica moderna para visualização e controle da simulação
- Visualização de gráficos de potência, frequência, tensão e corrente
- Esquemático interativo do sistema elétrico
- Controles em tempo real para parâmetros do sistema

### ⚙️ Sistema de Configuração Unificado (Novo!)
- **Configuração modular** separada em arquivos especializados
- **Migração automática** de configurações antigas
- **Validação de configuração** com relatórios de erro
- **Preferências de usuário** personalizáveis
- **Gerenciamento de dados** com organização por data

### 💾 Gerenciamento de Dados Avançado (Novo!)
- **Salvamento automático** de dados de simulação
- **Organização por data** em pastas estruturadas
- **Formato JSON + CSV** para máxima compatibilidade
- **Carregamento de dados** para análise posterior
- **Limpeza automática** opcional de dados antigos

## Requisitos

- Python 3.10 ou superior
- Bibliotecas: numpy, matplotlib, wxPython

## Instalação

```bash
pip install numpy matplotlib wxPython
```

## Execução

```bash
python main.py
```

## Configuração

O sistema pode ser configurado através do arquivo `config.json`. Para instruções detalhadas sobre como configurar o sistema, consulte o arquivo [config_instructions.md](config_instructions.md).

## Estrutura do Projeto

- `main.py`: Ponto de entrada principal da aplicação
- `core/`: Módulos principais da simulação
  - `generator.py`: Implementação dos modelos de geradores
  - `simulation.py`: Implementação da simulação do sistema elétrico
  - `constants.py`: Constantes e configurações do sistema
  - `voltage_controller.py`: Sistema de controle automático de tensão
- `gui/`: Módulos da interface gráfica
  - `gui_fixed.py`: Implementação da interface gráfica principal
  - `panels.py`: Painéis de informações e controle
- `config/`: Arquivos de configuração
  - `config.json`: Configuração principal do sistema

## Uso

1. Execute o arquivo principal:
```bash
python main.py
```

2. A interface gráfica será aberta mostrando:
   - Esquemático do sistema elétrico
   - Gráficos de potência, frequência, tensão e corrente
   - Controles dos disjuntores
   - Informações dos geradores

3. Use os controles para:
   - Abrir/fechar disjuntores
   - Visualizar diferentes barramentos
   - Monitorar parâmetros do sistema

## Configuração

O arquivo `config/config.json` contém todas as configurações do sistema:

- Parâmetros dos geradores (4 geradores: A, B, C, D)
- Configurações de carga para cada barramento
- Parâmetros de simulação (duração, passo de tempo)
- Configurações da interface

## Arquitetura

O sistema utiliza uma arquitetura modular com separação clara entre:

- **Modelo físico**: Geradores, barramentos, cargas (rede simplificada)
- **Simulação**: Dinâmica temporal e cálculos simplificados de fluxo de potência
- **Interface**: Visualização e controle em tempo real
- **Configuração**: Parâmetros centralizados

## Características Técnicas

- **Simulação simplificada**: Não requer bibliotecas externas complexas
- **Dinâmica de geradores**: Modelos de turbina, governador e AVR
- **Controle de frequência**: Modo síncrono e droop
- **Controle de tensão**: Sistema AVR com compartilhamento de potência reativa
- **Perturbações**: Simulação de distúrbios aleatórios no sistema
- **Visualização em tempo real**: Gráficos atualizados durante a simulação

## Licença

Este projeto é distribuído sob a licença MIT.
