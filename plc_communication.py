# plc_communication.py
import csv
import time
import threading
import logging
import os  # Usado para verificar se o arquivo de tags existe
from pylogix import PLC

# Função de log local para evitar importação circular
def log_message(message, level="info"):
    """Registra uma mensagem no log."""
    if level == "debug":
        logging.debug(message)
    elif level == "info":
        logging.info(message)
    elif level == "warning":
        logging.warning(message)
    elif level == "error":
        logging.error(message)
    elif level == "critical":
        logging.critical(message)

class PLCCommunication:
    def __init__(self, simulation, plc_ip="*************", tags_file="tags_plc_map.csv", update_interval=0.1):
        """
        Inicializa a comunicação com o PLC.

        Args:
            simulation: Instância da classe Simulation
            plc_ip: Endereço IP do PLC
            tags_file: Caminho para o arquivo CSV com o mapeamento de TAGs
            update_interval: Intervalo de atualização em segundos (padrão: 100ms)
        """
        self.simulation = simulation
        self.plc_ip = plc_ip
        self.tags_file = tags_file
        self.update_interval = update_interval
        self.tags_map = self._load_tags_map()
        self.running = False
        self.comm_thread = None
        self.plc = None  # Initialize as None, will create connection when needed

    def _connect(self):
        """Estabelece conexão com o PLC"""
        if self.plc is None:
            log_message(f"[PLC DEBUG] Conectando ao PLC em {self.plc_ip}...", level="debug")
            self.plc = PLC()
            self.plc.IPAddress = self.plc_ip
            # Added micro800 setting if needed
            # self.plc.Micro800 = True  # Uncomment if using Micro800 series
            log_message(f"[PLC DEBUG] Conexão com o PLC em {self.plc_ip} estabelecida", level="debug")

    def _load_tags_map(self):
        """Carrega o mapeamento de TAGs do arquivo CSV."""
        tags_map = {}
        invalid_tags = []
        try:
            log_message(f"Tentando abrir arquivo de mapeamento: {self.tags_file}", level="debug")
            with open(self.tags_file, 'r', encoding='utf-8') as f:
                log_message(f"Arquivo aberto com sucesso. Lendo cabeçalho...", level="debug")
                reader = csv.DictReader(f)
                log_message(f"Cabeçalho: {reader.fieldnames}", level="debug")

                # Verificar se os nomes das colunas estão corretos
                expected_columns = ['TAG', 'Descrição', 'Tipo', 'Unidade', 'Simulável', 'Direção', 'Mapeamento']
                actual_columns = reader.fieldnames

                # Mapear nomes de colunas com codificação incorreta para nomes corretos
                column_mapping = {}
                for expected in expected_columns:
                    for actual in actual_columns:
                        if expected.lower() in actual.lower() or actual.lower() in expected.lower():
                            column_mapping[actual] = expected
                            break

                log_message(f"Mapeamento de colunas: {column_mapping}", level="debug")

                # Função para obter valor de uma coluna usando o mapeamento
                def get_column_value(row, column_name):
                    # Tenta obter o valor usando o nome original
                    if column_name in row:
                        return row[column_name]

                    # Tenta obter o valor usando o mapeamento de colunas
                    for actual, expected in column_mapping.items():
                        if expected == column_name and actual in row:
                            return row[actual]

                    # Retorna valor padrão se não encontrar
                    return ''

                row_count = 0
                for row in reader:
                    row_count += 1
                    log_message(f"Processando linha {row_count}: {row}", level="debug")

                    tag = get_column_value(row, 'TAG')
                    log_message(f"TAG: {tag}", level="debug")

                    direction = get_column_value(row, 'Direção')
                    if not direction:
                        direction = 'LEITURA'  # Padrão é LEITURA se não especificado
                    log_message(f"Direção: {direction}", level="debug")

                    mapping = get_column_value(row, 'Mapeamento')
                    log_message(f"Mapeamento: {mapping}", level="debug")

                    # Verificação de consistência
                    if direction == 'LEITURA' and (not mapping or mapping.strip() == ''):
                        # TAG de LEITURA sem mapeamento - registrar erro
                        invalid_tags.append(tag)
                        log_message(f"ERRO: TAG {tag} com direção LEITURA não possui mapeamento definido", level="error")
                        continue  # Pula esta TAG

                    # Se for ESCRITA, aceita mapeamento vazio ou "NONE"
                    if direction == 'ESCRITA' and (not mapping or mapping.lower() == 'none'):
                        # Normaliza para "None" para consistência
                        mapping = "None"
                        log_message(f"Mapeamento normalizado para 'None'", level="debug")

                    log_message(f"Adicionando TAG {tag} ao mapeamento", level="debug")
                    tags_map[tag] = {
                        'description': get_column_value(row, 'Descrição'),
                        'type': get_column_value(row, 'Tipo'),
                        'unit': get_column_value(row, 'Unidade'),
                        'direction': direction,
                        'mapping': mapping
                    }

            # Conta TAGs de leitura e escrita
            read_tags = sum(1 for tag in tags_map if tags_map[tag]['direction'] == 'LEITURA')
            write_tags = sum(1 for tag in tags_map if tags_map[tag]['direction'] == 'ESCRITA')

            log_message(f"Carregado mapeamento de {len(tags_map)} TAGs do arquivo {self.tags_file}", level="info")
            log_message(f"  - {read_tags} TAGs de LEITURA (simulação → PLC)", level="info")
            log_message(f"  - {write_tags} TAGs de ESCRITA (PLC → simulação)", level="info")

            # Verificar se há TAGs inválidas
            if invalid_tags:
                log_message(f"ATENÇÃO: {len(invalid_tags)} TAGs de LEITURA sem mapeamento foram ignoradas:", level="warning")
                for tag in invalid_tags:
                    log_message(f"  - {tag}", level="warning")

                # Se não houver TAGs válidas de LEITURA, desabilitar comunicação
                if read_tags == 0:
                    log_message("ERRO CRÍTICO: Nenhuma TAG de LEITURA válida encontrada. Comunicação com o PLC será desabilitada.", level="error")
                    return {}  # Retorna dicionário vazio para desabilitar comunicação

            return tags_map
        except Exception as e:
            log_message(f"Erro ao carregar mapeamento de TAGs: {e}", level="error")
            return {}

    def _get_value_for_tag(self, tag):
        """
        Obtém o valor para uma TAG específica com base no mapeamento.

        Args:
            tag: Nome da TAG

        Returns:
            O valor correspondente à TAG
        """
        if tag not in self.tags_map:
            return None

        mapping = self.tags_map[tag].get('mapping', '')

        # Se não houver mapeamento, retorna None
        if not mapping:
            return None

        # Verifica se o mapeamento é um valor constante
        if mapping.lower() == "true":
            return True
        elif mapping.lower() == "false":
            return False
        elif mapping.isdigit() or (mapping.startswith('-') and mapping[1:].isdigit()):
            # Valor inteiro constante
            return int(mapping)
        elif mapping.replace('.', '', 1).isdigit() or (mapping.startswith('-') and mapping[1:].replace('.', '', 1).isdigit()):
            # Valor float constante
            return float(mapping)

        # Extrai o prefixo da TAG para identificar o gerador
        # Exemplo: TGCPA_GSP.A54GNMWBUD -> A é o gerador
        generator_idx = None
        if tag.startswith("TGCP"):
            try:
                # Extrai a letra do gerador (A, B, C, D)
                generator_letter = tag[4:5]
                # Converte para índice (0, 1, 2, 3)
                generator_idx = ord(generator_letter) - ord('A')
            except:
                pass

        # Mapeamento para power budget dos geradores
        if "A54GNMWBUD" in tag and generator_idx is not None:
            return self._get_power_budget_value(generator_idx)

        # Mapeamento para potência dos geradores
        elif "A54GNMW" in tag and generator_idx is not None and "MWBUD" not in tag:
            return self._get_generator_power(generator_idx)

        # Mapeamento para potência reativa dos geradores
        elif "A54GNMVAR" in tag and generator_idx is not None:
            return self._get_generator_reactive_power(generator_idx)

        # Mapeamento para frequência dos geradores
        elif "A54GNF" in tag and generator_idx is not None:
            return self._get_generator_frequency(generator_idx)

        # Mapeamento para tensão dos geradores
        elif "A54GNKV" in tag and generator_idx is not None:
            return self._get_generator_voltage(generator_idx)

        # Mapeamento para estado dos disjuntores
        elif "I86MCBC" in tag and generator_idx is not None:
            # Disjuntor do gerador (CB5-CB8)
            breaker_idx = generator_idx + 4
            if breaker_idx < len(self.simulation.system.breaker_status):
                return self.simulation.system.breaker_status[breaker_idx]
            return False

        # Mapeamento para estado dos disjuntores via expressão breaker_status[x]
        elif mapping.startswith("breaker_status"):
            try:
                idx = int(mapping.split('[')[1].split(']')[0])
                if idx < len(self.simulation.system.breaker_status):
                    return self.simulation.system.breaker_status[idx]
                return False
            except:
                return False

        # Mapeamento para estado de disponibilidade do gerador para loadshare
        elif "I4GNLSA" in tag and generator_idx is not None:
            if generator_idx < len(self.simulation.system.generators):
                gen = self.simulation.system.generators[generator_idx]
                # Verifica se o gerador está em modo droop (não isocrono)
                return gen.mode != "isochronous"
            return False

        # Mapeamento para estado de excitação do gerador
        elif "I4GNEXC" in tag and generator_idx is not None:
            if generator_idx < len(self.simulation.system.generators):
                # Assume que a excitação está sempre ligada se o gerador existe
                return True
            return False

        # Mapeamento para estado dos disjuntores de interconexão
        elif tag.startswith("I52BB2"):
            try:
                # Extrai a letra do disjuntor (A, B, C, D)
                breaker_letter = tag[6:7]
                # Converte para índice (0, 1, 2, 3)
                breaker_idx = ord(breaker_letter) - ord('A')

                if breaker_idx < len(self.simulation.system.breaker_status):
                    is_closed = self.simulation.system.breaker_status[breaker_idx]

                    # Verifica se é um status de aberto ou fechado
                    if tag.endswith("O"):  # Open status
                        return not is_closed
                    elif tag.endswith("C"):  # Close status
                        return is_closed
            except:
                pass

        # Mapeamento para disponibilidade dos disjuntores
        elif tag.startswith("I04TB2"):
            # Assume que todos os disjuntores estão sempre disponíveis
            return True

        # Valor não mapeado
        return None

    def _get_power_budget_value(self, generator_idx):
        """
        Obtém o valor atual do power budget para um gerador específico.

        Args:
            generator_idx: Índice do gerador (0 para A, 1 para B, etc.)

        Returns:
            float: Valor do power budget em MW
        """
        if not hasattr(self.simulation, 'secondary_bus_power_budgets'):
            return 0.0

        if generator_idx < 0 or generator_idx >= len(self.simulation.secondary_bus_power_budgets):
            return 0.0

        current_idx = self.simulation.current_time_index - 1
        if current_idx < 0 or current_idx >= len(self.simulation.t):
            return 0.0

        return self.simulation.secondary_bus_power_budgets[generator_idx][current_idx]

    def _get_generator_power(self, generator_idx):
        """
        Obtém a potência atual de um gerador específico.

        Args:
            generator_idx: Índice do gerador (0 para A, 1 para B, etc.)

        Returns:
            float: Potência do gerador em MW
        """
        if generator_idx < 0 or generator_idx >= len(self.simulation.system.generators):
            return 0.0

        gen = self.simulation.system.generators[generator_idx]
        current_idx = self.simulation.current_time_index - 1

        if current_idx < 0 or current_idx >= len(gen.P_elec):
            return 0.0

        # Converte de pu para MW
        return gen.P_elec[current_idx] * self.simulation.config.s_base / 1e6

    def _get_generator_frequency(self, generator_idx):
        """
        Obtém a frequência atual de um gerador específico.

        Args:
            generator_idx: Índice do gerador (0 para A, 1 para B, etc.)

        Returns:
            float: Frequência do gerador em Hz
        """
        if generator_idx < 0 or generator_idx >= len(self.simulation.system.generators):
            return 60.0  # Valor padrão

        gen = self.simulation.system.generators[generator_idx]
        current_idx = self.simulation.current_time_index - 1

        if current_idx < 0 or current_idx >= len(gen.omega):
            return 60.0  # Valor padrão

        # Converte de pu para Hz
        return gen.omega[current_idx] * self.simulation.config.f_base

    def _get_generator_voltage(self, generator_idx):
        """
        Obtém a tensão atual de um gerador específico.

        Args:
            generator_idx: Índice do gerador (0 para A, 1 para B, etc.)

        Returns:
            float: Tensão do gerador em kV
        """
        if generator_idx < 0 or generator_idx >= len(self.simulation.system.generators):
            return 13.8  # Valor padrão

        gen = self.simulation.system.generators[generator_idx]
        current_idx = self.simulation.current_time_index - 1

        if current_idx < 0 or current_idx >= len(gen.V_t):
            return 13.8  # Valor padrão

        # Converte de pu para kV
        return gen.V_t[current_idx] * self.simulation.config.v_base / 1000.0

    def _get_generator_reactive_power(self, generator_idx):
        """
        Obtém a potência reativa atual de um gerador específico.

        Args:
            generator_idx: Índice do gerador (0 para A, 1 para B, etc.)

        Returns:
            float: Potência reativa do gerador em MVAr
        """
        if generator_idx < 0 or generator_idx >= len(self.simulation.system.generators):
            return 0.0

        gen = self.simulation.system.generators[generator_idx]
        current_idx = self.simulation.current_time_index - 1

        if current_idx < 0 or current_idx >= len(gen.Q_elec):
            return 0.0

        # Converte de pu para MVAr
        return gen.Q_elec[current_idx] * self.simulation.config.s_base / 1e6

    def update_plc_tags(self):
        """Atualiza as TAGs no PLC com os valores atuais da simulação e lê valores do PLC."""
        try:
            log_message(f"[PLC DEBUG] Iniciando atualização de TAGs do PLC", level="debug")
            self._connect()  # Ensure connection exists
            log_message(f"[PLC DEBUG] Conexão com o PLC estabelecida", level="debug")

            # Contadores para estatísticas
            tags_written = 0
            tags_read = 0

            # Listas para armazenar detalhes das operações
            written_tags_details = []
            read_tags_details = []

            # Verifica se há TAGs mapeadas
            if not self.tags_map:
                log_message(f"[PLC DEBUG] Nenhuma TAG mapeada. Pulando atualização.", level="warning")
                return

            log_message(f"[PLC DEBUG] Processando {len(self.tags_map)} TAGs mapeadas", level="debug")

            # Processa todas as TAGs no mapeamento
            for tag, tag_info in self.tags_map.items():
                log_message(f"[PLC DEBUG] Processando TAG: {tag}", level="debug")

                # Verifica a direção da TAG
                direction = tag_info.get('direction', 'LEITURA')
                tag_description = tag_info.get('description', 'Sem descrição')
                tag_unit = tag_info.get('unit', '-')
                tag_type = tag_info.get('type', 'REAL')

                log_message(f"[PLC DEBUG] Detalhes da TAG: Direção={direction}, Descrição={tag_description}, Tipo={tag_type}, Unidade={tag_unit}", level="debug")

                if direction == 'LEITURA':
                    # TAG de LEITURA: Simulação → PLC (escrevemos no PLC)
                    # Verificar se o mapeamento é válido
                    mapping = tag_info.get('mapping', '')
                    if not mapping or mapping.strip() == '' or mapping.lower() == 'none':
                        log_message(f"[PLC DEBUG] ⚠️ Ignorando TAG {tag} com mapeamento inválido: '{mapping}'", level="warning")
                        continue

                    value = self._get_value_for_tag(tag)

                    # Se o valor for None, pula esta TAG
                    if value is None:
                        continue

                    # Escreve o valor no PLC
                    log_message(f"[PLC DEBUG] Escrevendo no PLC: TAG={tag}, DESCRIÇÃO={tag_description}, TIPO={tag_type}, VALOR={value}, UNIDADE={tag_unit}", level="debug")
                    ret = self.plc.Write(tag, value)

                    if ret.Status == "Success":
                        tags_written += 1
                        written_tags_details.append(f"{tag} ({tag_description}) = {value} {tag_unit}")
                        log_message(f"[PLC DEBUG] ✅ Escrita bem-sucedida: TAG={tag}, VALOR={value}, UNIDADE={tag_unit}", level="debug")

                        # Log detalhado apenas para power budget (para depuração)
                        if "MWBUD" in tag:
                            log_message(f"Escrito {tag} = {value} {tag_unit}", level="info")
                    else:
                        log_message(f"[PLC DEBUG] ❌ Erro ao escrever {tag}: {ret.Status}", level="error")

                elif direction == 'ESCRITA':
                    # TAG de ESCRITA: PLC → Simulação (lemos do PLC)
                    log_message(f"[PLC DEBUG] Lendo do PLC: TAG={tag}, DESCRIÇÃO={tag_description}, TIPO={tag_type}", level="debug")
                    ret = self.plc.Read(tag)

                    if ret.Status == "Success":
                        tags_read += 1
                        read_tags_details.append(f"{tag} ({tag_description}) = {ret.Value} {tag_unit}")
                        log_message(f"[PLC DEBUG] ✅ Leitura bem-sucedida: TAG={tag}, VALOR={ret.Value}, UNIDADE={tag_unit}", level="debug")

                        # Atualiza a simulação com o valor lido do PLC
                        self._update_simulation_with_plc_value(tag, ret.Value)

                        # Log detalhado para valores lidos do PLC
                        log_message(f"Lido {tag} = {ret.Value} {tag_unit}", level="info")
                    else:
                        log_message(f"[PLC DEBUG] ❌ Erro ao ler {tag}: {ret.Status}", level="error")

            # Log resumido
            if tags_written > 0 or tags_read > 0:
                log_message(f"\n[PLC DEBUG] === RESUMO DA COMUNICAÇÃO PLC ===", level="debug")
                log_message(f"[PLC DEBUG] {tags_written} TAGs escritas, {tags_read} TAGs lidas", level="debug")

                if written_tags_details:
                    log_message(f"\n[PLC DEBUG] === DETALHES DAS TAGS ESCRITAS ===", level="debug")
                    for detail in written_tags_details:
                        log_message(f"[PLC DEBUG] ESCRITA: {detail}", level="debug")

                if read_tags_details:
                    log_message(f"\n[PLC DEBUG] === DETALHES DAS TAGS LIDAS ===", level="debug")
                    for detail in read_tags_details:
                        log_message(f"[PLC DEBUG] LEITURA: {detail}", level="debug")

                log_message(f"[PLC DEBUG] === FIM DO RESUMO ===\n", level="debug")

        except Exception as e:
            log_message(f"Erro ao atualizar TAGs no PLC: {e}", level="error")
            if self.plc:
                self.plc.Close()
                self.plc = None  # Reset connection on error

    def start(self):
        """Inicia a comunicação com o PLC em um thread separado."""
        log_message(f"[PLC DEBUG] Solicitação para iniciar comunicação com o PLC em {self.plc_ip}", level="debug")
        if self.running:
            log_message("[PLC DEBUG] Comunicação com o PLC já está em execução", level="debug")
            return

        # Verificar se o arquivo de mapeamento de TAGs existe
        log_message(f"[PLC DEBUG] Verificando arquivo de mapeamento de TAGs: {self.tags_file}", level="debug")
        import os
        if not os.path.exists(self.tags_file):
            log_message(f"[PLC DEBUG] AVISO: Arquivo de mapeamento de TAGs não encontrado: {self.tags_file}", level="warning")
        else:
            log_message(f"[PLC DEBUG] Arquivo de mapeamento de TAGs encontrado: {self.tags_file}", level="debug")

        # Verificar se há TAGs mapeadas
        if not self.tags_map:
            log_message("[PLC DEBUG] AVISO: Nenhuma TAG mapeada. Verifique o arquivo de mapeamento.", level="warning")
        else:
            log_message(f"[PLC DEBUG] {len(self.tags_map)} TAGs mapeadas", level="debug")

        self.running = True
        log_message(f"[PLC DEBUG] Criando thread de comunicação com o PLC", level="debug")
        self.comm_thread = threading.Thread(target=self._communication_loop)
        self.comm_thread.daemon = True  # Permite que o thread seja encerrado quando o programa principal terminar
        self.comm_thread.start()
        log_message(f"[PLC DEBUG] Thread de comunicação iniciado", level="debug")
        log_message(f"Iniciada comunicação com o PLC em {self.plc_ip} (intervalo: {self.update_interval*1000:.0f}ms)", level="info")

    def stop(self):
        """Para a comunicação com o PLC."""
        log_message(f"[PLC DEBUG] Solicitação para interromper comunicação com o PLC em {self.plc_ip}", level="debug")
        self.running = False
        if self.comm_thread:
            log_message(f"[PLC DEBUG] Aguardando thread de comunicação finalizar (timeout: 2.0s)", level="debug")
            self.comm_thread.join(timeout=2.0)
            log_message(f"[PLC DEBUG] Thread de comunicação finalizado", level="debug")
        if self.plc:
            log_message(f"[PLC DEBUG] Fechando conexão com o PLC", level="debug")
            self.plc.Close()
            self.plc = None
            log_message(f"[PLC DEBUG] Conexão com o PLC fechada", level="debug")
        log_message("Comunicação com o PLC interrompida", level="info")

    def _communication_loop(self):
        """Loop de comunicação com o PLC."""
        log_message(f"[PLC DEBUG] Iniciando loop de comunicação com o PLC em {self.plc_ip}", level="debug")
        try:
            loop_count = 0
            while self.running:
                loop_count += 1
                # Verifica se a simulação está em execução
                if not self.simulation.is_finished():
                    log_message(f"[PLC DEBUG] Ciclo {loop_count}: Atualizando TAGs do PLC...", level="debug")
                    self.update_plc_tags()
                    log_message(f"[PLC DEBUG] Ciclo {loop_count}: Atualização de TAGs concluída", level="debug")
                else:
                    log_message(f"[PLC DEBUG] Ciclo {loop_count}: Simulação finalizada, pulando atualização de TAGs", level="debug")

                # Aguarda o intervalo de atualização
                log_message(f"[PLC DEBUG] Ciclo {loop_count}: Aguardando {self.update_interval*1000:.0f}ms para próxima atualização", level="debug")
                time.sleep(self.update_interval)

        except Exception as e:
            log_message(f"Erro no loop de comunicação com o PLC: {e}", level="error")
        finally:
            if self.plc:
                self.plc.Close()
                self.plc = None
            log_message("Conexão com o PLC fechada", level="info")

    def set_update_interval(self, interval):
        """
        Define o intervalo de atualização da comunicação.

        Args:
            interval: Novo intervalo em segundos
        """
        self.update_interval = interval
        log_message(f"Intervalo de atualização da comunicação com o PLC alterado para {interval*1000:.0f}ms", level="info")

    def _update_simulation_with_plc_value(self, tag, value):
        """
        Atualiza a simulação com um valor lido do PLC.

        Args:
            tag: Nome da TAG
            value: Valor lido do PLC
        """
        log_message(f"[PLC DEBUG] Atualizando simulação com valor do PLC: TAG={tag}, VALOR={value}", level="debug")
        try:
            # Implementar a lógica para atualizar a simulação com base na TAG
            # Exemplo: comandos de disjuntores, setpoints de geradores, etc.

            # Comandos de disjuntores
            if tag.startswith("I52CS"):
                # Exemplo: I52CS1 = comando para fechar disjuntor 1
                breaker_num = int(tag[-1]) - 1  # Converte I52CS1 para índice 0
                log_message(f"[PLC DEBUG] Processando comando de disjuntor: TAG={tag}, DISJUNTOR={breaker_num+1}, VALOR={value}", level="debug")
                if breaker_num >= 0 and breaker_num < len(self.simulation.system.breaker_status):
                    # Atualiza o estado do disjuntor na simulação
                    if value:  # Se o comando for True (fechar)
                        self.simulation.system.breaker_status[breaker_num] = True
                        log_message(f"[PLC DEBUG] Disjuntor {breaker_num+1} fechado por comando do PLC", level="info")

            # Comandos de velocidade do gerador
            elif tag.startswith("I18ASR"):  # Aumentar velocidade
                gen_num = int(tag[-1]) - 1  # Converte I18ASR1 para índice 0
                log_message(f"[PLC DEBUG] Processando comando de aumento de velocidade: TAG={tag}, GERADOR={gen_num+1}, VALOR={value}", level="debug")
                if gen_num >= 0 and gen_num < len(self.simulation.system.generators):
                    gen = self.simulation.system.generators[gen_num]
                    if value:  # Se o comando for True
                        # Aumenta o setpoint de potência do gerador
                        old_setpoint = gen.P_setpoint
                        gen.P_setpoint += 0.01  # Aumenta 1% da potência nominal
                        log_message(f"[PLC DEBUG] Aumentando setpoint do gerador {gen.name} de {old_setpoint:.2f} para {gen.P_setpoint:.2f} pu", level="info")

            elif tag.startswith("I18ASL"):  # Diminuir velocidade
                gen_num = int(tag[-1]) - 1  # Converte I18ASL1 para índice 0
                log_message(f"[PLC DEBUG] Processando comando de diminuição de velocidade: TAG={tag}, GERADOR={gen_num+1}, VALOR={value}", level="debug")
                if gen_num >= 0 and gen_num < len(self.simulation.system.generators):
                    gen = self.simulation.system.generators[gen_num]
                    if value:  # Se o comando for True
                        # Diminui o setpoint de potência do gerador
                        old_setpoint = gen.P_setpoint
                        gen.P_setpoint -= 0.01  # Diminui 1% da potência nominal
                        log_message(f"[PLC DEBUG] Diminuindo setpoint do gerador {gen.name} de {old_setpoint:.2f} para {gen.P_setpoint:.2f} pu", level="info")

            # Comandos de tensão do gerador
            elif tag.startswith("I90VRR"):  # Aumentar tensão
                gen_num = int(tag[-1]) - 1  # Converte I90VRR1 para índice 0
                log_message(f"[PLC DEBUG] Processando comando de aumento de tensão: TAG={tag}, GERADOR={gen_num+1}, VALOR={value}", level="debug")
                if gen_num >= 0 and gen_num < len(self.simulation.system.generators):
                    gen = self.simulation.system.generators[gen_num]
                    if value:  # Se o comando for True
                        # Aumenta o setpoint de tensão do gerador
                        old_setpoint = gen.V_setpoint
                        gen.V_setpoint += 0.01  # Aumenta 1% da tensão nominal
                        log_message(f"[PLC DEBUG] Aumentando setpoint de tensão do gerador {gen.name} de {old_setpoint:.2f} para {gen.V_setpoint:.2f} pu", level="info")

            elif tag.startswith("I90VRL"):  # Diminuir tensão
                gen_num = int(tag[-1]) - 1  # Converte I90VRL1 para índice 0
                log_message(f"[PLC DEBUG] Processando comando de diminuição de tensão: TAG={tag}, GERADOR={gen_num+1}, VALOR={value}", level="debug")
                if gen_num >= 0 and gen_num < len(self.simulation.system.generators):
                    gen = self.simulation.system.generators[gen_num]
                    if value:  # Se o comando for True
                        # Diminui o setpoint de tensão do gerador
                        old_setpoint = gen.V_setpoint
                        gen.V_setpoint -= 0.01  # Diminui 1% da tensão nominal
                        log_message(f"[PLC DEBUG] Diminuindo setpoint de tensão do gerador {gen.name} de {old_setpoint:.2f} para {gen.V_setpoint:.2f} pu", level="info")

            # Adicione mais mapeamentos conforme necessário

        except Exception as e:
            log_message(f"Erro ao atualizar simulação com valor do PLC para {tag}: {e}", level="error")
