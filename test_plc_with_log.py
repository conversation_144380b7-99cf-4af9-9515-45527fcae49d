# test_plc_with_log.py
# Script para testar a comunicação com o PLC e salvar a saída em um arquivo

from pylogix import PLC
import time
import sys

# Redirecionar a saída para um arquivo
log_file = open("plc_test_log.txt", "w")
sys.stdout = log_file

def test_plc_connection(plc_ip="*************"):
    """Testa a conexão com o PLC."""
    print(f"Testando conexão com o PLC em {plc_ip}...")
    plc = None
    
    try:
        plc = PLC()
        plc.IPAddress = plc_ip
        
        # Test connection with GetPLCTime
        ret = plc.GetPLCTime()
        
        if ret.Status == "Success":
            print(f"Conexão bem-sucedida! Hora do PLC: {ret.Value}")
            return True
        else:
            print(f"Falha na conexão: {ret.Status}")
            return False
            
    except Exception as e:
        print(f"Erro ao conectar ao PLC: {e}")
        return False
        
    finally:
        if plc:
            plc.Close()

if __name__ == "__main__":
    print("Iniciando teste de comunicação com o PLC...")
    
    # Teste com o IP padrão
    result1 = test_plc_connection()
    print(f"Resultado do teste com IP padrão: {'Sucesso' if result1 else 'Falha'}")
    
    # Aguarde um momento
    time.sleep(1)
    
    # Teste com um IP alternativo
    result2 = test_plc_connection("************")
    print(f"Resultado do teste com IP alternativo: {'Sucesso' if result2 else 'Falha'}")
    
    # Fechar o arquivo de log
    log_file.close()
    
    # Restaurar a saída padrão
    sys.stdout = sys.__stdout__
    
    print(f"Teste concluído. Resultados salvos em plc_test_log.txt")
