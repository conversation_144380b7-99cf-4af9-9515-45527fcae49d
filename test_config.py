#!/usr/bin/env python3
"""
Test script for the new configuration system.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_configuration_system():
    """Test the new configuration system."""
    print("🧪 Testing new configuration system...")
    
    try:
        # Test imports
        print("📦 Testing imports...")
        from core.configuration import ConfigurationManager
        print("✅ All imports successful")
        
        # Test configuration manager creation
        print("\n🔧 Testing configuration manager...")
        config_manager = ConfigurationManager()
        print("✅ Configuration manager created successfully")
        
        # Test validation
        print("\n✅ Testing validation...")
        errors = config_manager.validate_configuration()
        if errors:
            print("❌ Configuration errors found:")
            for error in errors:
                print(f"  - {error}")
        else:
            print("✅ Configuration validation passed")
        
        # Test configuration summary
        print("\n📋 Configuration Summary:")
        print(config_manager.get_configuration_summary())
        
        # Test data manager
        print("\n💾 Testing data manager...")
        from core.data_manager import DateBasedSimulationManager
        data_manager = DateBasedSimulationManager(config_manager)
        print("✅ Data manager created successfully")
        
        storage_info = data_manager.get_storage_info()
        print(f"\n📊 Storage Info:")
        print(f"  Directory: {storage_info['data_dir']}")
        print(f"  Total size: {storage_info['total_mb']:.2f} MB")
        print(f"  Folders: {storage_info['folder_count']}")
        print(f"  Files: {storage_info['file_count']}")
        
        # Test user preferences
        print("\n👤 Testing user preferences...")
        prefs = config_manager.user_preferences
        if prefs:
            print(f"  Default graph type: {prefs.default_graph_type}")
            print(f"  Auto-cleanup enabled: {prefs.auto_cleanup_enabled}")
            print(f"  Use date folders: {prefs.use_date_folders}")
            print(f"  Window size: {prefs.window_width}x{prefs.window_height}")
        else:
            print("  No user preferences loaded")
        
        print("\n🎉 All tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_configuration_system()
    sys.exit(0 if success else 1)
