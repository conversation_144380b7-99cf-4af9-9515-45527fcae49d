# Instruções para Configuração do Sistema Elétrico de Potência

Este documento fornece instruções detalhadas sobre como configurar o sistema elétrico de potência através do arquivo `config.json`.

## Estrutura do Arquivo `config.json`

O arquivo `config.json` contém quatro seções principais:

1. **bus**: Configuração do barramento principal e cargas dos barramentos secundários
2. **generators**: Configuração dos geradores
3. **breaker_status**: Estado dos disjuntores
4. **plc**: Configuração da comunicação com o PLC

## Sistema Por Unidade (pu)

O sistema por unidade (pu) é um método de normalização usado em sistemas elétricos de potência para expressar valores como frações de valores base predefinidos. Isso simplifica cálculos e permite comparar facilmente valores entre diferentes partes do sistema.

### Valores Base Fundamentais

No sistema elétrico de potência, três valores base fundamentais são definidos:

- **s_base**: Potência aparente base em VA (Volt-Ampere)
- **v_base**: Tensão base em V (Volt)
- **f_base**: Frequência base em Hz (Hertz)

### Valores Base Derivados

A partir dos valores base fundamentais, outros valores base são derivados:

- **z_base**: Impedância base em Ω (Ohm)
  - z_base = (v_base)² / s_base
- **i_base**: Corrente base em A (Ampere)
  - i_base = s_base / v_base

### Conversão entre Valores Absolutos e pu

Para converter um valor absoluto para pu:
```
Valor em pu = Valor absoluto / Valor base
```

Para converter um valor em pu para valor absoluto:
```
Valor absoluto = Valor em pu × Valor base
```

Exemplos:
- Potência ativa de 2.25 MW com s_base = 30 MVA:
  - P em pu = 2.25 MW / 30 MVA = 0.075 pu
- Potência reativa de 1.089 MVAR com s_base = 30 MVA:
  - Q em pu = 1.089 MVAR / 30 MVA = 0.0363 pu
- Resistência de 0.01 pu com z_base = 6.348 Ω:
  - R absoluto = 0.01 pu × 6.348 Ω = 0.06348 Ω

## Configuração do Barramento (`bus`)

A seção `bus` contém os parâmetros do barramento principal e as cargas dos barramentos secundários:

```json
"bus": {
    "s_base": 30000000.0,     // Potência aparente base em VA (30 MVA)
    "v_base": 13800.0,        // Tensão base em V (13.8 kV)
    "f_base": 60.0,           // Frequência base em Hz
    "r_eq": 0.01,             // Resistência equivalente em pu
    "x_eq": 0.1,              // Reatância equivalente em pu
    "loads": {                // Cargas individualizadas para cada barramento secundário
        "A": {
            "load_p": 0.075,  // Potência ativa em pu (0.075 pu = 2.25MW/30MVA)
            "load_q": 0.0363  // Potência reativa em pu (0.0363 pu = 1.089MVAR/30MVA)
        },
        "B": {
            "load_p": 0.075,  // Potência ativa em pu
            "load_q": 0.0363  // Potência reativa em pu
        },
        "C": {
            "load_p": 0.075,  // Potência ativa em pu
            "load_q": 0.0363  // Potência reativa em pu
        },
        "D": {
            "load_p": 0.075,  // Potência ativa em pu
            "load_q": 0.0363  // Potência reativa em pu
        }
    }
}
```

### Parâmetros do Barramento

- **s_base**: Potência aparente base em VA (Volt-Ampere). Este é um valor absoluto, não em pu.
- **v_base**: Tensão base em Volts (V). Este é um valor absoluto, não em pu.
- **f_base**: Frequência base em Hertz (Hz). Este é um valor absoluto, não em pu.
- **r_eq**: Resistência equivalente do barramento em pu (por unidade).
- **x_eq**: Reatância equivalente do barramento em pu (por unidade).

### Cargas dos Barramentos Secundários

Cada barramento secundário (A, B, C e D) pode ter sua própria carga configurada individualmente:

- **load_p**: Potência ativa em pu (por unidade).
  - Exemplo: 0.075 pu = 2.25 MW / 30 MVA (potência base)
- **load_q**: Potência reativa em pu (por unidade).
  - Exemplo: 0.0363 pu = 1.089 MVAR / 30 MVA (potência base)

## Configuração dos Geradores (`generators`)

A seção `generators` contém a configuração de cada gerador no sistema:

```json
"generators": [
    {
        "name": "A",
        "mode": "synchronous",
        "P_setpoint": 0.6,
        "H": 20.0,
        "damping_factor": 16.0,
        "K_pgov": 12.0,
        "K_igov": 2.0,
        "K_dgov": 0.8,
        "K_turb": 1.2,
        "T_turb": 0.15,
        "W_fnl": 0.15,
        "disturb_start": 0.0,
        "disturb_end": 0.0,
        "disturb_value": 0.0
    },
    // Configurações para os geradores B, C e D seguem o mesmo formato
]
```

### Parâmetros dos Geradores

#### Parâmetros Básicos
- **name**: Nome do gerador (A, B, C ou D).
- **mode**: Modo de operação do gerador:
  - `synchronous`: Modo síncrono (isocrono) - mantém a frequência constante
  - `droop`: Modo droop - permite variação de frequência proporcional à carga
- **P_setpoint**: Setpoint de potência ativa em pu (0.0 a 1.0).

#### Parâmetros de Inércia e Amortecimento
- **H**: Constante de inércia em segundos (5.0 a 30.0).
  - Valores típicos:
    - Geradores grandes (>100 MVA): 15.0 a 30.0 segundos
    - Geradores médios (10-100 MVA): 8.0 a 15.0 segundos
    - Geradores pequenos (<10 MVA): 5.0 a 8.0 segundos
  - Valor padrão: 20.0 segundos
  - Efeito: Valores maiores aumentam a estabilidade do sistema, mas reduzem a resposta a mudanças de carga

- **damping_factor**: Fator de amortecimento (0.0 a 30.0).
  - Valores recomendados:
    - Geradores síncronos (modo `synchronous`): 12.0 a 20.0 (padrão: 16.0)
    - Geradores em modo droop: 4.0 a 10.0 (padrão: 6.0)
  - Efeito: Valores maiores reduzem oscilações de frequência, mas podem tornar o sistema mais lento

#### Parâmetros do Governador (Controlador PID)
- **K_pgov**: Ganho proporcional do governador (5.0 a 20.0, padrão: 12.0).
  - Efeito: Controla a resposta imediata a desvios de frequência

- **K_igov**: Ganho integral do governador (0.5 a 5.0, padrão: 2.0).
  - Efeito: Elimina o erro de estado estacionário na frequência

- **K_dgov**: Ganho derivativo do governador (0.0 a 2.0, padrão: 0.8).
  - Efeito: Melhora a resposta transitória e reduz oscilações

#### Parâmetros da Turbina
- **K_turb**: Ganho da turbina (0.8 a 1.5, padrão: 1.2).
  - Efeito: Determina a amplificação do sinal de controle do governador

- **T_turb**: Constante de tempo da turbina em segundos (0.1 a 0.5, padrão: 0.15).
  - Efeito: Representa o atraso na resposta da turbina

- **W_fnl**: Fluxo de combustível sem carga (0.05 a 0.2, padrão: 0.15).
  - Efeito: Representa o consumo mínimo de combustível necessário para manter a turbina em operação

#### Parâmetros de Perturbação (para Testes)
- **disturb_start**: Tempo de início da perturbação em segundos (padrão: 0.0).
- **disturb_end**: Tempo de fim da perturbação em segundos (padrão: 0.0).
- **disturb_value**: Valor da perturbação em pu (padrão: 0.0).

## Estado dos Disjuntores (`breaker_status`)

A seção `breaker_status` contém o estado de cada disjuntor no sistema:

```json
"breaker_status": [true, true, true, true, true, true, true, true]
```

Os disjuntores são:
1. CB1: Barramento Principal → Sec A
2. CB2: Barramento Principal → Sec B
3. CB3: Barramento Principal → Sec C
4. CB4: Barramento Principal → Sec D
5. CB5: Conexão Gerador A
6. CB6: Conexão Gerador B
7. CB7: Conexão Gerador C
8. CB8: Conexão Gerador D

- **true**: Disjuntor fechado (conectado)
- **false**: Disjuntor aberto (desconectado)

## Configuração da Comunicação com o PLC (`plc`)

A seção `plc` contém os parâmetros para a comunicação com o PLC:

```json
"plc": {
    "enabled": true,           // Habilita ou desabilita a comunicação com o PLC
    "ip": "*************",      // Endereço IP do PLC
    "update_interval": 0.1     // Intervalo de atualização em segundos (100ms)
}
```

### Parâmetros da Comunicação com o PLC

- **enabled**: Habilita (true) ou desabilita (false) a comunicação com o PLC.
- **ip**: Endereço IP do PLC na rede.
- **update_interval**: Intervalo de atualização da comunicação em segundos.
  - Exemplo: 0.1 = 100 milissegundos

### Verificação de Conectividade

O sistema verifica automaticamente a conectividade com o PLC no início do programa usando o comando `ping`. Se o PLC não responder ao ping, a comunicação será desativada automaticamente, mesmo que esteja habilitada na configuração.

Esta verificação garante que o programa não tentará se comunicar com um PLC inacessível, evitando erros e atrasos desnecessários durante a execução.

### Teste de Comunicação com o PLC

O sistema oferece duas formas de testar a comunicação com o PLC:

1. **Através da Interface Gráfica**:
   - Clique no botão "Testar PLC" na parte inferior da interface
   - Digite o endereço IP do PLC no diálogo que aparece
   - O sistema realizará um teste completo e exibirá os resultados

2. **Através da Linha de Comando**:
   - Execute o programa com o argumento `--test-plc`
   - Exemplo: `python main.py --test-plc --plc-ip=*************`
   - Opções adicionais:
     - `--plc-ip`: Endereço IP do PLC (padrão: *************)
     - `--timeout`: Tempo limite para o teste em segundos (padrão: 1.0)

O teste de comunicação verifica:
- Conectividade básica (ping)
- Capacidade de estabelecer conexão com o PLC
- Capacidade de ler valores do PLC

Os resultados do teste são exibidos de forma detalhada, incluindo tempos de resposta e quaisquer erros encontrados.

## Exemplo Completo

Aqui está um exemplo completo de um arquivo `config.json`:

```json
{
    "bus": {
        "s_base": 30000000.0,
        "v_base": 13800.0,
        "f_base": 60.0,
        "r_eq": 0.01,
        "x_eq": 0.1,
        "loads": {
            "A": {"load_p": 0.075, "load_q": 0.0363},
            "B": {"load_p": 0.3, "load_q": 0.03},
            "C": {"load_p": 0.3, "load_q": 0.03},
            "D": {"load_p": 0.3, "load_q": 0.03}
        }
    },
    "generators": [
        {
            "name": "A",
            "mode": "synchronous",
            "P_setpoint": 0.6,
            "H": 20.0,
            "damping_factor": 16.0,
            "K_pgov": 12.0,
            "K_igov": 2.0,
            "K_dgov": 0.8,
            "K_turb": 1.2,
            "T_turb": 0.15,
            "W_fnl": 0.15,
            "disturb_start": 0.0,
            "disturb_end": 0.0,
            "disturb_value": 0.0
        },
        {
            "name": "B",
            "mode": "droop",
            "P_setpoint": 0.3,
            "H": 20.0,
            "damping_factor": 6.0,
            "K_pgov": 12.0,
            "K_igov": 2.0,
            "K_dgov": 0.8,
            "K_turb": 1.2,
            "T_turb": 0.15,
            "W_fnl": 0.15,
            "disturb_start": 0.0,
            "disturb_end": 0.0,
            "disturb_value": 0.0
        },
        {
            "name": "C",
            "mode": "droop",
            "P_setpoint": 0.3,
            "H": 20.0,
            "damping_factor": 6.0,
            "K_pgov": 12.0,
            "K_igov": 2.0,
            "K_dgov": 0.8,
            "K_turb": 1.2,
            "T_turb": 0.15,
            "W_fnl": 0.15,
            "disturb_start": 0.0,
            "disturb_end": 0.0,
            "disturb_value": 0.0
        },
        {
            "name": "D",
            "mode": "droop",
            "P_setpoint": 0.3,
            "H": 20.0,
            "damping_factor": 6.0,
            "K_pgov": 12.0,
            "K_igov": 2.0,
            "K_dgov": 0.8,
            "K_turb": 1.2,
            "T_turb": 0.15,
            "W_fnl": 0.15,
            "disturb_start": 0.0,
            "disturb_end": 0.0,
            "disturb_value": 0.0
        }
    ],
    "breaker_status": [true, true, true, true, true, true, true, true],
    "plc": {
        "enabled": false,
        "ip": "*************",
        "update_interval": 0.1
    }
}
```

## Observações Importantes

1. **Valores em pu**: Os valores de carga (`load_p` e `load_q`), resistência (`r_eq`), reatância (`x_eq`) e setpoints de potência dos geradores (`P_setpoint`) devem ser especificados em pu (por unidade), considerando a potência base do sistema (30 MVA por padrão).

2. **Valores base**: Os valores base (`s_base`, `v_base` e `f_base`) devem ser especificados em suas unidades absolutas (VA, V e Hz), não em pu. Estes valores definem a base para todos os cálculos em pu no sistema.

3. **Cargas individualizadas**: Cada barramento secundário (A, B, C e D) pode ter sua própria carga configurada individualmente. Isso permite simular diferentes condições de carga em cada parte do sistema.

4. **Modo síncrono**: Para estabilidade ótima do sistema, o gerador síncrono (modo `synchronous`) deve ter o maior setpoint de potência no sistema.

5. **Disjuntores**: O estado dos disjuntores determina a topologia do sistema. Alterar o estado dos disjuntores pode afetar significativamente o comportamento do sistema.

6. **Constante de inércia (H)**: A constante de inércia é um parâmetro crítico que afeta a estabilidade do sistema:
   - Valores maiores (15.0-30.0) proporcionam maior estabilidade, mas resposta mais lenta a mudanças de carga
   - Valores menores (5.0-15.0) proporcionam resposta mais rápida, mas menor estabilidade
   - O valor padrão de 20.0 é adequado para a maioria das simulações
   - Ajuste este valor com cuidado, pois afeta diretamente a dinâmica do sistema

7. **Fator de amortecimento (damping_factor)**: O fator de amortecimento controla as oscilações de frequência:
   - Para geradores síncronos, use valores maiores (12.0-20.0) para reduzir oscilações
   - Para geradores em modo droop, use valores menores (4.0-10.0) para permitir resposta mais rápida
   - Valores muito altos podem tornar o sistema lento e não responsivo
   - Valores muito baixos podem causar oscilações excessivas e instabilidade

8. **Comunicação com o PLC**: A comunicação com o PLC é desabilitada por padrão (`enabled: false`):
   - Habilite apenas quando houver um PLC real disponível na rede
   - O sistema verifica automaticamente a conectividade com o PLC usando ping
   - Se o PLC não responder ao ping, a comunicação será desativada automaticamente

9. **Vantagens do sistema pu**:
   - **Simplificação de cálculos**: Facilita cálculos em sistemas com múltiplos níveis de tensão.
   - **Padronização**: Permite comparar facilmente valores entre diferentes partes do sistema.
   - **Identificação de anormalidades**: Valores muito diferentes de 1.0 pu geralmente indicam condições anormais.
   - **Independência de unidades**: Os valores em pu são adimensionais, independentes das unidades físicas.

10. **Cálculo de valores base derivados**:
    - Impedância base (z_base) = (v_base)² / s_base = (13800)² / 30000000 = 6.348 Ω
    - Corrente base (i_base) = s_base / v_base = 30000000 / 13800 = 2173.9 A
