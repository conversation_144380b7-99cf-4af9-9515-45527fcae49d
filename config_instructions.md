# Instruções para Configuração do Sistema Elétrico de Potência - Versão 2.0

Este documento fornece instruções detalhadas sobre como configurar o sistema elétrico de potência através do **novo sistema de configuração unificado**.

## 🆕 Novo Sistema de Configuração

A partir da versão 2.0, o sistema utiliza **múltiplos arquivos de configuração especializados** em vez de um único `config.json`. Isso oferece:

- ✅ **Organização melhorada** com separação clara de responsabilidades
- ✅ **Migração automática** de configurações antigas
- ✅ **Validação robusta** com relatórios de erro detalhados
- ✅ **Preferências personalizáveis** por usuário
- ✅ **Manutenção simplificada** de parâmetros específicos

## 📁 Estrutura dos Arquivos de Configuração

```
config/
├── system_config.json          # Parâmetros do sistema elétrico
├── generators_config.json      # Configurações dos geradores
├── user_preferences.json       # Preferências do usuário
├── default_scenarios.json      # Cenários pré-definidos
└── config.json.backup          # Backup da configuração antiga (se existir)
```

## 🔄 Migração Automática

Se você possui um arquivo `config.json` antigo, o sistema irá:

1. **Detectar automaticamente** a configuração antiga
2. **Migrar os dados** para o novo formato
3. **Criar backup** do arquivo original como `config.json.backup`
4. **Validar** a migração e reportar eventuais problemas

**Não é necessária ação manual** - a migração é totalmente automática!

## ⚡ Sistema Por Unidade (pu)

O sistema por unidade (pu) é um método de normalização usado em sistemas elétricos de potência para expressar valores como frações de valores base predefinidos. Isso simplifica cálculos e permite comparar facilmente valores entre diferentes partes do sistema.

### 📊 Valores Base Fundamentais

No sistema elétrico de potência, três valores base fundamentais são definidos:

- **s_base**: Potência aparente base em VA (Volt-Ampere) - Padrão: 30 MVA
- **v_base**: Tensão base em V (Volt) - Padrão: 13.8 kV
- **f_base**: Frequência base em Hz (Hertz) - Padrão: 60 Hz

### 🔢 Valores Base Derivados

A partir dos valores base fundamentais, outros valores base são derivados automaticamente:

- **z_base**: Impedância base em Ω (Ohm)
  - z_base = (v_base)² / s_base = (13800)² / 30000000 = 6.348 Ω
- **i_base**: Corrente base em A (Ampere)
  - i_base = s_base / v_base = 30000000 / 13800 = 2173.9 A

### 🔄 Conversão entre Valores Absolutos e pu

**Para converter um valor absoluto para pu:**
```
Valor em pu = Valor absoluto / Valor base
```

**Para converter um valor em pu para valor absoluto:**
```
Valor absoluto = Valor em pu × Valor base
```

### 📝 Exemplos Práticos

| Grandeza | Valor Absoluto | Valor Base | Valor em pu | Cálculo |
|----------|----------------|------------|-------------|---------|
| Potência Ativa | 2.25 MW | 30 MVA | 0.075 pu | 2.25/30 |
| Potência Reativa | 1.089 MVAR | 30 MVA | 0.0363 pu | 1.089/30 |
| Resistência | 0.06348 Ω | 6.348 Ω | 0.01 pu | 0.06348/6.348 |
| Tensão | 14.49 kV | 13.8 kV | 1.05 pu | 14.49/13.8 |

### ✅ Vantagens do Sistema pu

- **Simplificação**: Facilita cálculos em sistemas com múltiplos níveis de tensão
- **Padronização**: Permite comparar facilmente valores entre diferentes partes do sistema
- **Detecção de problemas**: Valores muito diferentes de 1.0 pu indicam condições anormais
- **Independência**: Os valores em pu são adimensionais, independentes das unidades físicas

## ⚙️ Configuração do Sistema (`system_config.json`)

O arquivo `system_config.json` contém os parâmetros fundamentais do sistema elétrico:

```json
{
  "metadata": {
    "version": "2.0",
    "last_updated": "2024-01-15T10:30:00Z",
    "description": "System configuration for power system simulation"
  },
  "system": {
    "s_base": 30000000.0,     // Potência aparente base em VA (30 MVA)
    "v_base": 13800.0,        // Tensão base em V (13.8 kV)
    "f_base": 60.0,           // Frequência base em Hz
    "dt": 0.01,               // Passo de tempo da simulação (s)
    "duration": 15.0,         // Duração da simulação (s)
    "r_eq": 0.01,             // Resistência equivalente em pu
    "x_eq": 0.1               // Reatância equivalente em pu
  },
  "loads": {
    "A": {
      "load_p": 0.075,        // Potência ativa em pu (2.25 MW)
      "load_q": 0.0363        // Potência reativa em pu (1.089 MVAR)
    },
    "B": {
      "load_p": 0.3,          // Potência ativa em pu (9.0 MW)
      "load_q": 0.03          // Potência reativa em pu (0.9 MVAR)
    },
    "C": {
      "load_p": 0.3,          // Potência ativa em pu (9.0 MW)
      "load_q": 0.03          // Potência reativa em pu (0.9 MVAR)
    },
    "D": {
      "load_p": 0.3,          // Potência ativa em pu (9.0 MW)
      "load_q": 0.03          // Potência reativa em pu (0.9 MVAR)
    }
  },
  "breaker_status": [true, true, true, true, true, true, true, true]
}
```

### 🔧 Parâmetros do Sistema

#### Valores Base (Absolutos)
- **s_base**: Potência aparente base em VA - Define a escala do sistema
- **v_base**: Tensão base em V - Tensão nominal do sistema
- **f_base**: Frequência base em Hz - Frequência nominal (60 Hz no Brasil)

#### Parâmetros de Simulação
- **dt**: Passo de tempo em segundos (recomendado: 0.01s para estabilidade)
- **duration**: Duração total da simulação em segundos

#### Parâmetros da Rede
- **r_eq**: Resistência equivalente do barramento em pu
- **x_eq**: Reatância equivalente do barramento em pu

### 🏭 Configuração de Cargas

Cada barramento secundário (A, B, C, D) possui carga configurável:

| Barramento | Carga Padrão | Potência Ativa | Potência Reativa |
|------------|--------------|----------------|------------------|
| **A** | Leve | 0.075 pu (2.25 MW) | 0.0363 pu (1.089 MVAR) |
| **B** | Pesada | 0.3 pu (9.0 MW) | 0.03 pu (0.9 MVAR) |
| **C** | Pesada | 0.3 pu (9.0 MW) | 0.03 pu (0.9 MVAR) |
| **D** | Pesada | 0.3 pu (9.0 MW) | 0.03 pu (0.9 MVAR) |

### 🔌 Estado dos Disjuntores

Array de 8 valores booleanos representando o estado dos disjuntores:

| Posição | Disjuntor | Função |
|---------|-----------|--------|
| 0 | CB1 | Barramento Principal → Secundário A |
| 1 | CB2 | Barramento Principal → Secundário B |
| 2 | CB3 | Barramento Principal → Secundário C |
| 3 | CB4 | Barramento Principal → Secundário D |
| 4 | CB5 | Conexão Gerador A |
| 5 | CB6 | Conexão Gerador B |
| 6 | CB7 | Conexão Gerador C |
| 7 | CB8 | Conexão Gerador D |

- **true**: Disjuntor fechado (conectado)
- **false**: Disjuntor aberto (desconectado)

## 🔋 Configuração dos Geradores (`generators_config.json`)

O arquivo `generators_config.json` contém a configuração detalhada de cada gerador:

```json
{
  "metadata": {
    "version": "2.0",
    "last_updated": "2024-01-15T10:30:00Z",
    "description": "Generator configurations for power system simulation"
  },
  "generators": [
    {
      "name": "A",
      "mode": "synchronous",
      "P_setpoint": 0.6,
      "V_setpoint": 1.02,
      "H": 20.0,
      "damping_factor": 16.0,
      "K_pgov": 12.0,
      "K_igov": 2.0,
      "K_dgov": 0.8,
      "K_turb": 1.2,
      "T_turb": 0.15,
      "W_fnl": 0.15,
      "K_pavr": 12.0,
      "K_iavr": 1.5,
      "K_davr": 0.15,
      "T_avr": 0.04,
      "V_max": 1.1,
      "V_min": 0.9,
      "disturb_start": 0.0,
      "disturb_end": 0.0,
      "disturb_value": 0.0
    },
    // Configurações para geradores B, C e D...
  ]
}
```

### 🔧 Parâmetros dos Geradores

#### Parâmetros Básicos
- **name**: Nome do gerador (A, B, C ou D)
- **mode**: Modo de operação do gerador:
  - `synchronous`: Modo síncrono (isocrono) - mantém a frequência constante
  - `droop`: Modo droop - permite variação de frequência proporcional à carga
- **P_setpoint**: Setpoint de potência ativa em pu (0.0 a 1.0)
- **V_setpoint**: Setpoint de tensão em pu (0.9 a 1.1)

#### ⚡ Parâmetros de Inércia e Amortecimento
- **H**: Constante de inércia em segundos (5.0 a 30.0)

  | Tipo de Gerador | Potência | Valor H | Características |
  |-----------------|----------|---------|-----------------|
  | **Grande** | >100 MVA | 15.0-30.0s | Alta estabilidade, resposta lenta |
  | **Médio** | 10-100 MVA | 8.0-15.0s | Equilíbrio estabilidade/resposta |
  | **Pequeno** | <10 MVA | 5.0-8.0s | Resposta rápida, menor estabilidade |

  - **Valor padrão**: 20.0 segundos
  - **Efeito**: Valores maiores = maior estabilidade, menor velocidade de resposta

- **damping_factor**: Fator de amortecimento (0.0 a 30.0)

  | Modo do Gerador | Valor Recomendado | Efeito |
  |-----------------|-------------------|--------|
  | **Synchronous** | 12.0-20.0 (padrão: 16.0) | Reduz oscilações, maior estabilidade |
  | **Droop** | 4.0-10.0 (padrão: 6.0) | Permite resposta mais rápida |

  - **Efeito**: Valores maiores reduzem oscilações, mas podem tornar o sistema mais lento

#### 🎛️ Parâmetros do Governador (Controlador PID)

| Parâmetro | Faixa | Padrão | Função |
|-----------|-------|--------|--------|
| **K_pgov** | 5.0-20.0 | 12.0 | Ganho proporcional - resposta imediata a desvios de frequência |
| **K_igov** | 0.5-5.0 | 2.0 | Ganho integral - elimina erro de estado estacionário |
| **K_dgov** | 0.0-2.0 | 0.8 | Ganho derivativo - melhora resposta transitória |

#### 🔧 Parâmetros da Turbina

| Parâmetro | Faixa | Padrão | Função |
|-----------|-------|--------|--------|
| **K_turb** | 0.8-1.5 | 1.2 | Ganho da turbina - amplificação do sinal de controle |
| **T_turb** | 0.1-0.5s | 0.15s | Constante de tempo - atraso na resposta da turbina |
| **W_fnl** | 0.05-0.2 | 0.15 | Fluxo sem carga - consumo mínimo de combustível |

#### ⚡ Parâmetros do AVR (Regulador Automático de Tensão)

| Parâmetro | Faixa | Padrão | Função |
|-----------|-------|--------|--------|
| **K_pavr** | 5.0-20.0 | 12.0 | Ganho proporcional AVR - resposta a desvios de tensão |
| **K_iavr** | 0.5-3.0 | 1.5 | Ganho integral AVR - elimina erro de tensão |
| **K_davr** | 0.0-0.5 | 0.15 | Ganho derivativo AVR - estabiliza resposta |
| **T_avr** | 0.02-0.1s | 0.04s | Constante de tempo AVR - velocidade de resposta |
| **V_max** | 1.05-1.2 | 1.1 | Limite máximo de tensão (pu) |
| **V_min** | 0.8-0.95 | 0.9 | Limite mínimo de tensão (pu) |

#### 🧪 Parâmetros de Perturbação (para Testes)

| Parâmetro | Unidade | Padrão | Função |
|-----------|---------|--------|--------|
| **disturb_start** | segundos | 0.0 | Tempo de início da perturbação |
| **disturb_end** | segundos | 0.0 | Tempo de fim da perturbação |
| **disturb_value** | pu | 0.0 | Magnitude da perturbação de potência |

**Exemplo de perturbação:**
```json
"disturb_start": 5.0,    // Inicia aos 5 segundos
"disturb_end": 7.0,      // Termina aos 7 segundos
"disturb_value": -0.1    // Redução de 0.1 pu (3 MW) na potência
```

## 👤 Preferências do Usuário (`user_preferences.json`)

O arquivo `user_preferences.json` contém configurações personalizáveis da interface e comportamento:

```json
{
  "use_date_folders": true,
  "auto_cleanup_enabled": false,
  "auto_cleanup_days": 30,
  "max_storage_mb": 1000,
  "default_graph_type": "Potência",
  "legend_position": "below",
  "auto_save_enabled": false,
  "update_frequency": 50,
  "window_maximized": false,
  "window_width": 1200,
  "window_height": 800,
  "recent_simulations": []
}
```

### 💾 Preferências de Armazenamento

| Parâmetro | Tipo | Padrão | Função |
|-----------|------|--------|--------|
| **use_date_folders** | boolean | true | Organizar dados por pastas de data |
| **auto_cleanup_enabled** | boolean | false | ⚠️ Limpeza automática (DESABILITADA por segurança) |
| **auto_cleanup_days** | integer | 30 | Dias para manter dados (se auto-cleanup ativo) |
| **max_storage_mb** | integer | 1000 | Limite de armazenamento para avisos (MB) |

### 🎨 Preferências da Interface

| Parâmetro | Tipo | Padrão | Opções |
|-----------|------|--------|--------|
| **default_graph_type** | string | "Potência" | "Potência", "Frequência", "Tensão", "Corrente", "Balanço Potência" |
| **legend_position** | string | "below" | "below", "inside" |
| **auto_save_enabled** | boolean | false | Salvamento automático de simulações |
| **update_frequency** | integer | 50 | Frequência de atualização da interface (passos) |

### 🖥️ Preferências da Janela

| Parâmetro | Tipo | Padrão | Função |
|-----------|------|--------|--------|
| **window_maximized** | boolean | false | Iniciar janela maximizada |
| **window_width** | integer | 1200 | Largura da janela (pixels) |
| **window_height** | integer | 800 | Altura da janela (pixels) |
| **recent_simulations** | array | [] | Lista de simulações recentes |

### ⚠️ Importante: Limpeza Automática

A limpeza automática está **DESABILITADA por padrão** para evitar perda acidental de dados. Para habilitar:

1. Altere `auto_cleanup_enabled` para `true`
2. Configure `auto_cleanup_days` conforme necessário
3. **Use com extrema cautela** - dados serão deletados permanentemente!

## 💾 Gerenciamento de Dados de Simulação

O novo sistema organiza automaticamente os dados de simulação em uma estrutura hierárquica:

```
simulation_data/
├── index.json                    # Índice global de simulações
├── 2024-01-15/                   # Pasta por data
│   ├── simulation_2024-01-15_09-30-15_config.json
│   ├── simulation_2024-01-15_09-30-15_data.csv
│   ├── simulation_2024-01-15_09-30-15_summary.txt
│   └── daily_summary.txt         # Resumo do dia
├── 2024-01-16/
└── cleanup_log.txt               # Log de limpeza
```

### 📊 Formato dos Dados Salvos

#### 1. Arquivo de Configuração (`*_config.json`)
Contém **todos os parâmetros** necessários para reproduzir a simulação:
- Configuração do sistema (valores base, duração, etc.)
- Parâmetros de todos os geradores
- Estado dos disjuntores
- Condições iniciais
- Metadados (timestamp, descrição, etc.)

#### 2. Arquivo de Dados (`*_data.csv`)
Contém as **séries temporais** da simulação:
- Tempo
- Dados dos geradores (potência, frequência, tensão)
- Dados dos barramentos (potência, frequência, tensão)
- Balanço de potência

#### 3. Arquivo de Resumo (`*_summary.txt`)
Contém um **resumo legível** da simulação:
- Parâmetros principais
- Configuração dos geradores
- Análise básica dos resultados

### 🔧 Comandos de Gerenciamento

#### Testar Sistema de Configuração
```bash
python test_config.py
```

#### Verificar Armazenamento
```bash
python main.py --test-config
```

#### Limpeza Manual (Segura)
```bash
# Através da interface gráfica
# Menu: Ferramentas → Limpeza de Dados
```

## 📝 Exemplos Práticos de Configuração

### 🎯 Cenário 1: Teste de Perturbação no Gerador A

**Objetivo**: Simular uma redução súbita de 3 MW no Gerador A aos 5 segundos.

**Configuração em `generators_config.json`**:
```json
{
  "generators": [
    {
      "name": "A",
      "mode": "synchronous",
      "P_setpoint": 0.6,
      "V_setpoint": 1.02,
      "H": 20.0,
      "damping_factor": 16.0,
      "disturb_start": 5.0,     // ← Perturbação aos 5s
      "disturb_end": 7.0,       // ← Termina aos 7s
      "disturb_value": -0.1     // ← Redução de 3 MW (0.1 pu)
    }
    // Outros geradores...
  ]
}
```

### 🎯 Cenário 2: Sistema com Cargas Desbalanceadas

**Objetivo**: Simular cargas diferentes em cada barramento.

**Configuração em `system_config.json`**:
```json
{
  "loads": {
    "A": {"load_p": 0.05, "load_q": 0.02},   // Carga leve: 1.5 MW
    "B": {"load_p": 0.4, "load_q": 0.04},    // Carga pesada: 12 MW
    "C": {"load_p": 0.2, "load_q": 0.02},    // Carga média: 6 MW
    "D": {"load_p": 0.3, "load_q": 0.03}     // Carga normal: 9 MW
  }
}
```

### 🎯 Cenário 3: Configuração para Estudo de Estabilidade

**Objetivo**: Configurar sistema para análise de estabilidade com inércias diferentes.

**Configuração em `generators_config.json`**:
```json
{
  "generators": [
    {
      "name": "A",
      "mode": "synchronous",
      "H": 30.0,              // ← Alta inércia (estabilidade)
      "damping_factor": 20.0  // ← Alto amortecimento
    },
    {
      "name": "B",
      "mode": "droop",
      "H": 10.0,              // ← Baixa inércia (resposta rápida)
      "damping_factor": 4.0   // ← Baixo amortecimento
    }
    // Outros geradores...
  ]
}
```

### 🎯 Cenário 4: Preferências para Sala de Aula

**Objetivo**: Configurar interface para uso educacional.

**Configuração em `user_preferences.json`**:
```json
{
  "window_maximized": true,           // Tela cheia para projetor
  "default_graph_type": "Potência",   // Começar com conceito básico
  "legend_position": "below",         // Melhor para apresentação
  "auto_cleanup_enabled": false,      // Segurança para dados dos alunos
  "update_frequency": 100             // Atualização mais lenta
}
```

## ⚠️ Observações Importantes

### 🔧 Sistema de Configuração

1. **Migração Automática**: O sistema detecta automaticamente configurações antigas (`config.json`) e migra para o novo formato. O arquivo original é preservado como backup.

2. **Validação Robusta**: Todas as configurações são validadas automaticamente. Erros são reportados com mensagens claras para facilitar a correção.

3. **Arquivos Especializados**: Cada aspecto do sistema tem seu próprio arquivo de configuração, facilitando manutenção e organização.

### ⚡ Parâmetros Técnicos

4. **Valores em pu**: Cargas, impedâncias e setpoints devem ser especificados em pu (por unidade), considerando a potência base de 30 MVA.

5. **Valores Base**: `s_base`, `v_base` e `f_base` são especificados em unidades absolutas (VA, V, Hz) e definem a base para todos os cálculos.

6. **Gerador Síncrono**: Para estabilidade ótima, o gerador síncrono deve ter o maior setpoint de potência no sistema.

7. **Constante de Inércia (H)**:
   - **Alta (15-30s)**: Maior estabilidade, resposta mais lenta
   - **Baixa (5-15s)**: Resposta rápida, menor estabilidade
   - **Padrão (20s)**: Adequado para a maioria das simulações

8. **Fator de Amortecimento**:
   - **Síncrono**: 12-20 (padrão: 16) - reduz oscilações
   - **Droop**: 4-10 (padrão: 6) - permite resposta rápida

### 💾 Gerenciamento de Dados

9. **Organização por Data**: Dados são automaticamente organizados em pastas por data para facilitar localização e manutenção.

10. **Reprodutibilidade**: Todos os parâmetros necessários para reproduzir uma simulação são salvos automaticamente.

11. **Limpeza Automática**: **DESABILITADA por padrão** para evitar perda acidental de dados. Habilite apenas se necessário e com extrema cautela.

12. **Formatos Padrão**: JSON (configuração) + CSV (dados) + TXT (resumo) garantem compatibilidade máxima.

### 🎨 Interface e Usabilidade

13. **Preferências Personalizáveis**: Cada usuário pode configurar a interface conforme suas necessidades específicas.

14. **Seletor Unificado**: Um único seletor de tipo de gráfico simplifica a interface e melhora a experiência do usuário.

15. **Configuração Persistente**: Preferências são salvas automaticamente e restauradas na próxima execução.

### 📊 Cálculos de Referência

**Valores Base Derivados** (com s_base = 30 MVA, v_base = 13.8 kV):
- **Impedância base**: z_base = (13800)² / 30000000 = 6.348 Ω
- **Corrente base**: i_base = 30000000 / 13800 = 2173.9 A

**Conversões Típicas**:
- 1 pu de potência = 30 MW
- 1 pu de tensão = 13.8 kV
- 1 pu de corrente = 2173.9 A
